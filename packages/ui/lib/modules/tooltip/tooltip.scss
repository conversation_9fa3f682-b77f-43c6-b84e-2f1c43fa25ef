@use '../../styles/common/bem';
@use '../../styles/common/prop';
@use '../../styles/common/type-style' as *;
@use '../../styles/common/util';

$-radius: util.to-rem(8px);
$-arrow-size: util.to-rem(10px);
$-background: prop.get(color-zinc-900);

@mixin styles {
    @include bem.module('tooltip') {
        position: absolute;
        inline-size: max-content;
        max-inline-size: 30ch;
        inset: 0 auto auto 0;
        transform: translate(prop.get(tooltip-x, 0), prop.get(tooltip-y, 0));
        border-radius: $-radius;
        background-color: $-background;
        color: prop.get(color-zinc-300);
        padding: prop.get(size-xs-sm) prop.get(size-xs-m);
        filter: drop-shadow(0 0 1px prop.get(color-mt-400));
        text-wrap: balance;
        text-wrap: pretty;

        @include type-style('body', $with: (font-weight: normal, line-height: 1.2));

        &__arrow {
            position: absolute;
            inline-size: $-arrow-size;
            block-size: $-arrow-size;
            transform: translate(prop.get(tooltip-arrow-x, 0), prop.get(tooltip-arrow-y, 0));

            &::before {
                content: '';
                position: absolute;
                inset: 0;
                background-color: $-background;
                clip-path: polygon(100% 0%, 100% 100%, 20% 50%);
                transform: rotate(prop.get(tooltip-arrow-angle, 0));
            }
        }
    }
}
