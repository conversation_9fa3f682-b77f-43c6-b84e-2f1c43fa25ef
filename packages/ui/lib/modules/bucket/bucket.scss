@use '../../styles/common/bem';
@use '../../styles/common/prop';
@use '../../styles/common/util';
@use '../../styles/common/type-style' as *;
@use '../../styles/config/metrics';

@mixin styles {
    @include bem.module('bucket') {
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 0.5em;
        block-size: clamp(
            calc(1.25rem - ((prop.get(bucket-count, 9) - 9) * 0.05rem)),
            0.9651rem + 1.2733vw,
            calc(2.1875rem - ((prop.get(bucket-count, 9) - 9) * 0.05rem))
        );
        position: relative;
        border-bottom: 0.25em solid transparent;
        background-color: prop.get(bucket-color, #e41b1b);
        border-color: oklch(from prop.get(bucket-color) calc(l * 0.8) calc(c * 0.7) h);
        transform: translateY(0);
        min-inline-size: 0;
        cursor: default;

        &--standalone {
            padding: metrics.fluid-size(20, 30);
        }

        &__value {
            container: inline-size;
            font-size: clamp(0.5rem, 1.1cqi - ((prop.get(bucket-count, 9) - 9) * 0.05cqi), 1.25rem);
            font-weight: 600;
            line-height: 1;
            color: prop.get(bucket-text-color, prop.get(color-black));
            user-select: none;
            overflow: hidden;
            white-space: nowrap;

            &--standalone {
                font-size: metrics.fluid-size(20, 40);
            }
        }

        &--hit {
            animation: m-bucket-drop 0.25s ease;
        }

        &__row {
            display: grid;
            grid-template-columns: repeat(prop.get(bucket-count), 1fr);
            gap: calc(0.75em - ((prop.get(bucket-count, 9) - 9) * 0.05em));
        }

        @keyframes m-bucket-drop {
            0% {
                transform: translateY(0);
            }
            1% {
                transform: translateY(8%);
            }
            50% {
                transform: translateY(16%);
            }
            100% {
                transform: translateY(0);
            }
        }
    }
}
