import {
    Bar,
    defineComponent,
    forwardRef,
    Frame,
    Stack,
    Toast,
    html,
    withNamespace,
    type Component,
    type Props,
    type ToastBarControls,
} from '@monkey-tilt/ui';

import { type KenoUI } from '../../keno';
import { Balance } from '../balance/balance';
import { Loader } from '../loader/loader';

import { Board } from '../board/board';
import { Multipliers } from '../multipliers/multipliers';
import { formatCurrency } from '@monkey-tilt/client';

export interface TableProps extends Props<HTMLDivElement> {
    /**
     * The Keno UI state manager.
     */
    readonly ui: KenoUI;
}

export const Table = defineComponent(
    'Table',
    ({ state }) =>
        ({ ui, ...props }: TableProps): Component<HTMLDivElement> => {
            const { signal, memo, effect, read } = state();

            const [isLoading, setIsLoading] = signal(ui.gameState().isLoading);
            effect(() => {
                let timer: ReturnType<typeof setTimeout> | undefined;
                const newIsLoading = ui.gameState().isLoading;

                if (newIsLoading) {
                    timer = setTimeout(() => {
                        setIsLoading(true);
                    }, 1500);
                } else {
                    setIsLoading(false);
                }

                return () => {
                    if (timer) {
                        clearTimeout(timer);
                    }
                };
            });

            const multipliersWithPayout = memo(() =>
                ui.multipliers().map(([multiplier, chance]) => ({
                    multiplier,
                    chance: (parseFloat(chance) * 100).toFixed(8).replace(/\.?0+$/, ''),
                    payout: formatCurrency(
                        parseFloat(multiplier) * parseFloat(ui.betAmount.read()),
                        ui.currency(),
                    ),
                })),
            );

            return Frame(
                {
                    ...props,
                    ref: forwardRef(props.ref, (element: HTMLDivElement) => {
                        effect(() => {
                            element.classList.toggle(withNamespace('is-loading'), isLoading());
                        });
                    }),
                    className: { 'm-keno__table': true },
                },

                Stack(
                    { center: true, className: { 'm-keno__main': true }, gap: 'm' },
                    Board({ ui }),
                ),

                html('div')(
                    {
                        className: withNamespace('m-keno__payout-section'),
                    },
                    Stack(
                        { gap: 's' },
                        Multipliers({ items: ui.multipliers }),
                        Bar({
                            multipliers: multipliersWithPayout,
                        }),
                    ),
                ),
                Loader({
                    isLoading,
                    text: [
                        { text: 'Loading, please wait...', delay: 1000 },
                        { text: 'This is taking longer than expected...', delay: 5000 },
                        { text: 'Please check your internet connection!', delay: 8000 },
                    ],
                    ref: (element: HTMLDivElement) => {
                        effect(() => {
                            element.classList.toggle(withNamespace('u-hide'), !isLoading());
                        });
                    },
                }),
                Balance({ ui }),
                Toast.Bar({
                    position: 'absolute',
                    controls: (toastControls: ToastBarControls) => {
                        ui.toastBarControlsRef.current = toastControls;
                    },
                }),
                Toast.Bar({
                    position: 'absolute',
                    align: 'center',
                    css: {
                        bottom: '20%',
                    },
                    controls: (toastControls: ToastBarControls) => {
                        ui.notificationsControlsRef.current = toastControls;
                    },
                }),
            );
        },
);
