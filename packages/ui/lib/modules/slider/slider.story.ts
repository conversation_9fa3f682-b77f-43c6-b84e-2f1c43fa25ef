import type { Meta, StoryObj } from '@storybook/html';
import { fn } from '@storybook/test';
import { render } from '../../../.storybook/render';
import { html } from '../../component';
import { Slider, type SliderProps } from './slider';

const meta: Meta = {
    title: 'Modules/Slider',
    globals: { backgrounds: { value: 'dark' } },
};
export default meta;

export const Default: StoryObj<SliderProps> = {
    render: (props) => {
        return render(({ onStoryUnmount, store: { signal } }) => {
            const [rollOver, setRollOver] = signal((props.rollOver as boolean) ?? false);

            const interval = setInterval(() => {
                setRollOver((prev) => !prev);
            }, 3000);

            onStoryUnmount(() => {
                clearInterval(interval);
            });

            return html('div')(
                {
                    css: {
                        display: 'flex',
                        'flex-direction': 'column',
                        'align-items': 'center',
                        'justify-content': 'center',
                        padding: '4rem',
                    },
                },
                Slider({ ...props, rollOver }),
            );
        });
    },
    args: {
        value: 49.5,
        min: 2,
        max: 98,
        labelFrom: 0,
        labelTo: 100,
        totalLabels: 5,
        step: 0.1,
        rollOver: true,
        onChange: fn(),
    },
    argTypes: {
        rollOver: { control: 'boolean' },
        value: { control: 'number' },
        min: { control: 'number' },
        max: { control: 'number' },
        step: { control: 'number' },
        labelFrom: { control: 'number' },
        labelTo: { control: 'number' },
        totalLabels: { control: 'number' },
    },
};

export const Plinko: StoryObj<SliderProps> = {
    render: (props) =>
        render(({ store: { signal } }) => {
            const [value, setValue] = signal(props.value as number);

            return html('div')(
                {
                    css: {
                        'display': 'flex',
                        'flex-direction': 'column',
                        'align-items': 'center',
                        'justify-content': 'center',
                        'padding': '4rem',
                        'gap': '1rem',
                    },
                },
                html('div')({
                    css: {
                        'display': 'flex',
                        'align-items': 'center',
                        'justify-content': 'space-between',
                        'width': '100%',
                        'max-width': '300px',
                        'font-size': 'clamp(1rem, 1.2vw, 1.2rem)',
                        'color': 'white',
                    },
                    children: [
                        html('span')({ textContent: '08' }),
                        Slider({ ...props, value, onChange: setValue }),
                        html('span')({ textContent: '16' }),
                    ],
                }),
            );
        }),
    name: 'Plinko',
    args: {
        value: 8,
        min: 8,
        max: 16,
        step: 1,
        rollOver: false,
        variant: 'plinko',
        totalLabels: 0,
        onChange: fn(),
    },
    argTypes: {
        variant: { table: { disable: true } },
        rollOver: { table: { disable: true } },
        labelFrom: { table: { disable: true } },
        labelTo: { table: { disable: true } },
        totalLabels: { table: { disable: true } },
    },
};