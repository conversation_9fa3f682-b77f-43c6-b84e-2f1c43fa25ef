import { getCurrencySymbol, precisionForCurrency } from '../../../utils';
import { NumberInput, type NumberInputProps } from '../number-input/number-input';
import {
    defineComponent,
    Icon,
    type Component,
    type Reactive,
    type SignalReader,
} from '@monkey-tilt/ui';

export interface BetAmountProps extends NumberInputProps {
    /**
     * The minimum and maximum bet amounts allowed. Defaults to {min: 0.001, max: 1_000}.
     */
    readonly limits?: SignalReader<{ min: number; max: number }>;

    /**
     * The currency to be displayed. Defaults to 'USD'.
     */
    readonly currency?: Reactive<string>;
}

export const BetAmount = defineComponent(
    'BetAmount',
    ({ state }) =>
        ({
            currency = 'USD',
            limits = () => ({ min: 0.001, max: 1_000 }),
            showStepper = true,
            step = 0.1,
            multipliers = [
                { label: '1/2', factor: 0.5 },
                { label: '2x', factor: 2 },
            ],
            ...props
        }: BetAmountProps): Component<HTMLDivElement> => {
            const { memo, read } = state();

            const transformCurrency = <T>(transform: (value: string) => T) =>
                typeof currency === 'string'
                    ? transform(currency)
                    : memo(() => transform(read(currency)));

            return NumberInput({
                fractionDigits: transformCurrency(precisionForCurrency),
                limits,
                prefix: Icon({
                    cssProps: {
                        'icon-size': { var: 'size-l' },
                    },
                    css: {
                        'font-weight': 'normal',
                        'font-size': '0.85em',
                        'margin-right': '0.25em',
                    },
                    currency: transformCurrency(getCurrencySymbol),
                }),
                showStepper,
                step,
                multipliers,
                ...props,
            });
        },
);
