const dpi = {
    current: window.devicePixelRatio || 1,
    onChange(callback: (currentDPI: number) => void): () => void {
        const abortController = new AbortController();

        matchMedia(`(resolution: ${dpi.current}dppx)`).addEventListener(
            'change',
            () => {
                dpi.current = window.devicePixelRatio || 1;

                callback(dpi.current);
                dpi.onChange(callback);
            },
            { once: true, signal: abortController.signal },
        );

        return () => {
            abortController.abort();
        };
    },
};

export const DPI = dpi as {
    /**
     * The current device pixel ratio (DPI).
     */
    readonly current: number;
    /**
     * Add a listener for DPI changes.
     *
     * @param callback - The callback to invoke when the DPI changes.
     * @returns A function to remove the listener.
     */
    onChange: (callback: (currentDPI: number) => void) => () => void;
};
