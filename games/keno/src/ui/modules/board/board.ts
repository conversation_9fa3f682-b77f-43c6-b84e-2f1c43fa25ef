import { defineComponent, html, Tile, withNamespace, type Component } from '@monkey-tilt/ui';
import { KenoUI } from '../../keno';

export interface BoardProps {
    readonly ui: KenoUI;
}

export const Board = defineComponent(
    'Board',
    ({ state }) =>
        ({ ui, ...props }: BoardProps): Component<HTMLDivElement> => {
            const { memo, effect, read } = state();

            const wrapper = html('div')({
                className: withNamespace('m-tile-board'),
            }).render();

            const drawnNumbers = memo(() => {
                return ui.gameState().drawn_numbers || [];
            });

            for (let i = 1; i <= 40; i++) {
                const isSelected = memo(() => ui.chosen_numbers.read().includes(i));
                const isHit = memo(() => drawnNumbers().includes(i) && isSelected());
                const isMiss = memo(() => drawnNumbers().includes(i) && !isSelected());
                const disabled = memo(
                    () =>
                        ui.isBettingLocked() ||
                        ui.pickingInProgress.read() ||
                        (ui.chosen_numbers.read().length == 10 && !isSelected()),
                );

                effect(() => {
                    if (read(isHit)) {
                        void ui.playSound('win');
                    }
                    if (read(isMiss)) {
                        void ui.playSound('bet');
                    }
                });

                const tile = Tile({
                    ...props,
                    number: i,
                    isSelected,
                    isHit,
                    isMiss,
                    disabled,
                    onClick: () => ui.triggerAction('ToggleNumber', i),
                }).render();
                wrapper.appendChild(tile);
            }

            return html('div')({}, wrapper);
        },
);
