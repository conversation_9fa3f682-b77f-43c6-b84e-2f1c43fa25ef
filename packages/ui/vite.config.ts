import { resolve } from 'node:path';
import * as sass from 'sass-embedded';
import { defineConfig, loadEnv } from 'vite';
import dts from 'vite-plugin-dts';
import { ViteImageOptimizer } from 'vite-plugin-image-optimizer';
import { config } from './lib/config';
import { cssFluidClamp } from './lib/utilities/clamp';
import packageJson from './package.json' with { type: 'json' };

const globals = Object.keys(packageJson.peerDependencies ?? {})
    .filter((key) => key.startsWith('@monkey-tilt/'))
    .reduce(
        (acc, key) => {
            acc[key] = 'MT_' + key.split('/').pop()!.replace(/-/g, '_');
            return acc;
        },
        {} as Record<string, string>,
    );

export const scss = {
    api: 'modern-compiler',
    functions: {
        ///
        /// Get a configuration value by name.
        ///
        /// @param $name - The name of the configuration value.
        /// @param $default - The default value to return if the configuration value is not found.
        /// @return The configuration value or the default value if the configuration value is not found.
        ///
        'lib-config($name, $default: null)': ((args: sass.Value[]) => {
            if (args.length < 1) {
                throw new Error('Missing required argument $name');
            }
            const name = args[0]!.assertString('name').text;
            if (name in config) {
                return new sass.SassString(config[name as keyof typeof config]);
            }
            return args[1];
        }) as sass.CustomFunction<'async'>,

        ///
        /// Create a fluid clamp() calculation.
        ///
        /// @param $min - The minimum value.
        /// @param $max - The maximum value.
        /// @param $minSize - The minimum relative container size.
        /// @param $maxSize - The maximum relative container size.
        /// @param $unit - The unit of the resulting clamped value. Defaults to `rem`.
        /// @param $relativeUnit - The unit of the relative responsive container. Defaults to `vw`.
        /// @return The resulting clamp() calculation.
        ///
        'fluid-clamp($min, $max, $minSize, $maxSize, $unit: "rem", $relativeUnit: "vw")': ((
            args: sass.Value[],
        ) => {
            if (args.length < 4) {
                throw new Error('Insufficient number of arguments');
            }

            const assertEnum = <const T extends string>(value: sass.SassString, values: T[]): T => {
                if (values.includes(value.text as T)) {
                    return value.text as T;
                }
                throw new Error(`Expected one of: ${values.join(', ')}, but got: ${value.text}`);
            };

            const clamp = cssFluidClamp({
                min: args[0]!.assertNumber('min').assertNoUnits('min').value,
                max: args[1]!.assertNumber('max').assertNoUnits('max').value,
                minSize: args[2]!.assertNumber('minSize').assertNoUnits('minSize').value,
                maxSize: args[3]!.assertNumber('maxSize').assertNoUnits('maxSize').value,
                unit: assertEnum(args[4]?.assertString('unit') ?? new sass.SassString('rem'), [
                    'px',
                    'rem',
                ]),
                relativeUnit: assertEnum(
                    args[5]?.assertString('relativeUnit') ?? new sass.SassString('vw'),
                    ['vi', 'vw', 'cqi'],
                ),
            });

            return sass.SassCalculation.clamp(
                new sass.SassNumber(...clamp.min),
                new sass.CalculationOperation(
                    '+',
                    new sass.SassNumber(...clamp.base),
                    new sass.SassNumber(...clamp.scalingRate),
                ),
                new sass.SassNumber(...clamp.max),
            );
        }) as sass.CustomFunction<'async'>,
    },
} as const;

export default defineConfig(({ mode }) => {
    const env = loadEnv(mode, process.cwd(), 'MT_');

    return {
        envPrefix: ['MT_', 'VITE_'],
        define: ['MT_UI_CSS_NAMESPACE'].reduce(
            (acc, key) => {
                acc[`process.env.${key}`] = JSON.stringify(env[key] || null);
                return acc;
            },
            {} as Record<string, string>,
        ),
        plugins: [dts({ rollupTypes: true }), ViteImageOptimizer({ logStats: false })],
        build: {
            target: 'esnext',
            sourcemap: true,
            assetsInlineLimit: 512,
            lib: {
                entry: resolve(__dirname, 'lib/index.ts'),
                name: 'MT_ui',
                fileName: (format) => `ui.${format}.js`,
            },
            rollupOptions: {
                external: Object.keys(globals),
                output: {
                    globals,
                },
            },
        },
        experimental: {
            renderBuiltUrl: (filename, { type }) =>
                type === 'asset' ? './' + filename : undefined,
        },
        css: {
            devSourcemap: false,
            preprocessorOptions: {
                scss,
            },
        },
    };
});
