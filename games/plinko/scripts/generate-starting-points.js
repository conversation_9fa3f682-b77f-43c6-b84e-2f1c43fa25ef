import { promises as fs } from 'node:fs';
import { gzipSync } from 'node:zlib';

import { Board } from '../build/physics/board.js';
import { Ball } from '../build/physics/bodies/ball.js';
import { Peg } from '../build/physics/bodies/peg.js';

const FPS = 30;

class TestBall extends Ball {
    constructor(x, y, i, j) {
        super(x, y);

        this.start = [x, y];
        this.iters = [i, j];
        this.ticks = 0;
        this.wallHits = 0;
        this.pegHits = 0;
    }

    get elapsedTime() {
        return this.ticks / FPS;
    }

    update(dt) {
        super.update(dt);
        this.ticks++;
    }

    checkForCollision(collidable) {
        const collision = super.checkForCollision(collidable);
        if (collision) {
            if (collidable instanceof Peg) {
                this.pegHits++;
            } else {
                this.wallHits++;
            }
        }
        return collision;
    }
}

async function generate(numRows) {
    const board = new Board({ rows: numRows });

    const WIDTH_SAMPLES_FACTOR = 70;
    const HEIGHT_SAMPLES_FACTOR = 20;

    const dx = board.startRect.w / (numRows * WIDTH_SAMPLES_FACTOR);
    const dy = board.startRect.h / ((numRows / 2) * HEIGHT_SAMPLES_FACTOR);

    const xSamples = Math.ceil(board.startRect.w / dx);
    const ySamples = Math.ceil(board.startRect.h / dy);

    for (let i = 0; i < xSamples; i++) {
        for (let j = 0; j < ySamples; j++) {
            board.addBall(
                new TestBall(i * dx + board.startRect.x, j * dy + board.startRect.y, i, j),
            );
        }
    }

    const results = Array.from({ length: board.rows + 1 }, () => []);

    board.addEventListener('hit', (e) => {
        const { ball } = e.detail;
        results[ball.bucketNumber - 1].push(ball);
    });

    const MAX_TICKS = FPS * (numRows / 2 + 1);

    let ticks = 0;
    while (board.balls.length > 0 && ticks < MAX_TICKS) {
        ticks++;
        board.update(1 / FPS);
        await Promise.resolve();
    }

    const mid = numRows / 2;

    for (let i = 0; i < results.length; i++) {
        results[i] = sample(results[i], (mid - Math.abs(mid - i)) * 10 + 20).map(
            ({ iters }) => iters,
        );
    }

    return [[numRows, WIDTH_SAMPLES_FACTOR, HEIGHT_SAMPLES_FACTOR], ...results];
}

function shuffle(array) {
    for (let i = array.length - 1; i > 0; i--) {
        const j = Math.floor(Math.random() * (i + 1));
        [array[i], array[j]] = [array[j], array[i]];
    }
    return array;
}

function sample(balls, n) {
    const cleanBalls = balls.filter((ball) => ball.wallHits === 0);
    if (cleanBalls.length <= n) {
        return cleanBalls.concat(
            shuffle(balls.filter((ball) => ball.wallHits > 0)).slice(0, n - cleanBalls.length),
        );
    }
    return shuffle(
        cleanBalls
            .sort((a, b) => b.elapsedTime - a.elapsedTime)
            .sort((a, b) => b.pegHits - a.pegHits)
            .slice(0, Math.min(cleanBalls.length, n * 1.5)),
    ).slice(0, n);
}

function formatSize(bytes) {
    const sizes = ['B', 'KB', 'MB', 'GB'];
    if (bytes === 0) {
        return '0 B';
    }
    const i = parseInt(Math.floor(Math.log(bytes) / Math.log(1024)));
    return `${Math.round(bytes / Math.pow(1024, i), 2)} ${sizes[i]}`;
}

for (let i = 8; i <= 16; i++) {
    console.log(`Generating starting positions for a board with ${i} rows...`);
    const result = JSON.stringify(await generate(i));

    await fs.writeFile(`./src/data/${i}.json`, result);

    const gzipSize = gzipSync(result, { level: 9 }).length;

    console.log(
        `Wrote ./src/data/${i}.json [${formatSize(result.length)}, gzipped: ${formatSize(gzipSize)}]\n`,
    );
}
