<!doctype html>
<html lang="en">
    <head>
        <meta charset="UTF-8" />
        <meta name="viewport" content="width=device-width, initial-scale=1.0" />
        <title>Monkey Tilt Keno</title>
        <style>
            html,
            body {
                margin: 0;
                height: 100%;
            }
        </style>
    </head>

    <body>
        <div id="app"></div>
        <script type="module">
            import '@monkey-tilt/ui/style.scss';
            import { WebSocketTransport } from '@monkey-tilt/client';
            import { run } from './src/main.ts';

            let context;

            globalThis.runGame = () => {
                if (context) {
                    console.log('Unmounting previous game');
                    context.unmount();
                }

                run({
                    container: '#app',
                    gameId: 'keno-005',
                    gatewayUrl: `${window.location.origin}/-/og-dev/v1`,
                    currency: new URL(window.location.href).searchParams.get('currency') ?? 'USD',
                    gameSessionId:
                        new URL(window.location.href).searchParams.get('game_session') ??
                        'e938d0fd-eb17-4d9e-82f7-4400d394efdc',
                }).then((gameContext) => {
                    console.log('Game mounted', gameContext);
                    context = gameContext;
                });
            };

            runGame();
        </script>
    </body>
</html>
