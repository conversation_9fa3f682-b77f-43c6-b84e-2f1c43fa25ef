import {
    defineComponent,
    forwardRef,
    html,
    type Component,
    type Props,
    type Ref,
    type Renderable,
} from '../../component';
import type { Reactive } from '../../state';
import { mergeClasses, withNamespace } from '../../utilities/classes';
import { RadioGroup, type RadioGroupItem, type RadioGroupProps } from './group';

export type { RadioGroupItem, RadioGroupProps };

export interface RadioProps extends Props<HTMLInputElement> {
    /**
     * Label to display next to the radio button.
     */
    readonly label?: Renderable;

    /**
     * Whether the radio is checked.
     */
    readonly isChecked?: Reactive<boolean>;

    /**
     * The ref to pass to the input element.
     */
    readonly inputRef?: Ref<HTMLInputElement>;
}

const radio = defineComponent(
    'Radio',
    ({ state }) =>
        ({
            inputRef,
            label,
            isChecked = false,
            ...props
        }: RadioProps): Component<HTMLDivElement> => {
            const { onPropChange } = state();

            const id = props.id || `radio-${Math.random().toString(36).slice(2)}`;
            return html('div')(
                {
                    className: mergeClasses(props.className, withNamespace('m-radio')),
                },
                html('input')({
                    ...props,
                    id,
                    type: 'radio',
                    className: withNamespace('m-radio__input'),
                    ref: forwardRef(inputRef, (element: HTMLInputElement) => {
                        onPropChange(isChecked, (isChecked) => {
                            element.checked = isChecked;
                        });
                    }),
                }),
                label &&
                    html('label')(
                        { htmlFor: id, className: withNamespace('m-radio__label') },
                        label,
                    ),
            );
        },
);

export const Radio: typeof radio & {
    readonly Group: typeof RadioGroup;
} = Object.assign(radio, {
    Group: RadioGroup,
});
