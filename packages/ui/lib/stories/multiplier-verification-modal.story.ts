import { Button, Frame, Icon, Input, Label, Stack, Table, Tooltip } from '@monkey-tilt/ui';
import type { <PERSON>a, StoryObj } from '@storybook/html';
import { html } from '../component';
import { Flex } from '../layouts';

const meta: Meta = {
    title: 'Stories/Multiplier Verification Modal',
};
export default meta;

export const MultiplierVerificationModal: StoryObj = {
    render: () =>
        Frame(
            {
                className: {
                    'm-multiplier-verification__modal': true,
                    'm-frame': true,
                    'm-frame__section': true,
                    'm-frame--subtle': true,
                    'l-stack': true,
                    'l-cover': true,
                    'l-box': true,
                    'u-gap--2xl': true,
                },
                css: { 'max-inline-size': '600px', padding: '28px' },
            },

            // Title section + close button (X)
            html('div')(
                {
                    className: { 'l-flex': true, 'u-gap--s': true },
                    css: {
                        'align-items': 'center',
                        'justify-content': 'space-between',
                        'flex-wrap': 'nowrap',
                    },
                },
                Label({
                    variant: 'big',
                    text: 'Bets',
                }),
                Button({
                    css: {
                        border: 'none',
                        background: 'transparent',
                        padding: '0',
                        cursor: 'pointer',
                        display: 'flex',
                        'align-items': 'center',
                    },
                    children: [
                        Icon({
                            name: 'close',
                            css: {
                                transform: 'scale(1.5)',
                            },
                        }),
                    ],
                    onClick: () => console.log('Modal closed'),
                }),
            ),

            // Placed by & date section
            Label(
                {
                    text: 'Placed by user on date',
                    variant: 'light',
                    css: { display: 'flex', 'justify-content': 'space-between' },
                },
                html('span')(
                    {
                        className: { 'm-label--light': true },
                    },
                    'Placed by E***j',
                ),
                html('span')(
                    {
                        className: { 'm-label--light': true },
                    },
                    '2024-09-23 15:38',
                ),
            ),

            // Game information & copyable hash
            Stack(
                { gap: 'm' },
                Label({
                    variant: 'medium',
                    text: 'Limbo',
                    css: { display: 'flex', 'justify-content': 'center', 'align-items': 'center' },
                }),
                html('div')({
                    id: 'm-copyable-hash-wrapper',
                    css: { display: 'flex', 'align-items': 'center', 'justify-content': 'center' },
                    children: [
                        html('span')({
                            className: { 'm-label--light': true },
                            textContent: '6fa2ff6474812773330cbf57c34836da6cdb29b0df92cc8',
                            css: {
                                overflow: 'hidden',
                                'white-space': 'nowrap',
                                'text-overflow': 'ellipsis',
                                color: '#fff',
                            },
                        }),
                        Tooltip(
                            {
                                target: '#m-game-hash-tooltip',
                                showOn: 'hover',
                            },
                            'Copy the game hash for validation.',
                        ),
                        Button({
                            id: 'm-game-hash-tooltip',
                            icon: 'copy',
                            css: {
                                border: 'none',
                                background: 'transparent',
                                padding: '0',
                                'margin-left': '8px',
                                cursor: 'pointer',
                            },
                            onClick: () =>
                                navigator.clipboard.writeText(
                                    '6fa2ff6474812773330cbf57c34836da6cdb29b0df92cc8',
                                ),
                        }),
                    ],
                }),
            ),

            // Bet, multiplier, payout table
            Table({
                rows: [
                    { label: 'Bet', value: '0.20000000' },
                    { label: 'Multiplier', value: '1.2x' },
                    { label: 'Payout', value: '0.20000000' },
                ],
                className: 'm-bet-summary-table',
            }),

            // Target + result labels using LabelGroup
            Flex(
                { gap: 'm' },
                Label.Group({
                    text: Label({ variant: 'subtle' }, 'Target'),
                    value: Label({ variant: 'light' }, '0.20000000x'),
                }),
                Label.Group({
                    text: Label({ variant: 'subtle' }, 'Result'),
                    value: Label({ variant: 'highlight' }, '0.1'),
                }),
            ),

            // Hash round & random seed inputs with copy
            Stack(
                { gap: 'xl' },
                Stack(
                    { gap: 'm' },
                    Label({ variant: 'medium', text: 'Hash round' }),
                    Input({
                        value: '6fa2ff6474812773330cbf57c34836da6cdb29b0df92cc8',
                        readOnly: true,
                        css: { 'font-weight': 'normal' },
                        action: Button({
                            className: { 'm-stepper': true },
                            children: [Icon({ name: 'copy', description: 'Copy to clipboard' })],
                            onClick: () =>
                                navigator.clipboard.writeText(
                                    '6fa2ff6474812773330cbf57c34836da6cdb29b0df92cc8',
                                ),
                        }),
                    }),
                ),
                Stack(
                    { gap: 'm' },
                    Label({ variant: 'medium', text: 'Random Seed' }),
                    Input({
                        value: '4.34_igf722PRhc286eYSOiA5dpUIW',
                        readOnly: true,
                        css: { 'font-weight': 'normal' },
                        action: Button({
                            className: { 'm-stepper': true },
                            children: [Icon({ name: 'copy', description: 'Copy to clipboard' })],
                            onClick: () =>
                                navigator.clipboard.writeText('4.34_igf722PRhc286eYSOiA5dpUIW'),
                        }),
                    }),
                ),
            ),
        ).render(),
};
