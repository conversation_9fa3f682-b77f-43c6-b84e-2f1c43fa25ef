import { <PERSON><PERSON><PERSON><PERSON>boardController, KeyboardController } from '@monkey-tilt/ui';
import type { UIAction } from '../actions';

export const keyboardShortcuts: KeyboardController<UIAction> = new DOMKeyboardController<UIAction>({
    keyMap: new Map([
        ['Space', { action: 'BetOrStop', description: 'Make a bet' }],
        ['W', { action: 'MultiplyBetAmount', data: 2, description: 'Double bet amount' }],
        ['S', { action: 'MultiplyBetAmount', data: 0.5, description: 'Halve bet amount' }],
        ['Q', { action: 'SetBetAmount', data: 0, description: 'Zero bet amount' }],
        [
            'A',
            {
                action: 'DecreaseMultiplier',
                canRepeat: true,
                description: 'Decrease target multiplier',
            },
        ],
        [
            'D',
            {
                action: 'IncreaseMultiplier',
                canRepeat: true,
                description: 'Increase target multiplier',
            },
        ],
    ]),
}); 