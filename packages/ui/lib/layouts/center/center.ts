import { defineComponent, html, type Component, type Props } from '../../component';
import { mergeClasses, type CSSPropValue } from '../../utilities';
import type { Size } from '../types';

export interface CenterProps extends Props<HTMLDivElement> {
    readonly gap?: Size | undefined;
    readonly maxSize?: CSSPropValue | undefined;
    readonly intrinsic?: boolean | undefined;
    readonly centerText?: boolean | undefined;
}

export const Center = defineComponent(
    'Center',
    () =>
        ({
            gap,
            maxSize,
            intrinsic = false,
            centerText = false,
            ...props
        }: CenterProps): Component<HTMLDivElement> =>
            html('div')({
                ...props,
                cssProps: {
                    'max-size': maxSize,
                    'center-gap': gap && { var: `size-${gap}` },
                    ...(props.cssProps ?? {}),
                },
                className: mergeClasses(props.className, {
                    'l-center': true,
                    'l-center--intrinsic': intrinsic,
                    'l-center--text': centerText,
                }),
            }),
);
