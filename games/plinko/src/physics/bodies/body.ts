export interface Collidable {
    readonly x: number;
    readonly y: number;
    readonly radius: number;
}

export interface CollisionInfo {
    readonly deltaX: number;
    readonly deltaY: number;
    readonly distance: number;
    readonly overlap: number;
}

export abstract class Body implements Collidable {
    public abstract get x(): number;
    public abstract get y(): number;
    public abstract get radius(): number;
    public abstract get isAlive(): boolean;
    public abstract checkForCollision(body: Collidable): CollisionInfo | null;
}

export abstract class DynamicBody extends Body {
    public abstract update(dt: number): void;
    protected abstract handleCollision(info: CollisionInfo): void;

    public checkForCollision(body: Collidable): CollisionInfo | null {
        const deltaX = this.x - body.x;
        const deltaY = this.y - body.y;
        const distance = Math.hypot(deltaX, deltaY);

        const minimumSeparationDistance = this.radius + body.radius;

        if (distance <= minimumSeparationDistance) {
            const info = {
                deltaX,
                deltaY,
                distance,
                overlap: minimumSeparationDistance - distance,
            };

            this.handleCollision(info);

            return info;
        }

        return null;
    }
}

export abstract class StaticBody extends Body {
    #x: number;
    #y: number;
    #radius: number;

    public constructor(x: number, y: number, radius = 0) {
        super();
        this.#x = x;
        this.#y = y;
        this.#radius = radius;
    }

    public get x(): number {
        return this.#x;
    }

    public get y(): number {
        return this.#y;
    }

    public get radius(): number {
        return this.#radius;
    }

    public get isAlive(): boolean {
        return true;
    }

    public checkForCollision(body: Collidable): CollisionInfo | null {
        if (body instanceof Body) {
            return body.checkForCollision(this);
        }
        return null;
    }
}
