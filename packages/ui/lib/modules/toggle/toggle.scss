@use '../../styles/common/bem';
@use '../../styles/common/prop';
@use '../../styles/common/util';

@mixin styles {
    @include bem.module('toggle') {
        $border: 1px;
        $padding: 4px;
        $block-size: 24px;
        $thumb-size: $block-size - 2 * ($padding + $border);
        $inline-size: ($thumb-size + $padding + $border) * 2.5;
        $transition: 200ms ease-in-out;

        display: inline-block;

        &__thumb {
            border-radius: 999em;
            display: block;
            background: prop.get(toggle-thumb-color, prop.get(toggle-color, prop.get(color-white)));
            block-size: $thumb-size;
            aspect-ratio: 1;
            transform: translateX(prop.get(toggle-thumb-pos, 0));
            transition: $transition;
            transition-property: transform, background-color;
        }

        > input:focus-visible + label {
            outline: util.to-rem($border) solid prop.get(color-mt-400);
        }

        > input {
            &[disabled] + label {
                cursor: not-allowed;

                @include prop.set(
                    (
                        toggle-color: prop.get(color-zinc-800),
                        toggle-background: prop.get(color-zinc-900),
                        toggle-thumb-color: prop.get(color-zinc-700),
                    )
                );
            }

            &:checked + label {
                @include prop.set(
                    (
                        toggle-color: prop.get(color-mt-400),
                        toggle-background: prop.get(color-mt-950),
                    )
                );

                #{bem.namespaced-selector('m-toggle__thumb')} {
                    @include prop.set(
                        toggle-thumb-pos,
                        calc($inline-size - ($padding + $border) * 2 - $thumb-size)
                    );
                }

                &:hover {
                    @include prop.set(
                        (
                            toggle-color: prop.get(color-white),
                            toggle-background: prop.get(color-mt-950),
                            toggle-thumb-color: prop.get(color-mt-400),
                        )
                    );
                }
            }

            &[disabled]:checked + label {
                @include prop.set(
                    (
                        toggle-color: prop.get(color-zinc-800),
                        toggle-background: prop.get(color-zinc-900),
                        toggle-thumb-color: prop.get(color-mt-900),
                    )
                );
            }
        }

        > label {
            display: block;
            padding: util.to-rem($padding);
            block-size: util.to-rem($block-size);
            inline-size: util.to-rem($inline-size);
            background: prop.get(toggle-background, #383838);
            border: util.to-rem($border) solid prop.get(toggle-color, prop.get(color-white));
            border-radius: 999em;
            cursor: pointer;

            transition: $transition;
            transition-property: border-color, background-color;

            &:hover {
                @include prop.set(
                    (
                        toggle-color: prop.get(color-mt-400),
                        toggle-background: prop.get(color-black),
                        toggle-thumb-color: prop.get(color-white),
                    )
                );
            }
        }
    }
}
