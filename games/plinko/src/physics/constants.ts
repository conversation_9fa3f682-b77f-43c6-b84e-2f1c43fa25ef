/**
 * Gravitational acceleration, in px/s*s
 */
export const GRAVITY = 981;

/**
 * Coefficient of restitution (i.e. bounciness), between 0 and 1.
 */
export const BOUNCE = 0.5;

/**
 * Coefficient of friction (i.e. air resistance), between 0 and 1.
 */
export const FRICTION = 0.1;

/**
 * The size of the peg board "cell", in px.
 * 
 * A peg is placed in the center of a cell.
 */
export const CELL_SIZE = 100;

/**
 * The radius of a peg, in px.
 */
export const PEG_RADIUS = 15;

/**
 * The radius of a ball, in px.
 */
export const BALL_RADIUS = 20;
