import type { Signal } from '../store';

export interface Repository<T> {
    /**
     * Creates a signal for the specified key in storage.
     *
     * @param scope - The "scope" of the key (e.g. game name).
     * @param key - The key to create a signal for.
     * @param initialValue - The initial value of the key.
     * @returns A signal for the specified key.
     */
    signal<K extends keyof T>(scope: string, key: K, initialValue: T[K]): Signal<T[K]>;

    /**
     * Retrieves the value of the specified key from storage.
     *
     * @param scope - The "scope" of the key (e.g. game name).
     * @param key - The key to retrieve.
     * @returns The value of the key, or `undefined` if the key does not exist.
     */
    get<K extends keyof T>(scope: string, key: K): T[K] | undefined;

    /**
     * Sets the value of the specified key in storage.
     *
     * @param scope - The "scope" of the key (e.g. game name).
     * @param key - The key to set.
     * @param value - The value to set.
     */
    set<K extends keyof T>(scope: string, key: K, value: T[K]): void;

    /**
     * Removes the specified key from storage.
     *
     * @param scope - The "scope" of the key (e.g. game name).
     * @param key - The key to remove.
     */
    delete<K extends keyof T>(scope: string, key: K): void;

    /**
     * Clears all keys in the specified scope.
     *
     * @param scope - The "scope" to clear.
     */
    clear(scope: string): void;
}

export * from './local';
