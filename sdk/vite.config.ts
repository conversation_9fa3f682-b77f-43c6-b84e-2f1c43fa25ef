import browserslist from 'browserslist-to-esbuild';
import { resolve } from 'node:path';
import { defineConfig } from 'vite';
import { ViteImageOptimizer } from 'vite-plugin-image-optimizer';
import { viteStaticCopy } from 'vite-plugin-static-copy';
import packageJson from './package.json' with { type: 'json' };

// @ts-ignore
import { scss } from '../packages/ui/vite.config';

const pkg = packageJson as {
    name: string;
    dependencies: Record<string, string>;
    browserslist: string[];
};

const globals = Object.keys(pkg.dependencies ?? {})
    .filter((key) => key.startsWith('@monkey-tilt/game-'))
    .reduce(
        (acc, key) => {
            acc[key] = 'MT_' + toPascalCase(key.split('/game-').pop()!.replace(/-/g, '_'));
            return acc;
        },
        {} as Record<string, string>,
    );

export default defineConfig(() => ({
    build: {
        target: browserslist(pkg.browserslist),
        rollupOptions: {
            external: Object.keys(globals),
            output: {
                globals,
                assetFileNames({ names }) {
                    if (names.at(0) === 'originals-sdk.css') {
                        return 'style.css';
                    }
                    return names[0]!;
                },
            },
        },
        lib: {
            entry: resolve(__dirname, 'src/main.ts'),
            name: 'MT_Originals',
            formats: ['umd'],
            fileName: () => 'og.js',
        },
        sourcemap: false,
        assetsInlineLimit: 1024,
    },
    experimental: {
        renderBuiltUrl: (filename, { type }) => (type === 'asset' ? './' + filename : undefined),
    },
    css: {
        devSourcemap: false,
        preprocessorOptions: {
            scss,
        },
    },
    plugins: [
        viteStaticCopy({
            targets: Object.keys(globals).map((name) => ({
                src: `node_modules/${name}/dist/**/!(*.d.ts|*.map)`,
                dest: `games/${globals[name]}`,
            })),
        }),
        ViteImageOptimizer({
            logStats: false,
            svg: {
                multipass: true,
                plugins: [
                    {
                        name: 'preset-default',
                        params: {
                            overrides: {
                                cleanupNumericValues: false,
                                removeViewBox: false,
                                cleanupIds: false,
                                convertPathData: false,
                            },
                        },
                    },
                    'sortAttrs',
                    {
                        name: 'addAttributesToSVGElement',
                        params: {
                            attributes: [{ xmlns: 'http://www.w3.org/2000/svg' }],
                        },
                    },
                ],
            },
        }),
    ],
}));

function toPascalCase(str: string): string {
    return str
        .split('-')
        .map((word) => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
        .join('');
}
