import type { MessageTypes } from './messages';

export * from './messages';

/**
 * A union of all supported message (action) names.
 */
export type MessageName = keyof MessageTypes;

/**
 * A union of all supported message types.
 */
export type MessageType = {
    [K in keyof MessageTypes]: MessageTypes[K] extends { readonly type: infer T }
        ? T
        : 'GameEvent' | 'GameUpdate';
}[keyof MessageTypes];

/**
 * Returns the type of the request payload for a given action.
 */
export type RequestType<Action extends MessageName> = MessageTypes[Action] extends {
    readonly type: infer T;
}
    ? T & string
    : 'GameEvent';

/**
 * Returns the type of the response payload for a given action.
 */
export type ResponseType<Action extends MessageName> = MessageTypes[Action] extends {
    readonly type: infer T;
}
    ? T & string
    : 'GameUpdate';

/**
 * The data type for a given outgoing message (a.k.a., action).
 */
export type RequestData<Action extends MessageName> = MessageTypes[Action]['request'];

/**
 * The data type for a given incoming message (a.k.a., state).
 */
export type ResponseData<Action extends MessageName> = MessageTypes[Action]['response'];

/**
 * A union of all "session" (i.e., non-GameEvent) action names.
 */
export type SessionActionName = {
    [K in MessageName]: RequestType<K> extends 'GameEvent' ? never : K;
}[MessageName];

/**
 * A union of all GameEvent action names.
 */
export type GameActionName = Exclude<MessageName, SessionActionName>;

/**
 * An outgoing message (a.k.a., action) object.
 */
export interface Action<Game extends string = string, Action extends MessageName = MessageName> {
    /**
     * The type of the message (e.g., "GameEvent").
     */
    readonly type: RequestType<Action>;

    /**
     * The action name (e.g., "Open").
     */
    readonly action: Action;

    /**
     * The action payload.
     */
    readonly data: RequestData<Action>;

    /**
     * The unique action ID sent by the client.
     */
    readonly action_id: string;

    /**
     * The game ID for the action, if this is a GameEvent.
     */
    readonly game_id: Action extends GameActionName ? Game : void;

    /**
     * The round ID for the action, if this is a GameEvent.
     */
    readonly round_id: Action extends GameActionName ? string : void;
}

/**
 * A type that any outgoing message (a.k.a., action) object can be assigned to.
 */
export interface ActionLike {
    /**
     * The type of the message (e.g., "GameEvent").
     */
    readonly type: string;

    /**
     * The action name (e.g., "Open").
     */
    readonly action: string;

    /**
     * The action payload.
     */
    readonly data: Record<string, unknown>;

    /**
     * The unique action ID sent by the client.
     */
    readonly action_id: string;

    /**
     * The game ID for the action, if this is a GameEvent.
     */
    readonly game_id?: string | undefined;

    /**
     * The round ID for the action, if this is a GameEvent.
     */
    readonly round_id?: string | undefined;

    /**
     * The event order
     */
    readonly eventOrder?: number;
}

/**
 * An incoming message (a.k.a., state) object.
 */
export interface State<State extends MessageName = MessageName> {
    /**
     * The type of the message (e.g., "GameUpdate").
     */
    readonly type: MessageType;

    /**
     * The action name (e.g., "Open").
     */
    readonly action: State;

    /**
     * The action response payload.
     */
    readonly data: ResponseData<State>;

    /**
     * The unique ID of the action that triggered this state.
     */
    readonly action_id: string;

    /**
     * The new round ID.
     */
    readonly round_id?: string;

    /**
     * The event order.
     */
    readonly eventOrder?: number;
}

export type AnyState = { [K in MessageName]: State<K> }[MessageName];

export type AnyGameState = { [K in GameActionName]: State<K> }[GameActionName];

/**
 * A type that any incoming message (a.k.a., state) object can be assigned to.
 */
export interface StateLike {
    /**
     * The type of the message (e.g., "GameUpdate").
     */
    readonly type: string;

    /**
     * The action name (e.g., "Open").
     */
    readonly action: string;

    /**
     * The action response payload.
     */
    readonly data: Record<string, unknown>;

    /**
     * The unique ID of the action that triggered this state.
     */
    readonly action_id: string;

    /**
     * The new round ID.
     */
    readonly round_id?: string;

    /**
     * The event order.
     */
    readonly eventOrder: number;
}

export type EventsOutOfSync = {
    event_order: number;
    payload: StateLike;
}[];

/**
 * An error response object.
 */
export interface ErrorMessage {
    /**
     * The type of the message (always "Error").
     */
    readonly type: 'Error';

    /**
     * The error message sent by the server.
     */
    readonly message: string;

    /**
     * The error code name.
     */
    readonly code: string;

    /**
     * The unique ID of the action that triggered this error.
     */
    readonly action_id: string;

    /**
     * The event order.
     */
    readonly eventOrder?: number;

    /**
     * The events that are are missing in case the eventOrder is out of sync
     */
    readonly eventsOutOfSync?: EventsOutOfSync;
}

/**
 * A type that any message (action or state) object can be assigned to.
 */
export type Message = Action | State;

/**
 * Type guard for checking if a given action object is of a specific action type.
 *
 * @param action - The action object to check.
 * @param name - The action name to check against.
 * @returns `true` if the action object is of the specified action type, `false` otherwise.
 */
export function isAction<A extends MessageName>(action: Action, name: A): action is Action<A> {
    return action.action === name;
}

/**
 * Type guard for checking if a given state object is of a specific action type.
 *
 * @param state - The state object to check.
 * @param action - The action name to check against.
 * @returns `true` if the state object is of the specified action type, `false` otherwise.
 */
export function isState<Action extends MessageName>(
    state: State,
    action: Action,
): state is State<Action> {
    return state.action === action;
}

/**
 * Type guard for checking if a given state object is a GameUpdate.
 *
 * @param state - The state object to check.
 * @returns `true` if the state object is a GameUpdate, `false` otherwise.
 */
export function isGameUpdate(state: AnyState): state is AnyGameState {
    return state.type === 'GameUpdate';
}

/**
 * Type guard for checking if a given state object is an Authenticate response.
 *
 * @param state - The state object to check.
 * @returns `true` if the state object is an Authenticate response, `false` otherwise.
 */
export function isAuthenticate(state: State): state is State<'auth'> {
    return state.type === 'Authenticate';
}

/**
 * Type guard for checking if a given state object is a GameState response.
 *
 * @param state - The state object to check.
 * @returns `true` if the state object is a GameState response, `false` otherwise.
 */
export function isGameState(state: State): state is State<'GameState'> {
    return state.type === 'GameState';
}
