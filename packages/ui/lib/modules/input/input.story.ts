import type { Meta, StoryObj } from '@storybook/html';
import { fn } from '@storybook/test';
import { But<PERSON> } from '../button/button';
import { Icon } from '../icon/icon';
import { Stepper } from '../stepper/stepper';
import { Input, type InputProps } from './input';

const meta: Meta = {
    title: 'Modules/Input',
};
export default meta;

// Default Input Story
export const Default: StoryObj = (props: InputProps) => {
    return Input({
        ...props,
        prefix: Icon({ name: 'circle-dollar', description: 'USD' }),
        action: Stepper({}),
        children: [
            <PERSON><PERSON>({ label: '1/2', variant: 'tertiary' }),
            But<PERSON>({ label: '2x', variant: 'tertiary' }),
        ],
    }).render();
};
Default.argTypes = {
    value: { control: 'text' },
    disabled: { control: 'boolean' },
    readOnly: { control: 'boolean' },
    variant: { control: 'select', options: ['default', 'medium'] },
};
Default.args = {
    value: '0.00',
    disabled: false,
    readOnly: false,
    variant: 'default',
    onChange: fn(),
};

// Percentage Input Story
export const PercentageInput: StoryObj = (props: InputProps) => {
    return Input({
        ...props,
        value: props.value?.replace('%', '') || '0',
        mask: (value) => {
            const number = parseFloat(value.replace('%', '').trim());
            return `${Math.min(100, Math.max(0, number))}`; // Clamp between 0-100
        },
        suffix: '%',
    }).render();
};
PercentageInput.args = {
    value: '0',
    disabled: false,
    readOnly: false,
    onChange: fn(),
};

// Currency Input Story
export const CurrencyInput: StoryObj = (props: InputProps) => {
    return Input({
        ...props,
        value: props.value?.replace(/[^0-9.]/g, '') || '0.00',
        mask: (value) => {
            const sanitized = value.replace(/[^0-9.]/g, '');
            const [integer, decimal] = sanitized.split('.');

            return decimal !== undefined ? `${integer}.${decimal.slice(0, 2)}` : sanitized;
        },
        suffix: 'USD',
    }).render();
};
CurrencyInput.args = {
    value: '0.00',
    disabled: false,
    readOnly: false,
    onChange: fn(),
};
