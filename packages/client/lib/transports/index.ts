import type { ActionLike, StateLike } from '../messages';

export * from './websocket';

export type ReadyState = 'ready' | 'connecting' | 'disconnected' | 'closed';

/**
 * A transport for sending and receiving messages.
 */
export interface Transport {
    /**
     * Whether the transport is ready to send and receive messages (i.e, whether it is connected).
     */
    readonly readyState: ReadyState;

    /**
     * Closes the current connection and reconnects the transport to the new game session.
     *
     * @param gameSessionId - The game session ID to switch to.
     */
    switchGameSession: (gameSessionId: string) => Promise<void>;

    /**
     * Add a handler for incoming messages.
     *
     * @param handler - The message handler to add.
     */
    onMessage(handler: (message: StateLike) => void): void;

    /**
     * Add a handler for the ready state change.
     *
     * @param handler - The ready state change handler to add.
     */
    onReadyStateChanged(handler: (readyState: ReadyState) => void): void;

    /**
     * Check if a message can be sent.
     *
     * Not all transports support all message types.
     *
     * @param message - The specific message to check.
     * @returns Whether the message can be sent.
     */
    canSend: (message: ActionLike) => boolean;

    /**
     * Send a message.
     *
     * @param message - The message to send.
     * @returns Whether the message was sent.
     */
    send: (message: ActionLike) => boolean;

    /**
     * Close the transport.
     *
     * This will disconnect the transport and clean up any resources.
     * No further messages can be sent or received after this method is called.
     */
    close: () => void;
}
