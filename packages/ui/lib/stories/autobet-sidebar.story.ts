import type { <PERSON>a, StoryObj } from '@storybook/html';
import { fn } from '@storybook/test';
import { html } from '../component';
import { Button } from '../modules/button/button';
import { Frame } from '../modules/frame/frame';
import { Icon } from '../modules/icon/icon';
import { Input } from '../modules/input/input';
import { Label } from '../modules/label/label';
import { Radio } from '../modules/radio/radio';
import { Stepper } from '../modules/stepper/stepper';
import { Tabs } from '../modules/tabs/tabs';
import { Tooltip } from '../modules/tooltip/tooltip';
import { Stack } from '../layouts';

const meta: Meta = {
    title: 'Stories/AutoBet Sidebar',
};
export default meta;

export const Default: StoryObj = {
    render: () =>
        Frame(
            {
                className: {
                    'm-autobet__sidebar': true,
                    'm-frame': true,
                    'm-frame__section': true,
                    'm-frame--subtle': true,
                    'l-stack': true,
                    'l-cover': true,
                    'l-box': true,
                    'u-gap--xl': true,
                },
                css: { 'max-inline-size': '340px', padding: '20px' },
            },

            // Tabs section
            Tabs({
                tabs: [
                    { value: 'manual', label: 'Manual' },
                    { value: 'auto', label: 'Auto' },
                ],
            }),

            // Bet amount section
            html('div')(
                {
                    className: {
                        'm-frame__section': true,
                        'l-stack': true,
                        'u-gap--m': true,
                    },
                },

                // Label with tooltip
                Label(
                    { text: 'Bet Amount', variant: 'medium', css: { 'margin-top': '20px' } },
                    html('span')(null, 'Bet Amount'),
                    Icon({
                        name: 'ask',
                        id: 'm-bet-amount-tooltip-trigger',
                        className: 'm-label__tooltip',
                    }),
                    Tooltip(
                        {
                            target: '#m-bet-amount-tooltip-trigger',
                            showOn: 'hover',
                        },
                        'This is the maximum amount you can bet.',
                    ),
                ),

                // Input with prefix, action, and children
                Input({
                    value: '4.00',
                    prefix: Icon({ name: 'circle-dollar', description: 'USD' }),
                    action: Stepper({}),
                    children: [
                        Button({
                            label: '1/2',
                            variant: 'tertiary',
                        }),
                        Button({
                            label: '2x',
                            variant: 'tertiary',
                        }),
                    ],
                    onChange: fn(),
                }),

                // Preset buttons
                html('div')(
                    {
                        className: {
                            'm-preset-buttons': true,
                            'l-cluster': true,
                            'u-gap--xs': true,
                        },
                    },
                    ['0.10', '1.0', '10.0', '100.0'].map((value) =>
                        Button({
                            label: value,
                            className: { 'm-button': true, 'm-button--secondary': true },
                        }),
                    ),
                ),
            ),

            // Number of bets section
            html('div')(
                { className: { 'm-bets-section': true, 'l-stack': true, 'u-gap--m': true } },
                Label(
                    { text: 'Number of Bets', variant: 'medium' },
                    Icon({
                        name: 'hand',
                        className: 'm-label__icon',
                    }),
                    html('span')(null, 'Number of Bets'),
                ),
                html('div')(
                    { className: { 'm-bets-controls': true, 'l-flex': true } },
                    // Input with button containing hand icon placeholder as action
                    Input({
                        value: '0',
                        action: Button({
                            className: { 'm-stepper': true },
                            children: [
                                Icon({ name: 'infinity', description: 'Adjust number of bets' }),
                            ],
                        }),
                        children: [
                            Button({
                                label: '1/2',
                                variant: 'tertiary',
                            }),
                            Button({
                                label: '2x',
                                variant: 'tertiary',
                            }),
                        ],
                    }),
                ),
            ),

            // On win section with radio buttons
            Frame(
                {
                    variant: 'small',
                    className: {
                        'u-gap--m': true,
                    },
                    cssProps: {
                        'frame-padding': { var: 'size-m' },
                    },
                },
                Label({ text: 'On Win', variant: 'medium' }),
                html('div')(
                    { className: { 'l-flex': true, 'u-gap--l': true } },
                    Radio({
                        id: 'm-on-win-reset',
                        name: 'on-win',
                        label: 'Reset',
                        checked: true,
                        className: { 'm-radio-option': true },
                    }),
                    Radio({
                        id: 'm-on-win-increase',
                        name: 'on-win',
                        label: 'Increase by',
                        className: { 'm-radio-option': true },
                    }),
                ),
                Input({
                    id: 'm-on-win-percentage',
                    type: 'text',
                    value: '0',
                    mask: (value) => {
                        const trimmedValue = value.trim();
                        if (trimmedValue === '') {
                            return '0';
                        }
                        const number = parseFloat(trimmedValue.replace('%', ''));
                        return isNaN(number) ? '0' : `${Math.min(100, Math.max(0, number))}`;
                    },
                    children: [
                        html('span')({
                            className: { 'm-input__addons-percentage': true },
                            textContent: '%',
                        }),
                    ],
                    placeholder: 'Enter percentage',
                }),
            ),

            // On lose section with radio buttons
            Frame(
                {
                    variant: 'small',
                    className: {
                        'u-gap--m': true,
                    },
                    cssProps: {
                        'frame-padding': { var: 'size-m' },
                    },
                },
                Label({ text: 'On Lose', variant: 'medium' }),
                html('div')(
                    { className: { 'l-flex': true, 'u-gap--l': true } },
                    Radio({
                        id: 'm-on-lose-reset',
                        name: 'on-lose',
                        label: 'Reset',
                        checked: true,
                        className: { 'radio-option': true },
                    }),
                    Radio({
                        id: 'on-lose-increase',
                        name: 'on-lose',
                        label: 'Increase by',
                        className: { 'm-radio-option': true },
                    }),
                ),
                Input({
                    id: 'm-on-lose-percentage',
                    type: 'text',
                    value: '0',
                    mask: (value) => {
                        const trimmedValue = value.trim();
                        if (trimmedValue === '') {
                            return '0';
                        }
                        const number = parseFloat(trimmedValue.replace('%', ''));
                        return isNaN(number) ? '0' : `${Math.min(100, Math.max(0, number))}`;
                    },
                    suffix: '%',
                    placeholder: 'Enter percentage',
                }),
            ),

            // Stop on win/loss section
            Stack(
                null,
                // Stop on win
                html('div')(
                    { className: { 'l-flex': true, 'u-gap--s': true } },
                    Label(
                        {
                            text: 'Stop on Win',
                            variant: 'medium',
                            css: { display: 'flex', 'justify-content': 'space-between' },
                        },
                        html('span')(null, 'Stop on Win'),
                        html('span')({ css: { 'font-weight': 'normal' } }, '$0.00 USD'),
                    ),
                    Input({
                        id: 'stop-on-win',
                        type: 'text',
                        value: '0.00',
                        mask: (value) => {
                            const sanitized = value.replace(/[^0-9.]/g, '');
                            const [integer, decimal] = sanitized.split('.');
                            return decimal !== undefined
                                ? `${integer}.${decimal.slice(0, 2)}`
                                : sanitized;
                        },
                        suffix: 'USD',
                        placeholder: 'Enter stop value',
                    }),
                ),

                // Stop on loss
                html('div')(
                    { className: { 'l-flex': true, 'u-gap--s': true } },
                    Label(
                        {
                            text: 'Stop on Loss',
                            variant: 'medium',
                            css: { display: 'flex', 'justify-content': 'space-between' },
                        },
                        html('span')(null, 'Stop on Loss'),
                        html('span')({ css: { 'font-weight': 'normal' } }, '$0.00 USD'),
                    ),
                    Input({
                        id: 'stop-on-loss',
                        type: 'text',
                        value: '0.00',
                        mask: (value) => {
                            const sanitized = value.replace(/[^0-9.]/g, '');
                            const [integer, decimal] = sanitized.split('.');
                            return decimal !== undefined
                                ? `${integer}.${decimal.slice(0, 2)}`
                                : sanitized;
                        },
                        suffix: 'USD',
                        placeholder: 'Enter stop value',
                    }),
                ),
            ),

            // Bet button
            Button({
                cta: true,
                label: 'BET',
            }),

            // Provably fair section
            html('div')(
                {
                    className: {
                        'm-provably-fair': true,
                        'm-frame__section': true,
                        'l-stack__split': true,
                        'l-stack': true,
                        'l-box': true,
                        'u-gap--s': true,
                    },
                },
                html('button')(
                    {
                        className: { 'm-provably-fair-button': true, 'u-text--center': true },
                    },
                    Label(
                        {
                            variant: 'subtle',
                            icon: 'double-check',
                            className: { 'm-provably-fair-label': true },
                        },
                        html('span')({ className: { 'u-hide@m': true } }, 'This Game is'),
                        'Provably Fair',
                    ),
                ),
            ),
        ).render(),
};
Default.storyName = 'AutoBet Sidebar';
