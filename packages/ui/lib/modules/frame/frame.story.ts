import type { Meta, StoryObj } from '@storybook/html';
import { Frame, type FrameProps } from './frame';
import { Label } from '../label/label';

const meta: Meta = {
    title: 'Modules/Frame',
    globals: {
        backgrounds: { value: 'dark' },
    },
};
export default meta;

// N.B.: the #storybook-root has `padding: 1rem` applied to it.
const css = { 'min-block-size': 'calc(100vh - 2rem)' };

export const Default: StoryObj = (props: FrameProps) => {
    return Frame({ ...props, css }).render();
};
Default.argTypes = {
    variant: {
        control: 'select',
        options: ['default', 'small', 'medium', 'dashed'],
    },
};
Default.args = {
    variant: 'default',
};

export const Sections: StoryObj = () => {
    return Frame(
        { css },
        Frame.Section({ variant: 'subtle' }, Label({ text: 'Section 1' })),
        Frame.Section(null, Label({ text: 'Section 2' })),
        Frame.Section({ className: { 'l-stack__split': true } }, Label({ text: 'Section 3' })),
    ).render();
};
Sections.storyName = 'With Sections';
