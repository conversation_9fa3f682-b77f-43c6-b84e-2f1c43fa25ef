export class RingBuffer<T> {
    #capacity: number;
    #nextIndex = 0;
    #buffer: T[];

    public constructor(capacity: number, ...values: T[]) {
        this.#capacity = capacity;
        this.#buffer = new Array(capacity);
        this.push(...values);
    }

    public add(value: T) {
        this.#buffer[this.#nextIndex++ % this.#capacity] = value;
    }

    public push(...values: T[]) {
        for (const value of values) {
            this.#buffer[this.#nextIndex++ % this.#capacity] = value;
        }
    }

    public get size() {
        return Math.min(this.#nextIndex, this.#capacity);
    }

    public keys(): IterableIterator<T> {
        const size = this.size;
        const capacity = this.#capacity;
        const buffer = this.#buffer;
        const start = this.#nextIndex - size;
        return (function* (): IterableIterator<T> {
            for (let i = 0; i < size; i++) {
                yield buffer[(start + i) % capacity]!;
            }
        })();
    }

    public [Symbol.iterator](): IterableIterator<T> {
        return this.keys();
    }

    public toJSON(): T[] {
        return Array.from(this.keys());
    }
}
