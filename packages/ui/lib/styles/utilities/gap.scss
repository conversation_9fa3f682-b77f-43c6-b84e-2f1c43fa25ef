@use 'sass:meta';
@use '../config/metrics';
@use '../common/bem';
@use '../common/prop';
@use '../common/respond';
@use '../common/utility';

///
/// The gap utility classes for each step in the spacing scale.
/// These classes only control the --gap variable, not the gap property itself.
///
/// Usage:
///
/// <div class="l-stack u-gap--xs">
///    <div>First child</div>
///    <div>Second child</div>
/// </div>
///
/// Configuration:
///     metrics.$space-scale    The spacing scale steps (e.g. xs, s, m, l, xl, 2xl etc.).
///
/// Block:
///     .u-gap              The default gap size (the same as .u-gap--s).
///
/// Elements:
///     <none>
///
/// Modifiers:
///     .u-gap--{N}          N is the step in the spacing scale (e.g. xs, s, m, l, xl, 2xl etc.).
///     .u-gap--{N}@{BP}     Variation of the modifier at the breakpoint BP (e.g. xs, s, m etc.).
///

@mixin -gap-utility($size) {
    @include prop.set('gap', prop.get('size-#{$size}'), $important: true);
}

@mixin styles {
    @include bem.utility('gap') {
        @include -gap-utility(s);
        @include utility.modifiers('&', metrics.$space-scale, meta.get-mixin('-gap-utility'));
    }
}
