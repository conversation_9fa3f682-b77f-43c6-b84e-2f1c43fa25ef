#!/bin/bash

FFMPEG="ffmpeg -hide_banner"
LOUDNORM="-af loudnorm=I=-16:TP=-1.5:LRA=11"

if [[ "$1" == "--loudnorm" ]]; then
    shift
    if [[ "$1" == "off" ]]; then
        shift
        LOUDNORM=""
    fi
fi

if [ "$#" -lt 2 ]; then
    echo "Usage: $0 [options] <sprite-name> file1.wav file2.wav ... fileN.wav"
    exit 1
fi

sprite="$1"
shift

tmp_dir=$(mktemp -d)
concat_file="$tmp_dir/concat_list.txt"

total_duration=0

echo "{" >$sprite.json

first=true
for wav_file in "$@"; do
    if [[ ! -f "$wav_file" ]]; then
        echo "Error: File '$wav_file' does not exist."
        exit 1
    fi

    name=$(basename "$wav_file" .wav)

    duration=$($FFMPEG -i "$wav_file" 2>&1 | grep Duration | awk '{print $2}' | tr -d , | awk -F: '{ print ($1 * 3600) + ($2 * 60) + $3 }')

    temp_wav="$tmp_dir/$name.wav"
    $FFMPEG -y -i "$wav_file" $LOUDNORM -acodec pcm_s16le -ar 44100 "$temp_wav" >/dev/null 2>&1
    echo "file '$temp_wav'" >>"$concat_file"

    if [ "$first" = true ]; then
        first=false
    else
        printf ",\n" >>$sprite.json
    fi

    printf "\"$name\": [$(printf "%.2f" "$total_duration"), $(printf "%.2f" "$duration")]" >>$sprite.json

    total_duration=$(echo "$total_duration + $duration" | bc)
done

printf "\n}\n" >>$sprite.json

$FFMPEG -y -f concat -safe 0 -i "$concat_file" -c copy "$tmp_dir/$sprite.wav"

$FFMPEG -y -i "$tmp_dir/$sprite.wav" -codec:a libmp3lame -qscale:a 2 $sprite.mp3
$FFMPEG -y -i "$tmp_dir/$sprite.wav" -codec:a libvorbis $sprite.ogg
$FFMPEG -y -i "$tmp_dir/$sprite.wav" -codec:a libopus $sprite.webm

rm -rf "$tmp_dir"
