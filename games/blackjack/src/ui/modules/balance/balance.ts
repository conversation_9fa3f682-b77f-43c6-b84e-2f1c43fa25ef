import { currencyToFixedString, formatCurrency, nearlyEqual, timer } from '@monkey-tilt/client';
import {
    defineComponent,
    html,
    Label,
    mergeClasses,
    withNamespace,
    type Component,
    type LabelControls,
    type Props,
    type RefObject,
} from '@monkey-tilt/ui';
import type { BlackjackUI } from '../../blackjack';

export interface BalanceProps extends Props<HTMLDivElement> {
    /**
     * The Blackjack UI state manager.
     */
    readonly ui: BlackjackUI;
}

export const Balance = defineComponent(
    'Balance',
    ({ state, render }) =>
        ({ ui, ...props }: BalanceProps): Component<HTMLDivElement> => {
            const payoutsRef: RefObject<HTMLDivElement> = { current: null };
            const { effect, previousMemo, memo, untracked } = state();

            const balanceDeltas: { text: string; isPositive: boolean }[] = [];
            const payoutsY = `--${withNamespace('payouts-y')}`;

            const duration = (ms: number) => (ui.isTurboMode ? 0.1 : ms);

            const showNextDelta = () => {
                if (balanceDeltas.length === 0) {
                    return;
                }

                const payouts = payoutsRef.current!;
                const prevHeight = payouts.offsetHeight;

                const { text, isPositive } = balanceDeltas[0]!;

                const label = render(
                    Label({
                        text,
                        variant: 'caption',
                        className: { 'is-positive': isPositive, 'is-negative': !isPositive },
                    }),
                );
                payouts.appendChild(label);

                const animation = label.animate([{ opacity: 0 }, { opacity: 1 }], {
                    duration: 150,
                    fill: 'forwards',
                    easing: 'ease-out',
                });

                setTimeout(() => {
                    const delta = payouts.offsetHeight - prevHeight;
                    animation.onfinish = () => {
                        balanceDeltas.shift();
                        showDeltaDelay(showNextDelta);

                        setTimeout(() => {
                            label.animate([{ opacity: 1 }, { opacity: 0 }], {
                                duration: 100,
                                fill: 'forwards',
                                easing: 'ease-in',
                            });

                            const y = parseFloat(payouts.style.getPropertyValue(payoutsY) || '0');
                            payouts.style.setProperty(payoutsY, `${y - delta}px`);
                            payouts.addEventListener(
                                'transitionend',
                                () => {
                                    if (
                                        -parseFloat(
                                            payouts.style.getPropertyValue(payoutsY) || '0',
                                        ) >= payouts.offsetHeight
                                    ) {
                                        payouts.style.setProperty(payoutsY, '0');
                                        payouts.innerHTML = '';
                                    }
                                },
                                { once: true },
                            );
                        }, 2000);
                    };
                }, 100);
            };

            const showDeltaDelay = timer(300);
            const displayDelta = (amount: number, label = '') => {
                setTimeout(
                    () => {
                        balanceDeltas.push({
                            text:
                                (amount > 0 ? '+' : '') +
                                currencyToFixedString(amount, untracked(ui.currency)) +
                                (label && ` ${label}`),
                            isPositive: amount >= 0,
                        });
                        if (payoutsRef.current) {
                            if (balanceDeltas.length === 1 || ui.isTurboMode) {
                                showNextDelta();
                            } else {
                                showDeltaDelay(showNextDelta);
                            }
                        }
                    },
                    duration(amount < 0 ? 750 : 1000),
                );
            };

            return html('div')(
                {
                    ...props,
                    className: mergeClasses(props.className, withNamespace('m-blackjack__balance')),
                },
                Label({
                    variant: 'caption',
                    controls({ setText }: LabelControls) {
                        const playerBalance = memo(() => ui.gameState().balance);
                        const isAnimatingState = memo(() => ui.gameState().isAnimatingState);

                        const poker = previousMemo(() => {
                            const { poker = { payout: '', status: '' } } = ui.gameState();
                            return {
                                payout: poker.payout ? Number.parseFloat(poker.payout) : 0,
                                status:
                                    !poker.status || poker.status === 'sidebet_lost'
                                        ? (false as const)
                                        : poker.status,
                                text: poker.status
                                    ? (poker.status === 'suited_trips'
                                          ? 'Suited Trips'
                                          : poker.status === 'straight_flush'
                                            ? 'Straight Flush'
                                            : poker.status === 'three_of_a_kind'
                                              ? 'Three of a Kind'
                                              : poker.status === 'straight'
                                                ? 'Straight'
                                                : 'Flush') + '!'
                                    : '',
                            };
                        });

                        const perfectPair = previousMemo(() => {
                            const { perfect_pairs = { payout: '', status: '' } } = ui.gameState();
                            return {
                                payout: perfect_pairs.payout
                                    ? Number.parseFloat(perfect_pairs.payout)
                                    : 0,
                                status:
                                    !perfect_pairs.status || perfect_pairs.status === 'sidebet_lost'
                                        ? (false as const)
                                        : perfect_pairs.status,
                                text: perfect_pairs.status
                                    ? (perfect_pairs.status === 'perfect_pair'
                                          ? 'Perfect Pair'
                                          : perfect_pairs.status === 'coloured_pair'
                                            ? 'Coloured Pair'
                                            : perfect_pairs.status === 'mixed_pair'
                                              ? 'Mixed Pair'
                                              : 'Pair') + '!'
                                    : '',
                            };
                        });

                        const insurance = previousMemo(() => {
                            const { insurance = { payout: '', status: '' } } = ui.gameState();
                            return {
                                payout: insurance.payout ? Number.parseFloat(insurance.payout) : 0,
                                status:
                                    !insurance.status || insurance.status === 'insurance_lost'
                                        ? (false as const)
                                        : insurance.status,
                                text: insurance.status ? 'Insurance won!' : '',
                            };
                        });

                        let currentBalance = playerBalance();
                        let targetBalance = currentBalance;
                        let updateInterval: ReturnType<typeof setInterval> | undefined = undefined;

                        const update = () => {
                            if (updateInterval) {
                                return;
                            }
                            const startingBalance = currentBalance;
                            updateInterval = setInterval(() => {
                                const delta = Math.abs(targetBalance - startingBalance) / 5;
                                if (targetBalance > currentBalance) {
                                    currentBalance = Math.min(
                                        currentBalance + delta,
                                        targetBalance,
                                    );
                                } else {
                                    currentBalance = Math.max(
                                        currentBalance - delta,
                                        targetBalance,
                                    );
                                }

                                if (nearlyEqual(currentBalance, targetBalance)) {
                                    clearInterval(updateInterval);
                                    updateInterval = undefined;
                                    currentBalance = targetBalance;
                                }

                                setText(
                                    `Balance: ${formatCurrency(currentBalance, untracked(ui.currency))}`,
                                );
                            }, 50);
                        };

                        effect(() => {
                            setText(
                                `Balance: ${formatCurrency(untracked(playerBalance), ui.currency())}`,
                            );
                            const { current: currentPoker, previous: previousPoker } = poker();

                            if (
                                currentPoker.status !== previousPoker.status &&
                                currentPoker.payout !== 0
                            ) {
                                displayDelta(currentPoker.payout, currentPoker.text);
                            }
                        });

                        effect(() => {
                            const { current: currentPerfectPair, previous: previousPerfectPair } =
                                perfectPair();

                            if (
                                currentPerfectPair.status !== previousPerfectPair.status &&
                                currentPerfectPair.payout !== 0
                            ) {
                                displayDelta(currentPerfectPair.payout, currentPerfectPair.text);
                            }
                        });

                        effect(() => {
                            const { current: currentInsurance, previous: previousInsurance } =
                                insurance();

                            if (
                                currentInsurance.status !== previousInsurance.status &&
                                currentInsurance.payout !== 0
                            ) {
                                displayDelta(currentInsurance.payout, currentInsurance.text);
                            }
                        });

                        const payout = memo(() => {
                            const { round_closed = false, total_payout = '0' } = ui.gameState();
                            return round_closed ? Number.parseFloat(total_payout) : 0;
                        });

                        effect(() => {
                            const payoutAmount = payout();
                            if (payoutAmount !== 0) {
                                displayDelta(payoutAmount);
                            }
                        });

                        let didCurrencyChange = false;

                        effect(() => {
                            void ui.currency();
                            didCurrencyChange = true;
                        });

                        effect(() => {
                            const newBalance = playerBalance();
                            if (isAnimatingState()) {
                                return;
                            }
                            targetBalance = newBalance;
                            if (updateInterval) {
                                clearInterval(updateInterval);
                                updateInterval = undefined;
                            }
                            setTimeout(update, duration(750));
                        });
                    },
                }),
                html('div')(
                    { className: withNamespace('m-blackjack__payouts') },
                    html('div')({ ref: payoutsRef }),
                ),
            );
        },
);
