export interface CurrencyChanged {
    readonly type: 'CURRENCY_CHANGED';
    readonly gameSessionId: string;
    readonly currency: string;
}

export interface ContainerResized {
    readonly type: 'CONTAINER_RESIZED';
}

export interface SetCurrencyChangeAllowed {
    readonly type: 'SET_CURRENCY_CHANGE_ALLOWED';
    readonly isAllowed: boolean;
}

export type HostNotification = CurrencyChanged | ContainerResized;
export type HostAction = SetCurrencyChangeAllowed;

export interface GameContext {
    readonly unmount: () => void;
    readonly onAction: (callback: (action: HostAction) => void) => void;
    readonly notify: (notification: HostNotification) => void;
}
