#!/bin/bash

#
# This script is used to get a session ID required for launching a game.
#
# Usage: MT_AUTH_TOKEN=<JWT> ./get-session-id.sh <profileId> <gameId> [currencyCode]
#
# - MT_AUTH_TOKEN: The JWT token to authenticate the request (set as env var).
# - profileId: The profile ID of a user to launch the game for.
# - gameId: The game ID to launch.
# - currencyCode: The currency code to use. Default is "USD".
#
# To obtain the JWT token and the profile ID, you should:
#   1. Register a new user on the staging environment (if you don't have one).
#   2. Login as that user
#   3. Open the DevTools and look in the Network for any XHR request containing the 
#       Authorization header and the profile ID in the URL.
#       For example the wallets API endpoint looks like this:
#
#       https://api.p-dev.monkeytilt.codes/wallets/v1/profiles/<profileId>/wallets
#
#       That same API call will include the Authorization header with the JWT token.
#

if [ "$#" -lt 2 ]; then
    echo "Usage: MT_AUTH_TOKEN=<JWT> $0 <profileId> <gameId> [currencyCode]"
    exit 1
fi

PROFILE_ID=$1
GAME_ID=$2

if [ -z "$3" ]; then
    CURRENCY_CODE="USD"
else
    CURRENCY_CODE=$3
fi

if [ -z "$MT_AUTH_TOKEN" ]; then
    echo "Error: Authorization token is required, please set the MT_AUTH_TOKEN env var."
    exit 1
fi

PAYLOAD="{                                                  \
            \"gameId\":\"$GAME_ID\",                        \
            \"currencyCode\":\"$CURRENCY_CODE\",            \
            \"languageCode\":\"en\",                        \
            \"deviceCode\":\"desktop\",                     \
            \"lobbyUrl\":\"https://monkeytilt.com\",        \
            \"reloadUrl\":\"https://monkeytilt.com\"        \
        }"

RESULT=$(curl -s "https://api.p-dev.monkeytilt.codes/casino/v1/profiles/$PROFILE_ID/launch" \
    -H "Authorization: Bearer $MT_AUTH_TOKEN" \
    -H "Content-Type: application/json" \
    -H "Accept: application/json" \
    -H "X-Tenant-ID: monkeytilt" \
    -d "$PAYLOAD")

if [ $? -ne 0 ]; then
    echo "Error: Failed to launch the game:"
    echo "API request failed."
    exit 1
fi

if echo "$RESULT" | jq -e 'type=="array" and .[0].message' >/dev/null 2>&1; then
    echo "Error: Failed to launch the game:"
    echo "$RESULT" | jq -r '.[].message'
    exit 1
elif echo "$RESULT" | jq -e 'type=="object" and has("sessionId")' >/dev/null 2>&1; then
    echo "$RESULT" | jq -r '.sessionId'
    exit 0
else
    echo "Error: Failed to launch the game:"
    echo "Unexpected response from the API:"
    echo "$RESULT"
    exit 1
fi
