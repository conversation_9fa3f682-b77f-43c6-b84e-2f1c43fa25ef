@use '../common/bem';
@use '../common/prop';
@use '../common/respond';

///
/// The Stack Layout primitive injects block margin between its child elements.
/// Children are always in block order, start to end.
///
/// A split point can be set which will push towards the block end all children
/// after that point.
/// Split point can be defined via --split-X and --split-last-X modifiers, or
/// via __split element (which defines the first element in the split group).
///
///
///             Stack Layout
///   /-----------------------------\
///   | +-------------------------+ |
///   | |                         | | <================+
///   | |                         | |                  |
///   | +-------------------------+ |                  |
///   | ##########################  | <==== Gap        |
///   | +-------------------------+ |                  |
///   | |                         | |                  |
///   | |                         | | <================+
///   | |                         | |                  |
///   | +-------------------------+ |                  |===== Stack Children
///   |            -----            |                  |
///   |              ¦              |                  |
///   |            Split            |                  |
///   |              ¦              |                  |
///   |            -----            |                  |
///   | +-------------------------+ |                  |   <====+
///   | |                         | | <================+        |  Split group
///   | |                         | |                           | (split-last-1)
///   | +-------------------------+ |                      <====+
///   \-----------------------------/
///
/// Configuration:
///     $-split-points          The maximum number of split points in stack layouts. Defaults to 6.
///
/// Variables:
///     --gap                   The gap between children. Defaults to var(--size-s).
///     --stack-gap             The stack-specific gap that overrides the --gap. Unset by default.
///
/// Usage:
///
///     <div class="l-stack">
///         <div>First child</div>
///         <div>Second child</div>
///     </div>
///
/// Block:
///    .l-stack
///
/// Elements:
///    .l-stack__split              The first element in the "split group".
///    .l-stack__split-after        The element after which the "split group" is created.
///
/// Modifiers:
///    .l-stack--reverse            Reverses the order of the children.
///    .l-stack--reverse@{BP}       Reverses the order of the children at the given breakpoint.
///    .l-stack--center             Centers the children, instead of stretching them.
///    .l-stack--split-{N}          Split after {N}th child (where {N} goes from 1 to $stack-split-point).
///    .l-stack--split-last-{N}     Split before the last {N}th child (where {N} goes from 1 to $stack-split-point).
///    .l-stack--scroll             Makes the stack scrollable, if the children overflow.
///
/// Notes:
///
/// More than one split group can be defined, for example:
///
///     <div class="l-stack l-stack--split-2 l-stack--split-last-1" style="--gap: var(--size-l)">
///         <div>First</div>
///         <div>Second</div>
///         <div>Third</div>
///         <div>Fourth</div>
///         <div>Fifth</div>
///     </div>
///
/// Will render as:
///
///     +-----------+
///     |   First   |
///     |   Second  |
///     |           |
///     |           |
///     |   Third   |
///     |   Fourth  |
///     |           |
///     |           |
///     |   Fifth   |
///     +-----------+
///

$-split-points: 6;

@mixin styles {
    @include bem.layout('stack') {
        display: flex;
        flex-direction: column;
        justify-content: flex-start;
        gap: prop.get(stack-gap, prop.get(gap, prop.get(size-s)));

        &:only-child {
            block-size: 100%;
        }

        &--center {
            align-items: center;
        }

        & > * {
            margin-block: 0;
        }

        &--reverse {
            flex-direction: column-reverse;
        }

        &--scroll {
            overflow-x: hidden;
            overflow-y: auto;
            max-block-size: 100%;
            min-block-size: 0;
            flex-wrap: nowrap;
        }

        @each $breakpoint in respond.breakpoint-names() {
            &--reverse\@#{$breakpoint} {
                @include respond.until($breakpoint) {
                    flex-direction: column-reverse;
                }
            }
        }

        &__split {
            margin-block-start: auto;
        }

        &__split-after {
            margin-block-end: auto;
        }

        @for $i from 1 through $-split-points {
            &--split-#{$i} > :nth-child(#{$i}) {
                margin-block-end: auto;
            }
            &--split-last-#{$i} > :nth-last-child(#{$i}) {
                margin-block-start: auto;
            }
        }
    }
}
