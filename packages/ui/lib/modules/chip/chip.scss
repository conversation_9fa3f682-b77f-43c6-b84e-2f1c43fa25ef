@use '../../styles/common/bem';
@use '../../styles/common/prop';
@use '../../styles/common/util';
@use '../../styles/common/type-style' as *;
@use '../../styles/config/metrics';

$-colors: (
    'red': #ef4444,
    'black': #303030,
    'blue': #1d4ed8,
    'orange': #ea580c,
    'violet': #6d28d9,
    'indigo': #6366f1,
    'dark-cyan': #0891b2,
    'cyan': #0d9488,
    'green': #22c55e,
);

$-stroke-width: util.to-rem(3px);
$-chip-size: metrics.fluid-size(30, 44);

@mixin styles {
    @include bem.module('chip') {
        position: relative;
        inline-size: prop.get(chip-size, $-chip-size);
        aspect-ratio: 1 / 1;
        border-radius: 999em;
        cursor: pointer;
        filter: drop-shadow(0 0 util.to-rem(1px) rgba(0, 0, 0, 0.8));
        flex: 0 0 auto;

        @include prop.set(
            chip-dark-color,
            oklch(from prop.get(chip-color) calc(l * 0.642) calc(c * 0.595) calc(h + 12.25))
        );

        &#{bem.namespaced-selector(is-selected)} {
            background: prop.get(color-white);
        }

        > * {
            position: absolute;
            inline-size: calc(100% - #{$-stroke-width} * 2);
            inset: $-stroke-width;
            pointer-events: none;
        }

        &__value {
            display: flex;
            justify-content: center;
            align-items: center;
            color: prop.get(color-white);
            inset-block-end: calc(5% + #{$-stroke-width});
            inset-inline-end: calc(2.5% + #{$-stroke-width});
            transform: scale(0.9);
            inline-size: auto;

            @include type-style(
                'caps',
                $with: (font-family: Rubik, font-weight: 500, letter-spacing: -5%)
            );
        }

        @each $color, $value in $-colors {
            &--#{$color} {
                @include prop.set(chip-color, $value);
            }
        }

        &-stack {
            display: grid;
            inline-size: fit-content;
            justify-content: flex-end;
            transition: 0.3s ease;

            @include prop.set(
                (
                    stack-size: prop.get(chip-size, metrics.fluid-size(38, 44)),
                    chip-offset: 0.1,
                )
            );

            &:hover {
                @include prop.set(chip-offset, 0.3);
                > * {
                    @include prop.set(
                        (
                            chip-anim-delay: calc(prop.get(chip-count) - prop.get(chip-index)),
                            chip-x: calc(
                                    prop.get(chip-index) * util.to-rem(1px) *
                                        sin(
                                            120deg + (prop.get(chip-index) + prop.get(chip-count)) *
                                                60deg
                                        )
                                ),
                        )
                    );
                }
            }

            > * {
                grid-area: 1 / 1;
                z-index: prop.get(chip-index);
                transition: 0.3s ease-in-out;
                transition-delay: calc(prop.get(chip-anim-delay, prop.get(chip-index)) * 50ms);
                top: calc(prop.get(stack-size) * -1 * prop.get(chip-offset) * prop.get(chip-index));
                transform: translate(prop.get(chip-x, 0), 0);

                &:first-child {
                    margin-block-start: 0;
                }
            }
        }

        &-select {
            position: relative;
            inline-size: 0;
            min-inline-size: 100%;
            overflow: hidden;
            padding: calc(prop.get(reel-gap) / 2);
            background: linear-gradient(180deg, #1d1d1f 0%, #111113 100%);
            border: util.to-rem(1px) solid prop.get(color-zinc-800);
            border-radius: util.to-rem(12px);

            @include prop.set(reel-gap, prop.get(size-2xs));

            &__reel {
                min-inline-size: 100%;
                width: fit-content;
                scrollbar-width: none;
                justify-content: center;

                &::-webkit-scrollbar {
                    block-size: 0;
                    inline-size: 0;
                }
            }

            :where(
                    &#{bem.namespaced-selector(is-overflowing-start)},
                    &#{bem.namespaced-selector(is-overflowing-end)}
                )
                &__reel {
                justify-content: start;
            }

            &__button {
                position: absolute;
                block-size: 100%;
                aspect-ratio: 1 / 2;
                border: none;
                background: linear-gradient(90deg, #1d1d1f 50%, transparent 100%);
                color: prop.get(color-zinc-300);
                inset: 0 auto auto 0;
                display: none;
                transform: translate(-20%, 0);

                &:hover {
                    color: prop.get(color-white);
                }

                &:last-child {
                    inset: 0 0 auto auto;
                    transform: translate(20%, 0);
                    background: linear-gradient(90deg, transparent 0%, #1d1d1f 50%);
                }
            }

            &#{bem.namespaced-selector(is-overflowing-start)} &__button:not(:last-child) {
                display: block;
            }

            &#{bem.namespaced-selector(is-overflowing-end)} &__button:last-child {
                display: block;
            }
        }
    }
}
