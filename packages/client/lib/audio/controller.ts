import { logDevError } from '@monkey-tilt/utils';
import type { Source } from './loader';
import { Sound, SoundSprite, type SoundSpriteDefinitionLike } from './sounds';

export class AudioController {
    #context: AudioContext | null = null;
    #gain: GainNode | null = null;
    #volume = 1;
    #isMuted = false;
    #isLocked = true;
    #pendingClients: Array<(context: AudioContext) => void> = [];
    #abortController = new AbortController();
    #isDisposed = false;

    readonly offlineContext: OfflineAudioContext;

    public constructor() {
        this.offlineContext = new OfflineAudioContext(1, 2, 44100);

        const initController = new AbortController();
        const init = () => {
            initController.abort();

            if (this.#context) {
                return;
            }

            this.#context = new AudioContext();

            this.#gain = this.#context.createGain();
            this.#gain.connect(this.#context.destination);

            this.#isLocked =
                this.#isSuspended() && ('ontouchstart' in globalThis || 'onclick' in globalThis);

            this.#unlock()
                .then(() => {
                    for (const client of this.#pendingClients) {
                        client(this.#context!);
                    }
                })
                .catch(logDevError);

            const unlockController = new AbortController();
            const unlock = () => {
                this.#unlock()
                    .then(() => {
                        if (!this.#isLocked) {
                            unlockController.abort();
                        }
                    })
                    .catch(logDevError);
            };

            for (const event of ['touchend', 'mousedown', 'keypress']) {
                document.addEventListener(event, unlock, {
                    capture: true,
                    signal: unlockController.signal,
                });
            }
        };

        for (const event of ['touchstart', 'touchend', 'mousedown', 'keypress']) {
            document.addEventListener(event, init, {
                capture: true,
                signal: initController.signal,
            });
        }

        globalThis.addEventListener(
            'focus',
            () => {
                if (this.#context && (this.#isSuspended() || !this.#isLocked)) {
                    this.#context.resume().catch(logDevError);
                }
            },
            {
                signal: this.#abortController.signal,
            },
        );

        globalThis.addEventListener(
            'blur',
            () => {
                if (this.#context && !this.#isSuspended()) {
                    this.#context.suspend().catch(logDevError);
                }
            },
            {
                signal: this.#abortController.signal,
            },
        );
    }

    public dispose() {
        this.#isDisposed = true;
        this.#abortController.abort();
        if (this.#context) {
            void this.#context.close();
            this.#context = null;
        }
        if (this.#gain) {
            this.#gain.disconnect();
            this.#gain = null;
        }
    }

    public createSound(sources: ReadonlyArray<Source>): Sound {
        return new Sound(this, sources);
    }

    public createSoundSprite<T extends string>(
        definition: SoundSpriteDefinitionLike<T>,
    ): SoundSprite<T> {
        return new SoundSprite(this, definition);
    }

    public connect(node: AudioNode): boolean {
        if (this.#gain) {
            node.connect(this.#gain);
            return true;
        }
        return false;
    }

    public async resume(): Promise<AudioContext> {
        if (!this.#context) {
            if (this.#isDisposed) {
                throw new Error('Audio controller has been disposed');
            }
            return new Promise((resolve) => {
                this.#pendingClients.push(resolve);
            });
        }

        if (this.#isSuspended()) {
            await this.#context.resume();
        }

        return this.#context;
    }

    #isSuspended(): boolean {
        if (!this.#context) {
            return true;
        }
        const state = this.#context.state as 'playing' | 'suspended' | 'interrupted';
        return state === 'suspended' || state === 'interrupted';
    }

    public get muted(): boolean {
        return this.#isMuted;
    }

    public set muted(value: boolean) {
        if (this.#isMuted === value) {
            return;
        }

        this.#isMuted = value;

        if (this.#gain) {
            this.#gain.gain.value = value ? 0 : this.#volume;
        }
    }

    public get volume(): number {
        return this.#volume;
    }

    public set volume(value: number) {
        if (this.#volume === value) {
            return;
        }

        this.#volume = value;

        if (!this.#isMuted && this.#gain) {
            this.#gain.gain.value = value;
        }
    }

    async #unlock(): Promise<void> {
        if (!this.#isLocked || !this.#context) {
            return;
        }

        const source = this.#context.createBufferSource();
        source.buffer = this.#context.createBuffer(1, 1, 22050);
        source.connect(this.#context.destination);
        source.start(0, 0, 0);

        if (this.#isSuspended()) {
            await this.#context.resume();
        }

        if (this.#context.state === 'running') {
            this.#isLocked = false;
        }
    }
}
