/**
 * Shuffles an array in-place using the <PERSON><PERSON><PERSON> algorithm.
 *
 * @param array The array to shuffle.
 */
export function shuffleInPlace<T extends unknown[]>(array: T): T {
    for (let i = array.length - 1; i > 0; i--) {
        const j = Math.floor(Math.random() * (i + 1));
        const temp = array[i];
        array[i] = array[j];
        array[j] = temp;
    }
    return array;
}

/**
 * Creates a copy of the array and shuffles it using the <PERSON><PERSON>Yates algorithm.
 *
 * @param array The array to copy and shuffle.
 */
export function shuffle<T extends unknown[]>(array: T): T {
    return shuffleInPlace(array.slice()) as T;
}
