import type { Meta, StoryObj } from '@storybook/html';
import { fn } from '@storybook/test';
import { html } from '../../component';
import { namespaced } from '../../utilities/classes';
import { Radio, type RadioGroupProps, type RadioProps } from './radio';

const meta: Meta<RadioProps> = {
    title: 'Modules/Radio',
    globals: { backgrounds: { value: 'dark' } },
};
export default meta;

export const Default: StoryObj = (props: RadioProps) => {
    return html('div')(
        {
            className: namespaced('l-box', 'u-gap--xl'),
        },
        Radio(props),
    ).render();
};

Default.argTypes = {
    label: { control: 'text' },
    isChecked: { control: 'boolean' },
    disabled: { control: 'boolean' },
};
Default.args = {
    label: 'Default Option',
    isChecked: false,
    disabled: false,
};

export const Group: StoryObj = (props: RadioGroupProps<string>) => {
    return Radio.Group<string>({
        ...props,
        value: 'option2',
        items: [
            {
                value: 'option1',
                label: 'Option 1',
            },
            {
                value: 'option2',
                label: 'Option 2',
            },
            {
                value: 'option3',
                label: 'Option 3',
            },
            {
                value: 'option4',
                label: 'Option 4',
            },
        ],
    }).render();
};
Group.storyName = 'Radio Group';
Group.argTypes = {
    disabled: { control: 'boolean' },
};
Group.args = {
    disabled: false,
    onChange: fn(),
};
