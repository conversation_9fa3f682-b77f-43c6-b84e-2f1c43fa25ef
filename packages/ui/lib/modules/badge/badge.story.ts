import type { <PERSON>a, StoryObj } from '@storybook/html';
import { fn } from '@storybook/test';
import { render } from '../../../.storybook/render';
import { Badge, type BadgeProps, type BadgeTickerItem } from './badge';
import { html } from '../../component';

const meta: Meta = {
    title: 'Modules/Badge',
};
export default meta;

export const Default: StoryObj = (props: BadgeProps) => {
    return Badge(props).render();
};
Default.argTypes = {
    text: { control: 'text' },
    type: { control: 'radio', options: ['success', 'fail'] },
};
Default.args = {
    text: '1.00x',
    type: 'success',
};

export const Ticker: StoryObj = () => {
    return render(({ onStoryUnmount, store: { signal } }) => {
        const [items, setItems] = signal<BadgeTickerItem[]>([
            {
                id: Date.now().toString(),
                text: (Math.random() * 100).toFixed(2) + 'x',
                type: 'success',
                onClick: fn(),
            },
        ]);

        const interval = setInterval(() => {
            setItems((prev) =>
                prev
                    .concat([
                        {
                            id: Date.now().toString(),
                            text: (Math.random() * 100).toFixed(2) + 'x',
                            type: Math.random() > 0.5 ? 'success' : 'fail',
                            onClick: fn(),
                        },
                    ])
                    .slice(-5),
            );
        }, 3000);

        onStoryUnmount(() => {
            clearInterval(interval);
        });

        return Badge.Ticker({ items });
    });
};
Ticker.storyName = 'Ticker';

export const VerticalTicker: StoryObj = () => {
    return render(({ onStoryUnmount, store: { signal } }) => {
        const plinkoVariants: Pick<BadgeTickerItem, 'color'>[] = [
            { color: '#c60000' },
            { color: ' #e41b1b' },
            { color: '#ef4444' },
            { color: '#f56d0b' },
            { color: ' #f59e0b' },
            { color: '#fbbf24' },
            { color: ' #fcd34d' },
            { color: ' #fde68a' },
            { color: ' #fef3c7' },
        ];

        const [items, setItems] = signal<BadgeTickerItem[]>([
            {
                id: Date.now().toString(),
                text: (Math.random() * 100).toFixed(2) + 'x',
                color: '#e41b1b',
                pill: true,
                onClick: fn(),
            },
        ]);

        const interval = setInterval(() => {
            const variant = plinkoVariants[Math.floor(Math.random() * plinkoVariants.length)];

            setItems((prev) =>
                prev
                    .concat([
                        {
                            id: Date.now().toString(),
                            text: (Math.random() * 100).toFixed(2) + 'x',
                            ...variant,
                            pill: true,
                            onClick: fn(),
                        },
                    ])
                    .slice(-10),
            );
        }, 2000);

        onStoryUnmount(() => clearInterval(interval));

        return Badge.Ticker({ items, orientation: 'vertical' });
    });
};
VerticalTicker.storyName = 'Ticker – Vertical';

export const PlinkoColors: StoryObj = () => {
    return render(() => {
        return html('div')(
            {
                css: {
                    display: 'flex',
                    'flex-direction': 'column',
                    gap: '1rem',
                    'align-items': 'flex-end',
                },
            },
            ...[
                { text: '43x', color: '#c60000' },
                { text: '1.1x', color: ' #e41b1b' },
                { text: '11x', color: '#ef4444' },
                { text: '2.5x', color: '#f56d0b' },
                { text: '8.12x', color: ' #f59e0b' },
                { text: '55x', color: '#fbbf24' },
                { text: '2x', color: ' #fcd34d' },
                { text: '10x', color: ' #fde68a' },
                { text: '999x', color: ' #fef3c7' },
            ].map(({ text, color }) => Badge({ text, color, pill: true })),
        );
    });
};
PlinkoColors.storyName = 'Plinko Color Variants';
