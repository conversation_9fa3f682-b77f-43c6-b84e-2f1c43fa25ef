import { defineComponent, html, type Component, type Props } from '../../component';
import { mergeClasses } from '../../utilities/classes';
import { BadgeTicker } from './ticker';

export type { BadgeTickerProps, BadgeTickerItem } from './ticker';

export interface BadgeProps extends Props<HTMLDivElement> {
    /**
     * The text to display inside the badge.
     */
    readonly text: string;

    /**
     * Badge type - can be success (green) or fail (red).
     */
    readonly type?: 'success' | 'fail';

    /**
     * Custom color variant – overrides `type` if provided.
     */
    readonly color?: string;

    /**
     * Renders the badge with a pill shape.
     */
    readonly pill?: boolean;
}

const badge = defineComponent(
    'Badge',
    () =>
        ({
            text,
            type,
            color,
            pill,
            className,
            ...props
        }: BadgeProps): Component<HTMLDivElement> => {
            return html('div')({
                ...props,
                css: {
                    ...(color ? { 'background-color': color } : {}),
                    ...(color === '#c60000' ? { color: '#FFF' } : {}),
                },
                className: mergeClasses(className, {
                    'm-badge': true,
                    'm-badge--pill': !!pill,
                    ...(color ? {} : { [`m-badge--${type}`]: true }),
                }),
                textContent: text,
            });
        },
);

export const Badge: typeof badge & {
    readonly Ticker: typeof BadgeTicker;
} = Object.assign(badge, {
    Ticker: BadgeTicker,
});
