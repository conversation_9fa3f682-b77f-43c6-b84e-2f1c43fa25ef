import type { BetAmountLimits, <PERSON><PERSON><PERSON><PERSON>ame, <PERSON>Error, ReadyState } from '@monkey-tilt/client';

export type BetOutcome = 'win' | 'loss';
export type PlinkoRisk = 0 | 1 | 2;

export interface FinalState {
    readonly metadata: {
        readonly rtp: number;
    };
    readonly bucket: number;
    readonly risk: PlinkoRisk;
    readonly number_of_rows: number;
}

export interface AutobetState {
    readonly autobet_limit: number;
    readonly autobet_count: number;
    readonly on_win_reset: boolean;
    readonly on_win: string;
    readonly on_loss_reset: boolean;
    readonly on_loss: string;
    readonly stop_on_profit: string;
    readonly stop_on_loss: string;
    readonly autobet_cumulative_payout: string;
}

export interface PlinkoBetAmountLimits extends BetAmountLimits {
    readonly bet: {
        readonly min: number;
        readonly max: number;
        readonly max_payout: number;
    };
}

export type PlinkoMultipliersChancesMap = {
    [rtp: string]: {
        [risk: string]: {
            [number_of_rows: string]: [multiplier: string, chance: string][];
        };
    };
};

/**
 * Data associated with a ball.
 */
export interface BallData {
    /**
     * The unique identifier for the ball.
     */
    readonly id: string;
    /**
     * The risk level associated with the ball.
     */
    readonly risk: PlinkoRisk;
    /**
     * The number of rows associated with board size
     */
    readonly number_of_rows: number;
    /**
     * The bucket number the ball will fall into.
     */
    readonly bucketNumber: number;
    /**
     * The payout associated with the ball.
     */
    readonly payout: string;
}

export interface PlinkoState extends Partial<AutobetState>, Partial<FinalState> {
    readonly action?: GameActionName;

    readonly balance: number;
    readonly bet_limits: PlinkoBetAmountLimits;
    readonly max_multiplier: number;

    readonly error: GameError | null;
    readonly isLoading: boolean;
    readonly readyState: ReadyState;
    readonly isAuthenticated: boolean;

    readonly round_id: string;

    readonly next_actions: ReadonlyArray<GameActionName>;

    readonly isAutobetActive: boolean;

    readonly bet_amount?: string;

    readonly dropped_ball?: BallData;

    readonly round_closed?: boolean;
    readonly total_payout?: string;

    readonly rtp: number;
    readonly multipliersChancesMap: PlinkoMultipliersChancesMap;
}

export type RowCount = 8 | 9 | 10 | 11 | 12 | 13 | 14 | 15 | 16;

export type PlinkoStats = {
    [R in RowCount]: Map<number, number>;
};

export const DEFAULT_RTP = 0.98;
export const DEFAULT_MULTIPLIERS_CHANCES_CONFIG: PlinkoMultipliersChancesMap = {
    '0.98': {
        '0': {
            '8': [
                ['5.60', '0.003906250'],
                ['1.40', '0.031250000'],
                ['1.25', '0.109375000'],
                ['1.00', '0.218750000'],
                ['0.50', '0.273437500'],
                ['1.00', '0.218750000'],
                ['1.25', '0.109375000'],
                ['1.40', '0.031250000'],
                ['5.60', '0.003906250'],
            ],
            '9': [
                ['5.54', '0.001953125'],
                ['1.98', '0.017578125'],
                ['1.58', '0.070312500'],
                ['0.99', '0.164062500'],
                ['0.69', '0.246093750'],
                ['0.69', '0.246093750'],
                ['0.99', '0.164062500'],
                ['1.58', '0.070312500'],
                ['1.98', '0.017578125'],
                ['5.54', '0.001953125'],
            ],
            '10': [
                ['8.81', '0.000976562'],
                ['2.97', '0.009765625'],
                ['1.39', '0.043945312'],
                ['1.09', '0.117187500'],
                ['0.99', '0.205078125'],
                ['0.49', '0.246093750'],
                ['0.99', '0.205078125'],
                ['1.09', '0.117187500'],
                ['1.39', '0.043945312'],
                ['2.97', '0.009765625'],
                ['8.81', '0.000976562'],
            ],
            '11': [
                ['8.32', '0.000488281'],
                ['2.97', '0.005371094'],
                ['1.88', '0.026855469'],
                ['1.29', '0.080566406'],
                ['0.99', '0.161132812'],
                ['0.69', '0.225585938'],
                ['0.69', '0.225585938'],
                ['0.99', '0.161132812'],
                ['1.29', '0.080566406'],
                ['1.88', '0.026855469'],
                ['2.97', '0.005371094'],
                ['8.32', '0.000488281'],
            ],
            '12': [
                ['9.90', '0.000244141'],
                ['2.97', '0.002929688'],
                ['1.58', '0.016113281'],
                ['1.39', '0.053710938'],
                ['1.09', '0.120849609'],
                ['0.99', '0.193359375'],
                ['0.49', '0.225585938'],
                ['0.99', '0.193359375'],
                ['1.09', '0.120849609'],
                ['1.39', '0.053710938'],
                ['1.58', '0.016113281'],
                ['2.97', '0.002929688'],
                ['9.90', '0.000244141'],
            ],
            '13': [
                ['8.02', '0.000122070'],
                ['3.96', '0.001586914'],
                ['2.97', '0.009521484'],
                ['1.88', '0.034912109'],
                ['1.19', '0.087280273'],
                ['0.89', '0.157104492'],
                ['0.69', '0.209472656'],
                ['0.69', '0.209472656'],
                ['0.89', '0.157104492'],
                ['1.19', '0.087280273'],
                ['1.88', '0.034912109'],
                ['2.97', '0.009521484'],
                ['3.96', '0.001586914'],
                ['8.02', '0.000122070'],
            ],
            '14': [
                ['7.03', '0.000061035'],
                ['3.96', '0.000854492'],
                ['1.88', '0.005554199'],
                ['1.39', '0.022216797'],
                ['1.29', '0.061096191'],
                ['1.09', '0.122192383'],
                ['0.99', '0.183288574'],
                ['0.49', '0.209472656'],
                ['0.99', '0.183288574'],
                ['1.09', '0.122192383'],
                ['1.29', '0.061096191'],
                ['1.39', '0.022216797'],
                ['1.88', '0.005554199'],
                ['3.96', '0.000854492'],
                ['7.03', '0.000061035'],
            ],
            '15': [
                ['14.85', '0.000030518'],
                ['7.92', '0.000457764'],
                ['2.97', '0.003204346'],
                ['1.98', '0.013885498'],
                ['1.48', '0.041656494'],
                ['1.09', '0.091644287'],
                ['0.99', '0.152740479'],
                ['0.69', '0.196380615'],
                ['0.69', '0.196380615'],
                ['0.99', '0.152740479'],
                ['1.09', '0.091644287'],
                ['1.48', '0.041656494'],
                ['1.98', '0.013885498'],
                ['2.97', '0.003204346'],
                ['7.92', '0.000457764'],
                ['14.85', '0.000030518'],
            ],
            '16': [
                ['15.84', '0.000015259'],
                ['8.91', '0.000244141'],
                ['1.98', '0.001831055'],
                ['1.39', '0.008544922'],
                ['1.39', '0.027770996'],
                ['1.19', '0.066650391'],
                ['1.09', '0.122192383'],
                ['0.99', '0.174560547'],
                ['0.49', '0.196380615'],
                ['0.99', '0.174560547'],
                ['1.09', '0.122192383'],
                ['1.19', '0.066650391'],
                ['1.39', '0.027770996'],
                ['1.39', '0.008544922'],
                ['1.98', '0.001831055'],
                ['8.91', '0.000244141'],
                ['15.84', '0.000015259'],
            ],
        },
        '1': {
            '8': [
                ['12.87', '0.003906250'],
                ['2.97', '0.031250000'],
                ['1.29', '0.109375000'],
                ['0.69', '0.218750000'],
                ['0.40', '0.273437500'],
                ['0.69', '0.218750000'],
                ['1.29', '0.109375000'],
                ['2.97', '0.031250000'],
                ['12.87', '0.003906250'],
            ],
            '9': [
                ['17.82', '0.001953125'],
                ['3.96', '0.017578125'],
                ['1.68', '0.070312500'],
                ['0.89', '0.164062500'],
                ['0.49', '0.246093750'],
                ['0.49', '0.246093750'],
                ['0.89', '0.164062500'],
                ['1.68', '0.070312500'],
                ['3.96', '0.017578125'],
                ['17.82', '0.001953125'],
            ],
            '10': [
                ['21.78', '0.000976562'],
                ['4.95', '0.009765625'],
                ['1.98', '0.043945312'],
                ['1.39', '0.117187500'],
                ['0.59', '0.205078125'],
                ['0.40', '0.246093750'],
                ['0.59', '0.205078125'],
                ['1.39', '0.117187500'],
                ['1.98', '0.043945312'],
                ['4.95', '0.009765625'],
                ['21.78', '0.000976562'],
            ],
            '11': [
                ['23.76', '0.000488281'],
                ['5.94', '0.005371094'],
                ['2.97', '0.026855469'],
                ['1.78', '0.080566406'],
                ['0.69', '0.161132812'],
                ['0.49', '0.225585938'],
                ['0.49', '0.225585938'],
                ['0.69', '0.161132812'],
                ['1.78', '0.080566406'],
                ['2.97', '0.026855469'],
                ['5.94', '0.005371094'],
                ['23.76', '0.000488281'],
            ],
            '12': [
                ['32.67', '0.000244141'],
                ['10.89', '0.002929688'],
                ['3.96', '0.016113281'],
                ['1.98', '0.053710938'],
                ['1.09', '0.120849609'],
                ['0.59', '0.193359375'],
                ['0.30', '0.225585938'],
                ['0.59', '0.193359375'],
                ['1.09', '0.120849609'],
                ['1.98', '0.053710938'],
                ['3.96', '0.016113281'],
                ['10.89', '0.002929688'],
                ['32.67', '0.000244141'],
            ],
            '13': [
                ['42.50', '0.000122070'],
                ['12.80', '0.001586914'],
                ['5.90', '0.009521484'],
                ['2.90', '0.034912109'],
                ['1.30', '0.087280273'],
                ['0.70', '0.157104492'],
                ['0.40', '0.209472656'],
                ['0.40', '0.209472656'],
                ['0.70', '0.157104492'],
                ['1.30', '0.087280273'],
                ['2.90', '0.034912109'],
                ['5.90', '0.009521484'],
                ['12.80', '0.001586914'],
                ['42.50', '0.000122070'],
            ],
            '14': [
                ['57.41', '0.000061035'],
                ['14.85', '0.000854492'],
                ['6.93', '0.005554199'],
                ['3.96', '0.022216797'],
                ['1.88', '0.061096191'],
                ['0.99', '0.122192383'],
                ['0.49', '0.183288574'],
                ['0.20', '0.209472656'],
                ['0.49', '0.183288574'],
                ['0.99', '0.122192383'],
                ['1.88', '0.061096191'],
                ['3.96', '0.022216797'],
                ['6.93', '0.005554199'],
                ['14.85', '0.000854492'],
                ['57.41', '0.000061035'],
            ],
            '15': [
                ['87.11', '0.000030518'],
                ['17.82', '0.000457764'],
                ['10.89', '0.003204346'],
                ['4.95', '0.013885498'],
                ['2.97', '0.041656494'],
                ['1.29', '0.091644287'],
                ['0.49', '0.152740479'],
                ['0.30', '0.196380615'],
                ['0.30', '0.196380615'],
                ['0.49', '0.152740479'],
                ['1.29', '0.091644287'],
                ['2.97', '0.041656494'],
                ['4.95', '0.013885498'],
                ['10.89', '0.003204346'],
                ['17.82', '0.000457764'],
                ['87.11', '0.000030518'],
            ],
            '16': [
                ['108.89', '0.000015259'],
                ['40.59', '0.000244141'],
                ['9.90', '0.001831055'],
                ['4.95', '0.008544922'],
                ['2.97', '0.027770996'],
                ['1.48', '0.066650391'],
                ['0.99', '0.122192383'],
                ['0.49', '0.174560547'],
                ['0.30', '0.196380615'],
                ['0.49', '0.174560547'],
                ['0.99', '0.122192383'],
                ['1.48', '0.066650391'],
                ['2.97', '0.027770996'],
                ['4.95', '0.008544922'],
                ['9.90', '0.001831055'],
                ['40.59', '0.000244141'],
                ['108.89', '0.000015259'],
            ],
        },
        '2': {
            '8': [
                ['28.00', '0.003906250'],
                ['4.00', '0.031250000'],
                ['1.45', '0.109375000'],
                ['0.30', '0.218750000'],
                ['0.20', '0.273437500'],
                ['0.30', '0.218750000'],
                ['1.45', '0.109375000'],
                ['4.00', '0.031250000'],
                ['28.00', '0.003906250'],
            ],
            '9': [
                ['42.00', '0.001953125'],
                ['6.90', '0.017578125'],
                ['2.00', '0.070312500'],
                ['0.58', '0.164062500'],
                ['0.20', '0.246093750'],
                ['0.20', '0.246093750'],
                ['0.58', '0.164062500'],
                ['2.00', '0.070312500'],
                ['6.90', '0.017578125'],
                ['42.00', '0.001953125'],
            ],
            '10': [
                ['75.00', '0.000976562'],
                ['9.55', '0.009765625'],
                ['2.95', '0.043945312'],
                ['0.90', '0.117187500'],
                ['0.30', '0.205078125'],
                ['0.20', '0.246093750'],
                ['0.30', '0.205078125'],
                ['0.90', '0.117187500'],
                ['2.95', '0.043945312'],
                ['9.55', '0.009765625'],
                ['75.00', '0.000976562'],
            ],
            '11': [
                ['118.00', '0.000488281'],
                ['13.85', '0.005371094'],
                ['5.15', '0.026855469'],
                ['1.35', '0.080566406'],
                ['0.40', '0.161132812'],
                ['0.20', '0.225585938'],
                ['0.20', '0.225585938'],
                ['0.40', '0.161132812'],
                ['1.35', '0.080566406'],
                ['5.15', '0.026855469'],
                ['13.85', '0.005371094'],
                ['118.00', '0.000488281'],
            ],
            '12': [
                ['168.00', '0.000244141'],
                ['23.75', '0.002929688'],
                ['8.00', '0.016113281'],
                ['2.00', '0.053710938'],
                ['0.66', '0.120849609'],
                ['0.20', '0.193359375'],
                ['0.20', '0.225585938'],
                ['0.20', '0.193359375'],
                ['0.66', '0.120849609'],
                ['2.00', '0.053710938'],
                ['8.00', '0.016113281'],
                ['23.75', '0.002929688'],
                ['168.00', '0.000244141'],
            ],
            '13': [
                ['260.00', '0.000122070'],
                ['36.00', '0.001586914'],
                ['10.50', '0.009521484'],
                ['3.95', '0.034912109'],
                ['1.00', '0.087280273'],
                ['0.20', '0.157104492'],
                ['0.20', '0.209472656'],
                ['0.20', '0.209472656'],
                ['0.20', '0.157104492'],
                ['1.00', '0.087280273'],
                ['3.95', '0.034912109'],
                ['10.50', '0.009521484'],
                ['36.00', '0.001586914'],
                ['260.00', '0.000122070'],
            ],
            '14': [
                ['415.00', '0.000061035'],
                ['55.50', '0.000854492'],
                ['17.90', '0.005554199'],
                ['4.95', '0.022216797'],
                ['1.85', '0.061096191'],
                ['0.30', '0.122192383'],
                ['0.20', '0.183288574'],
                ['0.20', '0.209472656'],
                ['0.20', '0.183288574'],
                ['0.30', '0.122192383'],
                ['1.85', '0.061096191'],
                ['4.95', '0.022216797'],
                ['17.90', '0.005554199'],
                ['55.50', '0.000854492'],
                ['415.00', '0.000061035'],
            ],
            '15': [
                ['613.00', '0.000030518'],
                ['82.15', '0.000457764'],
                ['26.70', '0.003204346'],
                ['7.90', '0.013885498'],
                ['2.95', '0.041656494'],
                ['0.50', '0.091644287'],
                ['0.20', '0.152740479'],
                ['0.20', '0.196380615'],
                ['0.20', '0.196380615'],
                ['0.20', '0.152740479'],
                ['0.50', '0.091644287'],
                ['2.95', '0.041656494'],
                ['7.90', '0.013885498'],
                ['26.70', '0.003204346'],
                ['82.15', '0.000457764'],
                ['613.00', '0.000030518'],
            ],
            '16': [
                ['990.00', '0.000015259'],
                ['128.00', '0.000244141'],
                ['25.70', '0.001831055'],
                ['8.90', '0.008544922'],
                ['3.95', '0.027770996'],
                ['1.98', '0.066650391'],
                ['0.20', '0.122192383'],
                ['0.20', '0.174560547'],
                ['0.20', '0.196380615'],
                ['0.20', '0.174560547'],
                ['0.20', '0.122192383'],
                ['1.98', '0.066650391'],
                ['3.95', '0.027770996'],
                ['8.90', '0.008544922'],
                ['25.70', '0.001831055'],
                ['128.00', '0.000244141'],
                ['990.00', '0.000015259'],
            ],
        },
    },
};

export function createInitialPlinkoStats(): PlinkoStats {
    const stats = {} as PlinkoStats;

    for (let rows = 8 as RowCount; rows <= 16; rows++) {
        const map = new Map<number, number>();
        for (let i = 0; i <= rows; i++) {
            map.set(i, 0);
        }
        stats[rows] = map;
    }

    return stats;
}
