interface Params {
    multiplier: number;
    win_chance: number;
    dice_roll: number;
}

export const MULTIPLIER_PRECISION = 4;
export const WIN_CHANCE_PRECISION = 4;
export const DICE_ROLL_PRECISION = 2;

export const DEFAULT_RTP = 0.99;
export const MIN_MULTIPLIER = 1.01;
export const INITIAL_MULTIPLIER = 2;

export function diceRollLimits(rtp = DEFAULT_RTP): { min: number; max: number } {
    const max = Math.floor(calculateParams({ multiplier: MIN_MULTIPLIER }, { rtp }).dice_roll);
    return {
        min: 100 - max,
        max,
    };
}

export function calculateParams(
    input: { dice_roll: number } | { multiplier: number } | { win_chance: number },
    {
        rtp: rtpFactor = DEFAULT_RTP,
        roll_over = false,
    }: {
        rtp?: number;
        roll_over?: boolean;
    } = {},
): Params {
    let { multiplier, win_chance, dice_roll } = input as Partial<Params>;

    const rtp = rtpFactor * 100;

    if (multiplier !== undefined) {
        win_chance = rtp / multiplier;
    } else if (win_chance !== undefined) {
        multiplier = rtp / win_chance;
    } else {
        dice_roll ??= calculateParams(
            { multiplier: INITIAL_MULTIPLIER },
            { rtp, roll_over },
        ).dice_roll;

        win_chance = roll_over ? 100 - dice_roll : dice_roll;
        multiplier = rtp / win_chance;
    }

    if (dice_roll === undefined) {
        dice_roll = roll_over ? 100 - win_chance : win_chance;
    }

    return {
        multiplier: round(multiplier, MULTIPLIER_PRECISION),
        win_chance: round(win_chance, WIN_CHANCE_PRECISION),
        dice_roll: round(dice_roll, DICE_ROLL_PRECISION),
    };
}

function round(value: number, decimals: number): number {
    const factor = Math.pow(10, decimals);
    return Math.round(value * factor) / factor;
}

export function possibleWinAmount({
    multiplier,
    bet_amount,
}: {
    multiplier: number;
    bet_amount: number;
}): number {
    return bet_amount * multiplier;
}
