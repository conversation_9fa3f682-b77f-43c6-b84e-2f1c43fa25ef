# Monkey Tilt Original Games

This is a monorepo for the Monkey Tilt Original Games Frontend project.

## Getting Started

 - Install [Node.js](https://nodejs.org/en/download/), check the required version in the [.node-version](.node-version) file.
 - Install [PNPM](https://pnpm.io/installation) globally, or via corepack (preferred).
 - Install dependencies with `pnpm install`.
 - Test if everything is working with `pnpm build` from the root directory.