{"name": "@monkey-tilt/state", "version": "0.1.0-dev", "description": "Monkey Tilt Original Games State management library", "private": true, "type": "module", "license": "UNLICENSED", "author": "Monkey Tilt", "contributors": ["Alek<PERSON><PERSON> <<EMAIL>>"], "files": ["dist"], "module": "./dist/state.es.js", "main": "./dist/state.umd.js", "types": "./dist/index.d.ts", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/state.es.js", "require": "./dist/state.umd.js"}}, "scripts": {"build": "vite build", "lint": "eslint ./lib", "typecheck": "tsc --noEmit", "test": "vitest run", "test:watch": "vitest", "test:coverage": "vitest run --coverage", "test:ui": "vitest --ui"}, "peerDependencies": {"@monkey-tilt/utils": "workspace:*"}, "devDependencies": {"@eslint/js": "catalog:eslint", "@monkey-tilt/utils": "workspace:*", "@types/node": "catalog:typescript", "@vitest/coverage-v8": "catalog:vitest", "@vitest/ui": "catalog:vitest", "eslint": "catalog:eslint", "typescript-eslint-language-service": "catalog:eslint", "typescript-eslint": "catalog:eslint", "typescript": "catalog:typescript", "vite-plugin-dts": "catalog:vite", "vite": "catalog:vite", "vitest": "catalog:vitest"}, "packageManager": "pnpm@9.15.9+sha512.68046141893c66fad01c079231128e9afb89ef87e2691d69e4d40eee228988295fd4682181bae55b58418c3a253bde65a505ec7c5f9403ece5cc3cd37dcf2531"}