import type { <PERSON><PERSON>, StoryObj } from '@storybook/html';
import { Bucket, BucketPallette, type BucketProps } from './bucket';

const meta: Meta = {
    title: 'Modules/Bucket',
};
export default meta;

export const Standalone: StoryObj<BucketProps> = (props: BucketProps) => {
    return Bucket(props).render();
};

Standalone.argTypes = {
    value: {
        control: { type: 'text' },
    },
    color: {
        control: 'select',
        options: BucketPallette,
    },
};

Standalone.args = {
    value: '5.6',
    color: BucketPallette[0]!,
    variant: 'standalone',
};

Standalone.storyName = 'Standalone';

export const Default: StoryObj<BucketProps> = (props: BucketProps) => {
    return Bucket(props).render();
};

Default.argTypes = {
    value: {
        control: { type: 'text' },
    },
    color: {
        control: 'select',
        options: BucketPallette,
    },
};

Default.args = {
    value: '5.6',
    color: BucketPallette[0]!,
};

Default.storyName = 'Default';

import { BucketRow } from './bucket';

export const Row: StoryObj = {
    render: ({ count = 9 }: { count?: number }) => {
        const mid = Math.floor(count / 2);
        return BucketRow({
            multipliers: Array.from({ length: count }, (_, i) =>
                (mid - Math.min(i, count - i - 1)).toFixed(1),
            ),
            controls: ({ hitBucket }) => {
                setTimeout(() => {
                    for (let i = 0; i < count; i++) {
                        setTimeout(() => hitBucket(i), i * 1000);
                        if (i % 2) {
                            setTimeout(() => hitBucket(i), i * 1100);
                        }
                        if (i % 3 === 0) {
                            setTimeout(() => hitBucket(i), i * 1200);
                        }
                    }
                }, 1000);
            },
            onBucketFocus: (index) => {
                console.log(`Bucket ${index} focused`);
            },
            onBlur: () => {
                console.log('Row blurred');
            },
        }).render();
    },
};

Row.argTypes = {
    count: {
        control: { type: 'range', min: 9, max: 17, step: 1 },
    },
};
Row.args = {
    count: 9,
};

Row.storyName = 'Row';
