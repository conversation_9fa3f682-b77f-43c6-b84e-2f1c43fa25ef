name: Deploy to S3/CloudFront Prod

on:
  push:
    branches:
      - master
  workflow_dispatch:

env:
  AWS_REGION: eu-central-1
  S3_BUCKET: og-static-prod
  CLOUDFRONT_DISTRIBUTION: E3PQBZV5CHB7IZ
  ENVIRONMENT: prod
  CUSTOM_DOMAIN: og-static.monkeytilt.com

permissions:
  id-token: write
  contents: read

jobs:
  deploy:
    name: Build and Deploy to S3/CloudFront
    runs-on: ubuntu-latest
    environment: prod

    steps:
    - name: Checkout
      uses: actions/checkout@v4

    - name: Install pnpm
      uses: pnpm/action-setup@v4
      with:
        run_install: false

    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version-file: '.node-version'
        cache: 'pnpm'

    - name: Install dependencies
      run: |
        # Apply the css-mqpacker fix
        if [ -f "packages/ui/package.json" ]; then
          sed -i 's/"@hail2u\/css-mqpacker": "catalog:postcss"/"@hail2u\/css-mqpacker": "github:hail2u\/node-css-mqpacker#v9.0.1"/g' packages/ui/package.json
        fi
        pnpm install --no-frozen-lockfile

    - name: Build project
      run: pnpm build

    - name: Prepare distribution files
      run: |
        mkdir -p dist
        cp -r sdk/dist/* dist/
        
        # Create standalone HTML files
        mkdir -p dist/standalone
        for game in games/*; do
          if [ -f $game/deploy/standalone.html ]; then
            cp $game/deploy/standalone.html dist/standalone/$(basename $game).html
          fi
        done

    - name: Configure AWS credentials
      uses: aws-actions/configure-aws-credentials@v4
      with:
        aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
        aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
        aws-region: ${{ env.AWS_REGION }}

    - name: Upload JS files to S3
      run: |
        aws s3 sync dist/ s3://${{ env.S3_BUCKET }}/ \
          --region ${{ env.AWS_REGION }} \
          --exclude="*" \
          --include="*.js" \
          --metadata-directive REPLACE \
          --cache-control "public, max-age=31536000, immutable" \
          --content-type "application/javascript"

    - name: Upload JSON files to S3
      run: |
        aws s3 sync dist/ s3://${{ env.S3_BUCKET }}/ \
          --region ${{ env.AWS_REGION }} \
          --exclude="*" \
          --include="*.json" \
          --metadata-directive REPLACE \
          --cache-control "public, max-age=86400" \
          --content-type "application/json"

    - name: Upload HTML files to S3
      run: |
        aws s3 sync dist/ s3://${{ env.S3_BUCKET }}/ \
          --region ${{ env.AWS_REGION }} \
          --exclude="*" \
          --include="*.html" \
          --metadata-directive REPLACE \
          --cache-control "public, max-age=3600" \
          --content-type "text/html"

    - name: Upload other files to S3
      run: |
        aws s3 sync dist/ s3://${{ env.S3_BUCKET }}/ \
          --region ${{ env.AWS_REGION }} \
          --exclude="*.js" \
          --exclude="*.html" \
          --exclude="*.json" \
          --metadata-directive REPLACE \
          --cache-control "public, max-age=86400"

    - name: Create CloudFront invalidation
      run: |
        INVALIDATION_ID=$(aws cloudfront create-invalidation \
          --distribution-id ${{ env.CLOUDFRONT_DISTRIBUTION }} \
          --paths "/*" \
          --query 'Invalidation.Id' \
          --output text)
        echo "INVALIDATION_ID=$INVALIDATION_ID" >> $GITHUB_ENV

    - name: Purge Cloudflare cache
      run: |
        if [ -n "${{ secrets.CLOUDFLARE_API_TOKEN }}" ] && [ -n "${{ secrets.CLOUDFLARE_ZONE_ID_COM }}" ]; then
          echo "Purging Cloudflare cache for ${{ env.CUSTOM_DOMAIN }}..."
          response=$(curl -s -X POST "https://api.cloudflare.com/client/v4/zones/${{ secrets.CLOUDFLARE_ZONE_ID_COM }}/purge_cache" \
            -H "Content-Type: application/json" \
            -H "Authorization: Bearer ${{ secrets.CLOUDFLARE_API_TOKEN }}" \
            --data '{"files":["https://${{ env.CUSTOM_DOMAIN }}/og.js","https://${{ env.CUSTOM_DOMAIN }}/index.html","https://${{ env.CUSTOM_DOMAIN }}/*"]}')
          
          if echo "$response" | grep -q '"success":true'; then
            echo "✅ Cloudflare cache purged successfully"
          else
            echo "⚠️ Cloudflare cache purge may have failed: $response"
          fi
        else
          echo "⚠️ Cloudflare credentials not found, skipping cache purge"
        fi

    - name: Clean up
      run: rm -rf dist

    - name: Deployment summary
      run: |
        echo "🎉 Deployment completed successfully!"
        echo "📍 CloudFront: https://d3hb5vvfdo7tpj.cloudfront.net"
        echo "🌐 Custom domain: https://${{ env.CUSTOM_DOMAIN }}"
        echo ""
        echo "💾 Cache invalidations:"
        echo "✅ CloudFront: ${{ env.INVALIDATION_ID }}"
        if [ -n "${{ secrets.CLOUDFLARE_API_TOKEN }}" ]; then
          echo "✅ Cloudflare: Purged"
        else
          echo "⚠️ Cloudflare: Skipped (no credentials)"
        fi
        echo ""
        echo "🧪 Test CORS:"
        echo "curl -H 'Origin: https://frontend.monkeytilt.com' https://${{ env.CUSTOM_DOMAIN }}/og.js" 