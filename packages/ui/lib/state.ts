import type { Signal, SignalReader, Store } from '@monkey-tilt/state';

export type {
    <PERSON><PERSON><PERSON>un<PERSON>,
    EffectConstructor,
    EffectContext,
    EffectOptions,
    LocalStorageRepositoryConfiguration,
    MemoConstructor,
    Repository,
    Signal,
    SignalConstructor,
    SignalOptions,
    SignalReader,
    SignalUpdater,
    Store,
    StoreSnapshot,
    UntrackedReader,
} from '@monkey-tilt/state';

export { createStore, LocalStorageRepository } from '@monkey-tilt/state';

export interface PreviousMemo<T> {
    readonly previous: T;
    readonly current: T;
}

export interface UIStateStore extends Store {
    readonly previousMemo: <T>(this: void, value: SignalReader<T>) => SignalReader<PreviousMemo<T>>;
    readonly read: <T>(this: void, value: Reactive<T>) => T;
    readonly onPropChange: <T extends unknown[]>(
        this: void,
        ...args: [
            ...{ [K in keyof T]: T[K] extends () => infer U ? Reactive<U> : Reactive<T[K]> },
            (...values: { [K in keyof T]: T[K] extends () => infer U ? U : T[K] }) => void,
        ]
    ) => void;
}

export type Reactive<T> = T | SignalReader<T> | Signal<T>;

export function extendStore(store: Store): UIStateStore {
    const { effect, signal, untracked } = store;
    const read = <T>(value: Reactive<T>): T => {
        if (isSignalReader(value)) {
            return value();
        }
        if (isSignal(value)) {
            return value.read();
        }
        return value;
    };

    return {
        ...store,
        previousMemo: <T>(value: SignalReader<T>): SignalReader<PreviousMemo<T>> => {
            const current = value();
            const [get, set] = signal({ current, previous: current });

            effect(() => {
                set({
                    current: value(),
                    previous: untracked(get).current,
                });
            });

            return get;
        },
        read,
        onPropChange: (...values) => {
            if (values.length < 2) {
                return;
            }

            const signals = values.slice(0, -1) as Reactive<unknown>[];
            const callback = values[values.length - 1] as (...values: unknown[]) => void;

            if (typeof callback !== 'function') {
                return;
            }

            if (signals.every((value) => !isSignalReader(value) && !isSignal(value))) {
                callback(...signals);
            } else {
                effect(() => {
                    callback(...signals.map(read));
                });
            }
        },
    };
}

function isSignalReader<T>(value: T | SignalReader<T>): value is SignalReader<T> {
    return typeof value === 'function';
}

function isSignal<T>(value: T | Signal<T>): value is Signal<T> {
    return (
        value &&
        typeof value === 'object' &&
        'read' in value &&
        typeof value.read === 'function' &&
        'update' in value &&
        typeof value.update === 'function'
    );
}
