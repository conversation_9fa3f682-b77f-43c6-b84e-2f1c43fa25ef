export class UnmountObserver {
    #nodes: Map<Node, AbortController> = new Map();
    #abortSignal: AbortSignal;
    #isDisconnected = false;

    public readonly disconnect: () => void;

    public constructor(root: HTMLElement | SVGElement) {
        const rootController = new AbortController();
        this.#abortSignal = rootController.signal;

        const observer = new MutationObserver((mutations) => {
            for (const mutation of mutations) {
                const numRemoved = mutation.removedNodes.length;
                for (let i = 0; i < numRemoved; i++) {
                    const node = mutation.removedNodes[i]!;

                    if (node === root) {
                        this.disconnect();
                        return;
                    }

                    const controller = this.#nodes.get(node);
                    if (controller) {
                        controller.abort();
                        this.#nodes.delete(node);
                    }
                }
            }
        });

        observer.observe(root.parentNode ?? root, { subtree: true, childList: true });

        this.disconnect = () => {
            if (this.#isDisconnected) {
                return;
            }

            this.#isDisconnected = true;

            for (const controller of this.#nodes.values()) {
                controller.abort();
            }

            this.#nodes.clear();
            rootController.abort();
            observer.disconnect();
        };
    }

    public get signal(): AbortSignal {
        return this.#abortSignal;
    }

    public observe(node: Node, controller?: AbortController): AbortSignal {
        if (this.#isDisconnected) {
            if (controller) {
                controller.abort();
            }
            return AbortSignal.abort();
        }

        if (this.#nodes.has(node)) {
            const signal = this.#nodes.get(node)!.signal;
            if (controller && signal !== controller.signal) {
                signal.addEventListener('abort', () => controller!.abort(), { once: true });
            }
            return signal;
        }

        if (!controller) {
            controller = new AbortController();
        }

        this.#nodes.set(node, controller);
        return controller.signal;
    }
}
