import * as <PERSON>IXI from 'pixi.js';
import { 
    GRAPH_WIDTH_FACTOR, 
    GRAPH_HEIGHT_FACTOR, 
    MULTIPLIER_RECT_WIDTH, 
    MULTIPLIER_RECT_HEIGHT, 
    MULTIPLIER_PADDING, 
    MULTIPLIER_BORDER_RADIUS,
    R<PERSON><PERSON>RING_CONFIG,
    MATH_CONFIG,
    COLORS
} from './constants';

export interface PositionData {
    x: number;
    y: number;
}

export interface ScreenPoint {
    x: number;
    y: number;
}

export class PositionHelper {
    private width: number;
    private height: number;
    private readonly padding: number;

    constructor(width: number, height: number, padding: number) {
        this.width = width;
        this.height = height;
        this.padding = padding;
    }

    public updateDimensions(width: number, height: number): void {
        this.width = width;
        this.height = height;
    }

    /** Calculate bottom padding as 5% of canvas height */
    private get bottomPadding(): number {
        return this.height * 0.05;
    }

    public toScreen(point: PositionData, data: Array<PositionData>): ScreenPoint {
        const availableWidth = this.width - this.padding * 2;
        const availableHeight = this.height - this.padding - this.bottomPadding;

        // Apply 10% margin on top and right
        const graphWidth = availableWidth * GRAPH_WIDTH_FACTOR;
        const graphHeight = availableHeight * GRAPH_HEIGHT_FACTOR;

        const normalizedX = this.timeToNormalizedX(point.x, data);
        const normalizedY = this.multiplierToNormalizedY(point.y);

        const yStart = this.height - this.bottomPadding;

        return {
            x: this.padding + normalizedX * graphWidth,
            y: yStart - normalizedY * graphHeight,
        };
    }

    public positionMonkeyHead(
        monkeyHeadContainer: PIXI.Container, 
        tipXPosition: number, 
        tipYPosition: number,
        data: Array<PositionData>,
        gameOutcome: 'running' | 'win' | 'lose'
    ): void {
        if (!monkeyHeadContainer) return;

        // Hide monkey head when no data or when game is not running
        if (data.length === 0) {
            monkeyHeadContainer.visible = false;
            return;
        }

        monkeyHeadContainer.visible = true;
        monkeyHeadContainer.x = tipXPosition + RENDERING_CONFIG.MONKEY_OFFSET_X;
        monkeyHeadContainer.y = tipYPosition + RENDERING_CONFIG.MONKEY_OFFSET_Y;
        
        const lastPoint = data[data.length - 1];
        if (lastPoint) {
            if (gameOutcome === 'running') {
                const prevPoint = data[data.length - 2] ?? { x: -1, y: 1 };
                const screenPrev = this.toScreen(prevPoint, data);
                const screenLast = this.toScreen(lastPoint, data);
                const lineAngle = Math.atan2(
                    screenLast.y - screenPrev.y,
                    screenLast.x - screenPrev.x,
                );
                monkeyHeadContainer.rotation = lineAngle + RENDERING_CONFIG.LINE_ROTATION_OFFSET;
            } else {
                monkeyHeadContainer.rotation = 0;
            }
        }
    }

    public positionMultiplierLabel(
        container: PIXI.Container,
        background: PIXI.Graphics,
        text: PIXI.Text,
        x: number,
        y: number,
        multiplier: number,
        gameOutcome: 'running' | 'win' | 'lose',
        dataLength: number
    ): void {
        text.text = `${multiplier.toFixed(2)}x`;
        
        let multiplierTextColor: number;
        switch (gameOutcome) {
            case 'win':
                multiplierTextColor = COLORS.GREEN;
                break;
            case 'lose':
                multiplierTextColor = COLORS.RED;
                break;
            default:
                multiplierTextColor = COLORS.WHITE;
                break;
        }

        text.style.fill = multiplierTextColor;
        
        // Only position the multiplier if we have valid data
        if (dataLength === 0) {
            container.visible = false;
            return;
        }
        
        container.visible = true;
        
        // Clear background (no background)
        background.clear();
        
        // Position container to the bottom-right of the tip
        container.x = Math.min(this.width - MULTIPLIER_RECT_WIDTH - MULTIPLIER_PADDING, x + 20);
        container.y = Math.min(this.height - MULTIPLIER_RECT_HEIGHT - MULTIPLIER_PADDING, y + 20);
    }

    public setupMultiplierDisplay(
        container: PIXI.Container,
        background: PIXI.Graphics,
        text: PIXI.Text
    ): void {
        background.clear();
        background.roundRect(0, 0, MULTIPLIER_RECT_WIDTH, MULTIPLIER_RECT_HEIGHT, MULTIPLIER_BORDER_RADIUS)
                  .fill({ color: COLORS.BLACK, alpha: 0.4 });
        
        text.anchor.set(0.5);
        text.x = MULTIPLIER_RECT_WIDTH / 2;
        text.y = MULTIPLIER_RECT_HEIGHT / 2;

        // Don't position the container here - it will be positioned by updateMultiplierLabel
        container.visible = false;
    }

    public calculateInterpolatedPosition(
        visibleData: Array<PositionData>,
        animationProgress: number
    ): ScreenPoint | null {
        if (visibleData.length < 2) return null;

        const lastPoint = visibleData[visibleData.length - 2];
        const nextPoint = visibleData[visibleData.length - 1];
        
        if (!lastPoint || !nextPoint) return null;

        let progress = Math.min(animationProgress, 1);
        progress = this.easeInCubic(progress);
        
        const interpolatedX = lastPoint.x + (nextPoint.x - lastPoint.x) * progress;
        const interpolatedY = lastPoint.y + (nextPoint.y - lastPoint.y) * progress;
        
        const availableWidth = this.width - this.padding * 2;
        const availableHeight = this.height - this.padding - this.bottomPadding;
        const graphWidth = availableWidth * GRAPH_WIDTH_FACTOR;
        const graphHeight = availableHeight * GRAPH_HEIGHT_FACTOR;
        
        const normalizedX = this.timeToNormalizedX(interpolatedX, visibleData);
        const x = this.padding + normalizedX * graphWidth;
        
        const normalizedY = this.multiplierToNormalizedY(interpolatedY);
        const y = this.height - this.bottomPadding - normalizedY * graphHeight;
        
        return { x, y };
    }

    private multiplierToNormalizedY(multiplier: number): number {
        if (multiplier <= 1) {
            return 0;
        }
        
        const maxMultiplier = MATH_CONFIG.MAX_MULTIPLIER;
        const logMin = Math.log(1);
        const logMax = Math.log(maxMultiplier);
        const logValue = Math.log(multiplier);
        
        const normalizedY = (logValue - logMin) / (logMax - logMin);
        const easedY = Math.pow(normalizedY, MATH_CONFIG.Y_CURVE_POWER);
        
        return Math.min(easedY, 1);
    }

    private timeToNormalizedX(timeSeconds: number, data: Array<PositionData>): number {
        const lastDataPoint = data.length > 0 ? data[data.length - 1] : null;
        const maxTime = lastDataPoint ? Math.max(MATH_CONFIG.MIN_MAX_TIME, lastDataPoint.x) : MATH_CONFIG.MIN_MAX_TIME;
        
        if (timeSeconds <= 0) return 0;
        
        const normalizedTime = timeSeconds / maxTime;
        const power = MATH_CONFIG.X_CURVE_POWER;
        return Math.pow(normalizedTime, power);
    }

    private easeInCubic(x: number): number {
        return Math.pow(x, 3);
    }
} 