import { logDevError } from '@monkey-tilt/utils';
import type { AudioController } from './controller';
import { loadAudio, type Source } from './loader';

export interface PlaybackOptions {
    readonly playbackRate?: number;
}

export class Sound {
    #controller: AudioController;
    #buffer: AudioBuffer | null = null;

    public constructor(controller: AudioController, sources: ReadonlyArray<Source>) {
        this.#controller = controller;

        loadAudio(controller.offlineContext, sources)
            .then((buffer) => {
                this.#buffer = buffer;
            })
            .catch(logDevError);
    }

    public async play(options?: PlaybackOptions | undefined): Promise<void> {
        return this.playSegment(undefined, undefined, undefined, options);
    }

    public async playSegment(
        start?: number,
        duration?: number,
        when?: number,
        { playbackRate = 1.0 }: PlaybackOptions | undefined = {},
    ): Promise<void> {
        if (!this.#buffer) {
            return;
        }

        if (duration === undefined) {
            duration = this.#buffer.duration - (start ?? 0);
        }

        if (this.#controller.muted) {
            return new Promise((resolve) => setTimeout(resolve, duration * 1000));
        }

        const initTime = performance.now();

        const context = await this.#controller.resume();

        if (performance.now() - initTime > 300) {
            // Audio context took too long to resume, skip playback
            return;
        }

        const source = context.createBufferSource();
        source.buffer = this.#buffer;
        source.playbackRate.value = playbackRate;

        if (!this.#controller.connect(source)) {
            return;
        }

        return new Promise((resolve) => {
            if (context.state !== 'running') {
                setTimeout(resolve, duration * 1000);
            } else {
                source.addEventListener('ended', () => resolve(), { once: true });
                source.start(when, start, duration);
            }
        });
    }
}

export interface SoundSpriteDefinition<T extends string = string> {
    readonly sources: ReadonlyArray<Source>;
    readonly sprites: Record<T, [start: number, duration: number]>;
}

export type SoundSpriteDefinitionLike<T extends string = string> =
    | SoundSpriteDefinition<T>
    | (Omit<SoundSpriteDefinition<T>, 'sprites'> & {
          readonly sprites: Record<T, ReadonlyArray<number>>;
      });

export class SoundSprite<T extends string = string> {
    #sprites: Record<T, [start: number, duration: number]>;
    #sound: Sound;

    public constructor(controller: AudioController, definition: SoundSpriteDefinitionLike<T>) {
        const { sources, sprites } = definition;

        this.#sound = new Sound(controller, sources);

        for (const span of Object.values(sprites) as ReadonlyArray<ReadonlyArray<number>>) {
            if (span.length !== 2) {
                throw new Error('Sprite spans must have exactly two elements.');
            }
        }

        this.#sprites = sprites as Record<T, [start: number, duration: number]>;
    }

    public async play(sprite: T, options?: PlaybackOptions): Promise<void> {
        if (!this.#sprites[sprite]) {
            return;
        }
        const [start, duration] = this.#sprites[sprite];
        return this.#sound.playSegment(start, duration, undefined, options);
    }
}
