import { <PERSON><PERSON><PERSON><PERSON>boardController, KeyboardController } from '@monkey-tilt/ui';
import type { UIAction } from '../actions';

export const keyboardShortcuts: KeyboardController<UIAction> = new DOMKeyboardController<UIAction>({
    keyMap: new Map([
        ['Space', { action: 'Bet', description: 'Make a bet' }],
        ['W', { action: 'MultiplyBetAmount', data: 2, description: 'Double bet amount' }],
        ['S', { action: 'MultiplyBetAmount', data: 0.5, description: 'Halve bet amount' }],
        ['Q', { action: 'SetBetAmount', data: 0, description: 'Zero bet amount' }],
        ['A', { action: 'Hit' }],
        ['E', { action: 'Double' }],
        ['D', { action: 'Stand' }],
        ['R', { action: 'Split' }],
        ['X', { action: 'Insure', data: true, description: 'Accept insurance' }],
        ['C', { action: 'Insure', data: false, description: 'No insurance' }],
        ['1', { action: 'SetBetAmount', data: 0.1, hidden: true }],
        ['2', { action: 'SetBetAmount', data: 1, hidden: true }],
        ['3', { action: 'SetBetAmount', data: 10, hidden: true }],
        ['4', { action: 'SetBetAmount', data: 100, hidden: true }],
    ]),
});
