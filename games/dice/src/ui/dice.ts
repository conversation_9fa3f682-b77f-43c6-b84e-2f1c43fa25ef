import {
    currencyToFixedString,
    GameUI,
    roundCurrency,
    timer,
    type MessageName,
    type RequestData,
    type VerifySeedParams,
    type Writable,
} from '@monkey-tilt/client';
import {
    type ModalControls,
    type RefObject,
    type Root,
    type Signal,
    type SignalReader,
    type ToastBarControls,
    type ToastControls,
    type ToastProps,
} from '@monkey-tilt/ui';
import { logDevError } from '@monkey-tilt/utils';
import { type DiceClient } from '../game/dice';
import { type AutobetState, type DiceState, type FinalState } from '../game/state';
import type { UIAction } from './actions';
import { keyboardShortcuts } from './input/keyboard';

import {
    calculateParams,
    DICE_ROLL_PRECISION,
    diceRollLimits,
    INITIAL_MULTIPLIER,
    MIN_MULTIPLIER,
    MULTIPLIER_PRECISION,
} from '../util/chance';
import effects from './assets/effects.json';
import effectsMP3 from './assets/effects.mp3';
import effectsOGG from './assets/effects.ogg';
import effectsWEBM from './assets/effects.webm';

export type SoundEffect = keyof typeof effects;

type Config = {
    readonly soundEffects: boolean;
    readonly hotkeys: boolean;
    readonly turboMode: boolean;
};

export interface DiceUIOptions {
    /**
     * The base URL used to load assets from.
     */
    readonly assetsUrl: URL;

    /**
     * The Dice GameClient instance.
     */
    readonly client: DiceClient;

    /**
     * The state root.
     */
    readonly root: Root;
}

export interface GameState extends DiceState {}

export class DiceUI extends GameUI<GameState, DiceClient, UIAction, Config, SoundEffect> {
    #toastBarControlsRef: RefObject<ToastBarControls> = { current: null };
    #notificationsControlsRef: RefObject<ToastBarControls> = { current: null };
    #hotkeysModalControlsRef: RefObject<ModalControls> = { current: null };
    #provablyFairModalControlsRef: RefObject<ModalControls> = { current: null };

    #pendingIntents: {
        CloseAutobet: Signal<boolean>;
    };

    #activeAction: Signal<MessageName | null>;

    public readonly activeTab: Signal<'manual' | 'auto'>;

    public readonly currency: SignalReader<string>;
    public readonly usdRate: SignalReader<number>;

    public readonly betAmount: Signal<string>;

    public readonly multiplier: Signal<string>;
    public readonly diceRoll: Signal<string>;
    public readonly rollOver: Signal<boolean>;

    public readonly autobet: {
        [K in Exclude<keyof AutobetState, 'autobet_count'>]: Signal<
            AutobetState[K] extends boolean ? boolean : string
        >;
    };

    public readonly maxBetAmount: SignalReader<number>;
    public readonly isBetAllowed: SignalReader<boolean>;
    public readonly isBettingLocked: SignalReader<boolean>;

    #setBetAmount: (amount: number) => void;
    #multiplyBetAmount: (multiplier: number) => void;

    public constructor({ client, assetsUrl, root }: DiceUIOptions) {
        const initialState = client.state();

        super({
            client,
            assetsUrl,
            root,
            keyboardShortcuts,
            config: {
                soundEffects: true,
                hotkeys: false,
                turboMode: window.matchMedia(`(prefers-reduced-motion: reduce)`).matches,
            },
            soundEffects: {
                sources: [
                    { src: new URL(effectsWEBM, assetsUrl).href, type: 'audio/webm; codecs=opus' },
                    { src: new URL(effectsOGG, assetsUrl).href, type: 'audio/ogg; codecs=vorbis' },
                    { src: new URL(effectsMP3, assetsUrl).href, type: 'audio/mpeg' },
                ],
                sprites: effects,
            },
            initialState: {
                ...initialState,
            },
            pendingUpdatesDelay: 500,
        });

        const { signal, effect, memo, untracked } = this.root.store;

        this.#pendingIntents = {
            CloseAutobet: signal(false),
        };

        this.client.on('intentAdded', ({ type }) => {
            if (type === 'CloseAutobet') {
                this.#pendingIntents.CloseAutobet.update(true);
            }
        });
        this.client.on('intentRemoved', ({ type }) => {
            if (type === 'CloseAutobet') {
                this.#pendingIntents.CloseAutobet.update(false);
            }
        });

        this.#activeAction = signal<MessageName | null>(null);

        this.activeTab = signal<'manual' | 'auto'>('manual');

        this.currency = memo(() => this.client.session().currency);
        this.usdRate = memo(() => this.client.session().usdRate);

        this.betAmount = signal('0.00');

        effect(() => {
            const { bet_amount } = this.client.state();
            const currency = this.currency();

            if (bet_amount) {
                const value = Number.parseFloat(bet_amount);
                if (Number.isFinite(value)) {
                    this.betAmount.update(currencyToFixedString(value, currency));
                }
            }
        });

        const { rtp, roll_over, multiplier, dice_roll } = this.client.state();

        this.multiplier = signal(multiplier ?? INITIAL_MULTIPLIER.toFixed(MULTIPLIER_PRECISION));

        this.rollOver = signal(roll_over ?? true);
        this.diceRoll = signal(
            dice_roll ??
                calculateParams(
                    { multiplier: Number(this.multiplier.read()) },
                    { rtp, roll_over: this.rollOver.read() },
                ).dice_roll.toFixed(DICE_ROLL_PRECISION),
        );

        effect(() => {
            const { multiplier } = this.client.state();
            if (multiplier) {
                const value = Number.parseFloat(multiplier);
                if (Number.isFinite(value)) {
                    this.multiplier.update(value.toFixed(MULTIPLIER_PRECISION));
                }
            }
        });

        effect(() => {
            const { roll_over } = this.client.state();
            if (roll_over) {
                this.rollOver.update(roll_over);
            }
        });

        effect(() => {
            const { dice_roll } = this.client.state();
            if (dice_roll) {
                const value = Number.parseFloat(dice_roll);
                if (Number.isFinite(value)) {
                    this.diceRoll.update(value.toFixed(DICE_ROLL_PRECISION));
                }
            }
        });

        this.autobet = {
            autobet_limit: signal('0'),
            on_win: signal('0'),
            on_win_reset: signal(true),
            on_loss: signal('0'),
            on_loss_reset: signal(true),
            stop_on_profit: signal('0.00'),
            stop_on_loss: signal('0.00'),
            autobet_cumulative_payout: signal('0.00'),
        };

        let previousCurrencyCode = this.currency();
        let previousUsdRate = this.usdRate();
        effect(() => {
            const currency = this.currency();
            if (currency !== previousCurrencyCode) {
                void Promise.resolve().then(() => {
                    const usdRate = this.usdRate();
                    const factor = (1 / previousUsdRate) * usdRate;

                    previousCurrencyCode = currency;
                    previousUsdRate = usdRate;

                    const update = (amount: string) =>
                        currencyToFixedString(Number(amount) * factor, currency);

                    this.client.setAutobetState({
                        bet_amount: update(this.betAmount.read()),
                        stop_on_profit: update(this.autobet.stop_on_profit.read()),
                        stop_on_loss: update(this.autobet.stop_on_loss.read()),
                    });
                });
            }
        });

        this.maxBetAmount = memo(() => {
            const multiplier = Number(this.multiplier.read());
            const { bet_limits } = this.client.state();
            return Math.min(
                bet_limits.bet.max,
                roundCurrency(bet_limits.bet.max_payout / multiplier, this.currency()),
            );
        });

        this.isBetAllowed = memo(() => {
            const bet_amount = Number(this.betAmount.read());
            const multiplier = Number(this.multiplier.read());
            const max_bet_amount = this.maxBetAmount();

            return multiplier > 0 && bet_amount <= max_bet_amount;
        });

        this.isBettingLocked = memo(() => {
            const hasActiveAction = this.#activeAction.read() !== null;
            const {
                next_actions = [],
                round_closed = true,
                readyState = 'closed',
                isAuthenticated = false,
                isLoading,
                isAnimatingState,
            } = this.gameState();
            return !(
                !isLoading &&
                !isAnimatingState &&
                !hasActiveAction &&
                readyState === 'ready' &&
                isAuthenticated &&
                (next_actions.includes('Bet') || (next_actions.length === 0 && round_closed))
            );
        });

        this.#setBetAmount = (amount) => {
            this.betAmount.update(
                currencyToFixedString(this.#clampBetAmount(amount), this.currency()),
            );
        };

        this.#multiplyBetAmount = (factor) => {
            this.betAmount.update((amount) =>
                currencyToFixedString(
                    this.#clampBetAmount(Number(amount) * factor),
                    this.currency(),
                ),
            );
        };

        let lastRoundId: string | null = null;

        effect(() => {
            const newState = client.state();

            this.updateGameState((gameState) => {
                if (gameState.isAnimatingState) {
                    return {
                        ...gameState,
                        pendingUpdates: gameState.pendingUpdates.concat([newState]),
                    };
                }

                if (
                    this.isTurboMode ||
                    !newState.round_id ||
                    !newState.action ||
                    !['Bet', 'Autobet', 'Nextbet'].includes(newState.action)
                ) {
                    lastRoundId = null;
                    return {
                        ...gameState,
                        ...newState,
                    };
                }

                const {
                    next_actions,
                    round_closed = false,
                    round_id,
                    autobet_count = 0,
                } = newState;

                const roundId = `${round_id}-${autobet_count}`;

                if (lastRoundId === roundId) {
                    return {
                        ...gameState,
                        ...newState,
                    };
                }

                lastRoundId = roundId;

                return {
                    ...gameState,
                    ...newState,
                    round_closed: false,
                    autobet_count: gameState.autobet_count ?? 0,
                    isAnimatingState: true,
                    next_actions: [],
                    pendingUpdates: gameState.pendingUpdates.concat([
                        {
                            round_closed,
                            autobet_count,
                            next_actions,
                            isAnimatingState: false,
                        },
                    ]),
                };
            });
        });

        const hasNextbet = memo(() => {
            const { isAnimatingState, next_actions } = this.gameState();
            return !isAnimatingState && next_actions.includes('Nextbet');
        });
        const nextbetTimer = timer(800);
        const turboModeNextbetTimer = timer(350);

        this.client.on('roundEnd', () => {
            if (this.client.hasIntent('CloseAutobet')) {
                this.client.closeAutobet().catch(logDevError);
            }
        });

        effect(() => {
            if (hasNextbet()) {
                nextbetTimer.cancel();
                turboModeNextbetTimer.cancel();
                (this.isTurboMode ? turboModeNextbetTimer : nextbetTimer)(() => {
                    this.triggerAction('Nextbet');
                });
            }
        });

        let previousReadyState = this.client.readyState;
        let connectionStatusToast: ToastControls | null = null;
        const connectionStatusTimer = timer(1000);

        this.client.on('readyStateChanged', (newReadyState) => {
            if (newReadyState !== previousReadyState) {
                if (newReadyState === 'ready') {
                    connectionStatusTimer.cancel();
                    connectionStatusToast?.dismiss();
                    this.initSession().catch(logDevError);
                } else if (!connectionStatusToast && !untracked(this.gameState).isLoading) {
                    connectionStatusTimer(() => {
                        connectionStatusToast = this.showToast({
                            variant: 'error',
                            content: 'Lost connection. Trying to reconnect...',
                            isDismissable: false,
                            dismiss: undefined,
                            onDismiss() {
                                connectionStatusToast = null;
                            },
                        });
                    });
                }
                previousReadyState = newReadyState;
            }
        });

        this.client.on('beforeSend', ({ action }) => {
            this.#activeAction.update(action as MessageName);
        });

        this.client.on('afterHandle', () => {
            this.#activeAction.update(null);
        });

        this.client.on('beforeGameSessionSwitch', () => {
            const limit = this.autobet.autobet_limit.read();
            this.client.setAutobetState({
                bet_amount: this.betAmount.read(),
                multiplier: this.multiplier.read(),
                dice_roll: this.diceRoll.read(),
                roll_over: this.rollOver.read(),
                autobet_limit: limit === '∞' ? Infinity : Number(limit),
                on_win: this.autobet.on_win.read(),
                on_win_reset: this.autobet.on_win_reset.read(),
                on_loss: this.autobet.on_loss.read(),
                on_loss_reset: this.autobet.on_loss_reset.read(),
                stop_on_profit: this.autobet.stop_on_profit.read(),
                stop_on_loss: this.autobet.stop_on_loss.read(),
            });
        });

        this.onStateReload(() => {
            const { isAutobetActive } = untracked(this.client.state);
            this.activeTab.update(isAutobetActive ? 'auto' : 'manual');
            if (isAutobetActive) {
                this.triggerAction('CloseAutobet');
            }
        });
    }

    public get shouldCloseAutobet(): SignalReader<boolean> {
        return this.#pendingIntents.CloseAutobet.read;
    }

    public onStateReload(handler: () => void): void {
        this.client.onStateReload(handler);
    }

    #clampBetAmount(value: number): number {
        const { bet_limits, balance } = this.gameState();
        return Number.isFinite(value)
            ? Math.min(bet_limits.bet.max, balance, Math.max(0, value))
            : 0;
    }

    public triggerAction(action: UIAction, data?: unknown): void {
        const { rtp, next_actions, round_closed, readyState, isAnimatingState, isAutobetActive } =
            this.gameState();

        if (readyState !== 'ready') {
            return;
        }

        if (action === 'BetOrStop') {
            action = isAutobetActive
                ? 'CloseAutobet'
                : this.activeTab.read() === 'auto'
                  ? 'Autobet'
                  : 'Bet';
        }

        if (isAnimatingState) {
            if (isAutobetActive && action === 'CloseAutobet') {
                this.client.addIntent({ type: 'CloseAutobet' });
            }
            return;
        }

        const canBet = this.client.can('Bet');

        switch (action) {
            case 'MultiplyBetAmount':
                if (typeof data !== 'number' || !canBet) {
                    return;
                }
                this.#multiplyBetAmount(data);
                break;

            case 'SetBetAmount':
                if (typeof data !== 'number' || !canBet) {
                    return;
                }
                this.#setBetAmount(data * this.usdRate());
                break;

            case 'ToggleRollOverUnder': {
                if (!canBet) {
                    return;
                }
                this.rollOver.update((prev) => !prev);
                break;
            }

            case 'DecreaseMultiplier':
            case 'IncreaseMultiplier': {
                if (!canBet) {
                    return;
                }

                const { min: minDiceRoll } = diceRollLimits(rtp);
                const min = MIN_MULTIPLIER;
                const max = calculateParams({ dice_roll: minDiceRoll }, { rtp }).multiplier;

                this.multiplier.update((multiplier) => {
                    return Math.max(
                        min,
                        Math.min(
                            max,
                            Number(multiplier) + (action === 'IncreaseMultiplier' ? 1 : -1),
                        ),
                    ).toFixed(MULTIPLIER_PRECISION);
                });

                break;
            }

            case 'DecreaseDiceRoll':
            case 'IncreaseDiceRoll': {
                if (!canBet) {
                    return;
                }

                const { min, max } = diceRollLimits(rtp);
                this.diceRoll.update((diceRoll) => {
                    return Math.max(
                        min,
                        Math.min(max, Number(diceRoll) + (action === 'IncreaseDiceRoll' ? 1 : -1)),
                    ).toFixed(DICE_ROLL_PRECISION);
                });

                break;
            }

            case 'Bet':
            case 'Autobet': {
                const uiState = {
                    bet_amount: this.betAmount.read(),
                    multiplier: this.multiplier.read(),
                    dice_roll: this.diceRoll.read(),
                    roll_over: this.rollOver.read(),
                };

                if (action === 'Autobet') {
                    const limit = this.autobet.autobet_limit.read();
                    this.client.setAutobetState({
                        ...uiState,
                        autobet_limit: limit === '∞' ? Infinity : Number(limit),
                        on_win: this.autobet.on_win.read(),
                        on_win_reset: this.autobet.on_win_reset.read(),
                        on_loss: this.autobet.on_loss.read(),
                        on_loss_reset: this.autobet.on_loss_reset.read(),
                        stop_on_profit: this.autobet.stop_on_profit.read(),
                        stop_on_loss: this.autobet.stop_on_loss.read(),
                    });
                } else {
                    this.client.setBetState(uiState);
                }

                let canBet = next_actions.includes(action);
                if (!canBet && (round_closed || next_actions.length === 0)) {
                    this.client.reset();
                    canBet = this.gameState().next_actions.includes(action);
                }

                if (!canBet || !this.isBetAllowed()) {
                    return;
                }

                const { bet_limits, balance } = this.gameState();
                const currency = this.currency();

                const request: Writable<RequestData<'Bet' | 'Autobet'>> = {
                    currency,
                    multiplier: Number(uiState.multiplier),
                    dice_roll: Number(uiState.dice_roll),
                    roll_over: uiState.roll_over,
                };

                const betAmount = Number(uiState.bet_amount);

                if (betAmount > 0 && (betAmount < bet_limits.bet.min || betAmount > balance)) {
                    this.showToast({
                        content: 'Please place a valid bet amount.',
                        variant: 'error',
                    });
                    return;
                }

                request.bet_amount = betAmount;

                if (action === 'Autobet') {
                    const autobetRequest = {} as Writable<RequestData<'Autobet'>>;
                    const limit = this.autobet.autobet_limit.read();

                    if (limit == '∞') {
                        autobetRequest.autobet_limit = 0;
                    } else {
                        autobetRequest.autobet_limit = Number(limit);
                        if (
                            !Number.isFinite(autobetRequest.autobet_limit) ||
                            autobetRequest.autobet_limit < 0
                        ) {
                            this.showToast({
                                content: 'Please enter a valid number of bets.',
                                variant: 'error',
                            });
                            return;
                        }
                    }

                    for (const key of [
                        'on_win',
                        'on_loss',
                        'stop_on_profit',
                        'stop_on_loss',
                    ] as const) {
                        const value = this.autobet[key].read();
                        autobetRequest[key] = Number(value);
                        if (Number.isFinite(autobetRequest[key])) {
                            autobetRequest[key] = Number(value);
                        }
                    }

                    autobetRequest.on_win_reset = this.autobet.on_win_reset.read();
                    autobetRequest.on_loss_reset = this.autobet.on_loss_reset.read();

                    Object.assign(request, autobetRequest);
                }

                Promise.resolve()
                    .then(() => this.client[action.toLowerCase() as 'bet' | 'autobet'](request))
                    .catch(logDevError);

                void this.playSound('bet');

                break;
            }
            case 'CloseAutobet':
                if (!isAutobetActive) {
                    return;
                }
                if (next_actions.includes('CloseAutobet')) {
                    void this.client.closeAutobet().catch(logDevError);
                } else {
                    this.client.addIntent({ type: 'CloseAutobet' });
                }
                break;
            case 'Nextbet':
                if (!next_actions.includes('Nextbet') || !this.isBetAllowed()) {
                    return;
                }
                void this.client.nextbet().catch(logDevError);
                break;
            default:
                logDevError(`Unexpected action: ${String(action)}`);
                return;
        }
    }

    public get toastBarControlsRef(): RefObject<ToastBarControls> {
        return this.#toastBarControlsRef;
    }

    public get notificationsControlsRef(): RefObject<ToastBarControls> {
        return this.#notificationsControlsRef;
    }

    public get hotkeysModalControlsRef(): RefObject<ModalControls> {
        return this.#hotkeysModalControlsRef;
    }

    public get provablyFairModalControlsRef(): RefObject<ModalControls> {
        return this.#provablyFairModalControlsRef;
    }

    public showToast(toast: ToastProps): ToastControls | null {
        if (this.#toastBarControlsRef.current) {
            return this.#toastBarControlsRef.current.showToast({
                dismiss: { after: 2000 },
                ...toast,
            });
        }
        return null;
    }

    public showNotification(toast: ToastProps): ToastControls | null {
        if (this.#notificationsControlsRef.current) {
            return this.#notificationsControlsRef.current.showToast({
                dismiss: { after: 2500 },
                isDismissable: false,
                ...toast,
            });
        }
        return null;
    }

    public async verifySeeds(params: VerifySeedParams): Promise<FinalState> {
        return this.client.verifySeeds(params);
    }
}
