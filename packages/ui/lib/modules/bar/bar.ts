import { defineComponent, html, type Props } from '../../component';
import { mergeClasses, namespaced } from '../../utilities/classes';
import { Input } from '../input/input';
import type { Reactive } from '../../state';

import diamondSVG from './assets/diamond.svg?raw';

export interface BarProps extends Props<HTMLDivElement> {
    readonly multipliers: Reactive<{ multiplier: string; chance: string; payout: string }[]>;
}

const createTooltipItem = (label: string, value: string) =>
    html('div')({
        className: namespaced('m-bar__tooltip-item'),
        children: [
            html('p')({ children: [label] }),
            Input({
                value,
                readOnly: true,
                className: namespaced('m-bar__tooltip-input'),
            }),
        ],
    });

const renderTooltip = (values: { multiplier: string; payout: string; winChance: string }) =>
    html('div')({
        className: namespaced('m-bar__tooltip'),
        children: [
            createTooltipItem('Multiplier', values.multiplier),
            createTooltipItem('Payout', values.payout),
            createTooltipItem('Chance', values.winChance),
        ],
    });

export const Bar = defineComponent(
    'Bar',
    ({ state }) =>
        ({ multipliers, className, ...props }: BarProps) => {
            const { onPropChange, read } = state();

            return html('div')({
                ...props,
                role: 'list',
                className: mergeClasses(className, namespaced('m-bar')),
                ref: (element) => {
                    const renderNumbers = (
                        list: { multiplier: string; chance: string; payout: string }[],
                    ) => {
                        element.innerHTML = '';

                        if (list.length === 0) {
                            element.appendChild(
                                html('div')({
                                    className: namespaced('m-bar__empty'),
                                    children: [
                                        html('div')({
                                            children: ['Select 1 – 10 numbers to play'],
                                        }),
                                    ],
                                }).render(),
                            );
                            return;
                        }

                        list.forEach(({ multiplier, chance, payout }, index) => {
                            const tooltip = renderTooltip({
                                multiplier: `${multiplier}x`,
                                payout,
                                winChance: `${chance}%`,
                            });

                            element.appendChild(
                                html('div')({
                                    role: 'listitem',
                                    className: namespaced('m-bar__item'),
                                    children: [
                                        html('div')({
                                            className: namespaced('m-bar__caret'),
                                        }),
                                        tooltip,
                                        html('span')({
                                            className: namespaced('m-bar__number'),
                                            children: [`${index}x`],
                                        }),
                                        html('div')({
                                            className: namespaced('m-bar__icon'),
                                            innerHTML: diamondSVG,
                                        }),
                                    ],
                                }).render(),
                            );
                        });
                    };

                    onPropChange(multipliers, renderNumbers);
                },
            });
        },
);

export default Bar;
