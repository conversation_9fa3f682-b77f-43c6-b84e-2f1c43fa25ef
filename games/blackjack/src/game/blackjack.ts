import {
    APIClient,
    createWebSocketTransport,
    currencyToFixedString,
    formatCurrency,
    GameClient,
    GameError,
    isGameUpdate,
    merge,
    pick,
    RetryError,
    timer,
    uuid,
    validateObject,
    writable,
    type AnyState,
    type GameActionName,
    type GameState,
    type RequestData,
    type VerifySeedParams,
    type Writable,
} from '@monkey-tilt/client';
import type { Root, SignalReader, SignalUpdater } from '@monkey-tilt/ui';
import { deepMerge, logDevError } from '@monkey-tilt/utils';
import type { BlackjackBetAmountLimits, BlackjackState, Card, CardInfo } from './state';

export interface BlackjackClientOptions {
    readonly root: Root;
    readonly gameId: string;
    readonly gatewayUrl: string | URL;
    readonly websocketUrl?: string | URL;
    readonly gameSessionId: string;
    readonly currency: string;
}

interface BlackjackGameOptions extends BlackjackClientOptions {
    readonly apiClient: APIClient;
    readonly betAmountLimits: BlackjackBetAmountLimits;
}

export async function createBlackjackClient(
    options: BlackjackClientOptions,
): Promise<BlackjackClient> {
    const apiClient = new APIClient(options.gatewayUrl);
    apiClient.gameId = options.gameId;
    apiClient.gameSessionId = options.gameSessionId;

    return new BlackjackGame({
        ...options,
        apiClient,
        betAmountLimits: deepMerge<BlackjackBetAmountLimits>(
            {
                bet: {
                    min: 0.01,
                    max: 5000,
                },
                perfect_pairs: {
                    min: 0,
                    max: 1000,
                },
                poker: {
                    min: 0,
                    max: 500,
                },
            },
            await apiClient.getBetLimits(),
        ),
    });
}

export interface BlackjackClient extends GameClient<BlackjackState> {
    readonly state: SignalReader<BlackjackState>;
    onStateReload(handler: () => void): void;
    bet(request: RequestData<'Bet'>): Promise<void>;
    peek(request: RequestData<'Peek'>): Promise<void>;
    action(action: 'Hit' | 'Stand' | 'Surrender' | 'Double' | 'Split'): Promise<void>;
    setBetAmounts(
        amounts: Pick<BlackjackState, 'bet_amount' | 'poker_bet_amount' | 'perfect_pairs_amount'>,
    ): void;
    reset(): void;
    verifySeeds(params: VerifySeedParams): Promise<CardInfo[]>;
}

class BlackjackGame extends GameClient<BlackjackState> implements BlackjackClient {
    #getState: SignalReader<BlackjackState>;
    #setState: SignalUpdater<BlackjackState>;

    #emptyState = {
        dealer_hand: {
            id: uuid(),
            cards: [],
            status: 'playing',
        },
        player_hands: [
            {
                id: uuid(),
                cards: [],
                bet_amount: '0.00',
                status: 'playing',
            },
        ],
        hand_owner: 'Player',
        hand_index: 0,
    } as const satisfies Partial<BlackjackState>;

    #stateReloadHandlers = new Set<() => void>();

    #updateBalance: () => Promise<void>;
    #updateBetLimits: () => Promise<void>;

    public constructor({
        root,
        gameId,
        gatewayUrl,
        apiClient,
        betAmountLimits,
        websocketUrl,
        gameSessionId,
        currency,
    }: BlackjackGameOptions) {
        super({
            root,
            gameId,
            gameSessionId,
            gatewayUrl,
            currency,
            apiClient,
            transport: createWebSocketTransport({
                gameSessionId,
                gatewayUrl,
                websocketUrl,
            }),
            actionTimeout: 2000,
            autoRetryAttempts: 5,
        });

        const { effect, memo, signal } = this.root.store;

        [this.#getState, this.#setState] = signal<BlackjackState>({
            balance: 0,
            bet_limits: betAmountLimits,
            error: null,
            isLoading: true,
            readyState: 'closed',
            isAuthenticated: false,
            round_id: '',
            ...this.#emptyState,
            bet_type: 'standard',
            bet_amount: '0.00',
            next_actions: [],
        });

        this.#updateBalance = this.#createUpdater('balance', () =>
            this.api.getBalance().then(({ balance }) => balance),
        );

        const usdRate = memo(() => this.session().usdRate);
        const usdBetLimits = signal(betAmountLimits);

        this.#updateBetLimits = async () => {
            const limits = await this.api.getBetLimits('perfect_pairs', 'poker');
            usdBetLimits.update((prevLimits) => deepMerge(prevLimits, limits));
        };

        effect(() => {
            const limits = usdBetLimits.read();
            const factor = usdRate();
            this.#setState((state) => ({
                ...state,
                bet_limits: {
                    ...(['bet', 'perfect_pairs', 'poker'] as const).reduce(
                        (acc, bet) => ({
                            ...acc,
                            [bet]: {
                                min: limits[bet].min * factor,
                                max: limits[bet].max * factor,
                            },
                        }),
                        {} as BlackjackBetAmountLimits,
                    ),
                },
            }));
        });

        const prepareNextRound = () => {
            this.#updateBalance().catch(logDevError);
            this.#updateBetLimits().catch(logDevError);
        };

        prepareNextRound();
        this.on('roundEnd', prepareNextRound);

        const nextActions = memo(() => {
            const { next_actions, hand_owner } = this.#getState();
            return {
                next_actions,
                hand_owner,
            };
        });

        const dealerActionDelay = timer(1200);

        effect(() => {
            const { next_actions, hand_owner } = nextActions();

            if (hand_owner === 'Dealer' && next_actions.includes('Hit')) {
                dealerActionDelay(this.#prepareSend('Hit'));
            } else {
                dealerActionDelay.cancel();
            }
        });

        this.on('readyStateChanged', (readyState) => {
            this.#setState((state) => ({
                ...state,
                isLoading: !(state.isLoading && readyState === 'ready'),
                readyState,
                isAuthenticated: this.isAuthenticated,
            }));
        });

        this.on('error', (event) => {
            let error: unknown = event.error;

            if (error instanceof RetryError) {
                error = error.reason;
            }

            if (error instanceof GameError) {
                this.#setState((state) => {
                    return {
                        ...state,
                        next_actions:
                            error.code === 'INSUFFICIENT_FUNDS'
                                ? this.roundStartActions
                                : state.next_actions,
                        error,
                    };
                });
            }
        });
    }

    public override close(): void {
        this.root.unmount();
        super.close();
    }

    protected updateState(
        value: BlackjackState | ((prevValue: BlackjackState) => BlackjackState),
    ): void {
        return this.#setState(value);
    }

    public get state(): SignalReader<BlackjackState> {
        return this.#getState;
    }

    public onStateReload(handler: () => void): void {
        this.#stateReloadHandlers.add(handler);
    }

    public override bet(request: RequestData<'Bet'>): Promise<void> {
        if (!this.isAuthenticated) {
            return this.authenticate().then(() => this.bet(request));
        }

        if (this.can('Bet')) {
            const { currency } = this.session();
            this.#setState((state) =>
                merge(state, {
                    bet_amount: currencyToFixedString(request.bet_amount, currency),
                    poker_bet_amount: currencyToFixedString(
                        request.poker_bet_amount ?? 0,
                        currency,
                    ),
                    perfect_pairs_amount: currencyToFixedString(
                        request.perfect_pairs_amount ?? 0,
                        currency,
                    ),
                }),
            );

            this.#resetHands();
        }

        return this.#send(
            'Bet',
            request,
            (error) => !(error instanceof GameError) || error.code !== 'INSUFFICIENT_FUNDS',
        );
    }

    public peek(request: RequestData<'Peek'>): Promise<void> {
        return this.#send('Peek', request);
    }

    public action(action: 'Hit' | 'Stand' | 'Surrender' | 'Double' | 'Split'): Promise<void> {
        return this.#send(action);
    }

    public setBetAmounts(
        amounts: Pick<BlackjackState, 'bet_amount' | 'poker_bet_amount' | 'perfect_pairs_amount'>,
    ): void {
        this.#setState((state) => ({
            ...state,
            ...amounts,
        }));
    }

    public override async reload(): Promise<GameState> {
        this.#setState((state) => ({ ...state, error: null }));
        return super.reload();
    }

    #resetHands(): void {
        writable(this.#emptyState.dealer_hand).id = uuid();
        writable(this.#emptyState.player_hands[0]).id = uuid();

        this.#setState((state) => ({
            ...state,
            ...this.#emptyState,
        }));
    }

    public reset(): void {
        super.reset();

        const { isAuthenticated } = this;

        this.#setState((state) => ({
            ...state,
            error: null,
            next_actions: isAuthenticated ? this.roundStartActions : [],
            isAuthenticated,
            actionInProgress: undefined,
        }));
    }

    #prepareSend(
        action: GameActionName,
        request: RequestData<GameActionName>,
        shouldRetry?: (error: Error) => boolean,
    ): () => void {
        const send = this.prepareSendAndAwait(action, shouldRetry);

        return () => {
            this.#setState((state) => ({
                ...state,
                error: null,
                next_actions: [],
                actionInProgress: {
                    action,
                    requestData: request,
                },
            }));

            return send().catch(logDevError);
        };
    }
    async #send(
        action: GameActionName,
        request: RequestData<GameActionName>,
        shouldRetry?: (error: Error) => boolean,
    ): Promise<void> {
        this.#setState((state) => ({
            ...state,
            error: null,
            next_actions: [],
            actionInProgress: {
                action,
                requestData: request,
            },
        }));
        await this.sendAndAwait(action, request, shouldRetry);
    }

    protected override handleUpdate(state: AnyState): void {
        if (state.type === 'Authenticate') {
            this.reset();
            return;
        }

        if (state.type === 'GameState') {
            const resetState = state.data as Writable<BlackjackState>;

            if (state.round_id) {
                resetState.round_id = state.round_id;
            }

            writable(resetState.dealer_hand).id = uuid();
            if (resetState.dealer_hand.cards) {
                const cards: Card[] = [];
                for (const card of resetState.dealer_hand.cards) {
                    if (card) {
                        writable(card).id = uuid();
                        writable(card).isFacedown = false;
                        cards.push(card);
                    }
                }
                if (cards.length == 1) {
                    cards.push({ id: uuid(), isFacedown: true });
                }
                writable(resetState.dealer_hand).cards = cards;
            } else {
                writable(resetState.dealer_hand).cards = [];
            }

            for (const hand of resetState.player_hands) {
                writable(hand).id = uuid();
                const cards: Card[] = [];
                if (hand.cards) {
                    for (const card of hand.cards) {
                        if (card) {
                            writable(card).id = uuid();
                            writable(card).isFacedown = false;
                            cards.push(card);
                        }
                    }
                }
                writable(hand).cards = cards;
                if (!hand.status) {
                    writable(hand).status = 'playing';
                }
            }

            const { currency } = this.session();

            for (const bet of ['bet_amount', 'poker_bet_amount', 'perfect_pairs_amount'] as const) {
                resetState[bet] = formatCurrency(
                    Number.parseFloat(resetState[bet] ?? '0.00'),
                    currency,
                );
            }

            this.allowedActions = new Set(resetState.next_actions);

            this.#setState((currentState) => ({
                ...currentState,
                poker: undefined,
                perfect_pairs: undefined,
                insurance: undefined,
                ...resetState,
                round_closed: resetState.round_closed == true,
                isAuthenticated: true,
                actionInProgress: undefined,
            }));

            if (resetState.round_closed) {
                this.signalRoundEnd();
            }

            void Promise.resolve().then(() => {
                for (const handler of this.#stateReloadHandlers) {
                    try {
                        handler();
                    } catch {
                        // ignore
                    }
                }
            });

            return;
        }

        if (!isGameUpdate(state)) {
            return;
        }

        const newState = structuredClone(this.#getState()) as Writable<BlackjackState>;
        let isNewRound = false;

        if (state.round_id) {
            isNewRound = state.round_id !== newState.round_id;
            newState.round_id = state.round_id;
        }

        newState.isAuthenticated = this.isAuthenticated;

        newState.action = state.action;
        delete newState.requestData;
        if (newState.actionInProgress) {
            if (newState.actionInProgress.action === state.action) {
                newState.requestData = newState.actionInProgress.requestData;
            }
            delete newState.actionInProgress;
        }

        newState.next_actions = state.data.next_actions;
        this.allowedActions = new Set(newState.next_actions);

        if (isNewRound) {
            this.#setState({
                ...newState,
                ...this.#emptyState,
                round_closed: false,
                insurance: undefined,
                poker: undefined,
                perfect_pairs: undefined,
            });
            // N.B. due to removal of Open step, we have to split state update
            // into two parts to allow UI to properly clean up the previous round
            void Promise.resolve().then(() => {
                this.handleUpdate(state);
            });
            return;
        }

        const playingHandIndex = newState.hand_index;

        merge(
            newState,
            pick(
                state.data,
                'hand_index',
                'hand_owner',
                'poker',
                'perfect_pairs',
                'insurance',
                'round_closed',
                'total_payout',
                'dealer_hand',
            ),
        );

        let index = 0;
        while (true) {
            if (index >= newState.dealer_hand.cards.length) {
                break;
            }

            const card = newState.dealer_hand.cards[index]!;
            if (!card.id) {
                writable(card).isFacedown = false;

                const facedownIndex = newState.dealer_hand.cards.findIndex(
                    ({ isFacedown }) => isFacedown,
                );

                if (facedownIndex >= 0) {
                    writable(card).id = newState.dealer_hand.cards[facedownIndex]!.id;
                    newState.dealer_hand.cards.splice(facedownIndex, 1);
                    continue;
                }

                writable(card).id = uuid();
            }

            index++;
        }

        if (newState.dealer_hand.cards.length == 1) {
            newState.dealer_hand.cards.push({ id: uuid(), isFacedown: true });
        }

        if (state.data.round_closed) {
            const total_payout = Number.parseFloat(state.data.total_payout ?? '0.00');
            const status = total_payout > 0 ? 'win' : 'bust';
            for (const hand of newState.player_hands) {
                if (hand.status === 'playing') {
                    writable(hand).status = status;
                }
            }

            newState.balance += total_payout;

            this.signalRoundEnd();
        }

        if ('player_hands' in state.data) {
            if (state.action == 'Split') {
                const originalHand = newState.player_hands[playingHandIndex]!;
                writable(originalHand.cards[1]!).splitFrom = originalHand.id;
                newState.player_hands.splice(
                    playingHandIndex,
                    1,
                    {
                        ...originalHand,
                        ...state.data.player_hands[0]!,
                        cards: [
                            originalHand.cards[0]!,
                            {
                                ...state.data.player_hands[0]!.cards[1]!,
                                id: uuid(),
                            },
                        ],
                    },
                    {
                        ...originalHand,
                        ...state.data.player_hands[1]!,
                        id: uuid(),
                        splitFrom: originalHand.id,
                        cards: [
                            originalHand.cards[1]!,
                            {
                                ...state.data.player_hands[1]!.cards[1]!,
                                id: uuid(),
                            },
                        ],
                    },
                );
            } else if (state.data.player_hands.length > 0) {
                let offset = 0;
                let { length } = state.data.player_hands;

                if (length == 1) {
                    offset = playingHandIndex;
                } else if (state.data.player_hands.length !== newState.player_hands.length) {
                    length = Math.min(state.data.player_hands.length, newState.player_hands.length);
                }

                for (let i = 0; i < length; i++) {
                    const hand = state.data.player_hands[i]!;
                    if (hand.cards && Array.isArray(hand.cards)) {
                        for (const card of hand.cards) {
                            writable(card).id = uuid();
                        }
                    }
                    merge(newState.player_hands[offset + i], hand);
                }
            }
        }

        this.#setState(newState);
    }

    #createUpdater<T extends keyof BlackjackState>(
        key: T,
        updater: () => Promise<
            BlackjackState[T] | ((previousValue: BlackjackState[T]) => BlackjackState[T])
        >,
    ): () => Promise<void> {
        let isFetching = false;
        return async () => {
            if (isFetching) {
                return;
            }

            isFetching = true;
            try {
                const value = await updater();
                this.#setState((state) => ({
                    ...state,
                    [key]: typeof value === 'function' ? value(state[key]) : value,
                }));
            } catch (error) {
                logDevError(`Failed to fetch ${key}:`, error);
            } finally {
                isFetching = false;
            }
        };
    }

    protected validateSeedVerificationData(data: unknown): boolean {
        return (
            Array.isArray(data) &&
            data.every((card) =>
                validateObject<CardInfo>(card, {
                    suit: 'number',
                    index: 'number',
                    value: 'number',
                }),
            )
        );
    }

    public async verifySeeds(params: VerifySeedParams): Promise<CardInfo[]> {
        return this._validateSeeds(params) as Promise<CardInfo[]>;
    }
}
