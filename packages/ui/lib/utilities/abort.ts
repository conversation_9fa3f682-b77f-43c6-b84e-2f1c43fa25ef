export function abortOnAny(signals: ReadonlyArray<AbortSignal>): AbortSignal {
    if ('any' in AbortSignal) {
        return AbortSignal.any(signals as AbortSignal[]);
    }

    const controller = new AbortController();
    const abortSignal = controller.signal;
    const abort = (event?: Event) =>
        controller.abort(
            event && event.target instanceof AbortSignal ? event.target.reason : undefined,
        );

    for (const signal of signals) {
        if (signal.aborted) {
            controller.abort(signal.reason);
            break;
        }
        signal.addEventListener('abort', abort, { once: true, signal: abortSignal });
    }

    return abortSignal;
}
