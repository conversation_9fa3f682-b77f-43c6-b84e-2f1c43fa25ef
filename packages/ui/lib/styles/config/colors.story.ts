import type { <PERSON>a, StoryObj } from '@storybook/html';
import { html } from '../../component';
import { namespaced } from '../../utilities/classes';

export default {
    title: 'Tokens/Colors',
} satisfies Meta;

const colors = [
    'white',
    'black',

    'mt-400',
    'mt-500',
    'mt-900',
    'mt-950',

    'zinc-50',
    'zinc-300',
    'zinc-350',
    'zinc-400',
    'zinc-450',
    'zinc-700',
    'zinc-800',
    'zinc-850',
    'zinc-900',
    'zinc-950',

    'green-350',
    'green-400',
    'green-450',
    'green-500',
    'green-850',

    'red-400',
    'red-500',

    'blue-400',
    'blue-500',

    'yellow-400',

    'orange-400',
    'orange-500',
];

const div = html('div');

export const Colors: StoryObj = () => {
    return div(
        {
            className: namespaced('l-box', 'l-grid', 'u-gap--xl'),
        },
        ...colors.map((name) =>
            div(
                {
                    className: namespaced('l-cluster', 'u-gap--s', 'u-nowrap'),
                },
                div({
                    css: {
                        background: { var: `color-${name}` },
                        padding: { var: 'size-xl' },
                        width: { var: 'size-xl' },
                        height: { var: 'size-xl' },
                        border: '1px solid black',
                    },
                }),
                `--color-${name}`,
            ),
        ),
    ).render();
};
Colors.storyName = 'Colors';
