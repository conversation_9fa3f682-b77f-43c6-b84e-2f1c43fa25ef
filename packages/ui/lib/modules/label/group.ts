import { create, defineComponent, html, type Props, type Renderable } from '../../component';
import { mergeClasses, namespaced } from '../../utilities/classes';

export interface LabelGroupProps extends Props<HTMLDivElement> {
    /**
     * Label text
     */
    readonly text: Renderable;

    /**
     * Value corresponding to the label
     */
    readonly value: Renderable;

    /**
     * The variant of the label group
     */
    readonly variant?: 'default' | 'box';

    /**
     * Whether the wrapping should be disabled. Defaults to `false`.
     */
    readonly nowrap?: boolean;

    /**
     * Optional additional classes for customization
     */
    readonly className?: string;
}

export const LabelGroup = defineComponent(
    'LabelGroup',
    () =>
        ({
            text,
            value,
            variant = 'default',
            nowrap = false,
            className,
            ...props
        }: LabelGroupProps): HTMLDivElement =>
            create(
                'div',
                {
                    props: {
                        ...props,
                        className: mergeClasses(className, {
                            'm-label-group': true,
                            'm-label-group--nowrap': nowrap,
                            [`m-label-group--${variant}`]: variant !== 'default',
                        }),
                    },
                },
                html('span')({ className: namespaced('m-label-group__text') }, text),
                html('span')({ className: namespaced('m-label-group__value') }, value),
            ),
);
