import {
    type ActionLike,
    type BetPayload,
    type ReadyState,
    type StateLike,
    type Transport,
} from '@monkey-tilt/client';

export class MockTransport implements Transport {
    #messageHandlers: Set<(message: StateLike) => void> = new Set();
    #readyStateHandlers: Set<(readyState: ReadyState) => void> = new Set();
    #readyState: ReadyState = 'closed';

    constructor() {
        // Start ready immediately for simpler mock behavior
        console.log('🎭 Mock transport starting as ready');
        this.#readyState = 'ready';
        
        // Auto-trigger authentication and readyState for local development
        setTimeout(() => {
            console.log('🎭 Mock transport triggering authentication and ready state');
            this.#notifyReadyStateChange();
            
            // Auto-send authentication success
            this.#respond({
                type: 'Authenticate',
                action: 'auth',
                action_id: 'auto-auth',
                eventOrder: 1,
                data: {
                    authenticated: true,
                },
            });
        }, 100);
    }

    get readyState(): ReadyState {
        return this.#readyState;
    }

    #notifyReadyStateChange(): void {
        for (const handler of this.#readyStateHandlers) {
            handler(this.#readyState);
        }
    }

    switchGameSession(_gameSessionId: string): Promise<void> {
        return Promise.resolve();
    }

    onMessage(handler: (message: StateLike) => void): void {
        this.#messageHandlers.add(handler);
    }

    onReadyStateChanged(handler: (readyState: ReadyState) => void): void {
        this.#readyStateHandlers.add(handler);
        // Immediately notify if we're already ready
        if (this.#readyState !== 'closed') {
            setTimeout(() => handler(this.#readyState), 0);
        }
    }

    canSend(_message: ActionLike): boolean {
        return this.#readyState === 'ready';
    }

    close(): void {
        this.#readyState = 'closed';
        this.#notifyReadyStateChange();
    }

    #respond(state: StateLike): void {
        setTimeout(
            () => {
                console.log('🎭 CRASH MOCK RECEIVE:', JSON.stringify(state, null, 2));
                try {
                    for (const handler of this.#messageHandlers) {
                        handler(state);
                    }
                } catch (error) {
                    console.error('🚨 Error in mock response handler:', error);
                }
            },
            50 + Math.random() * 100, // Reduced from 100-300ms to 50-150ms
        );
    }

    #autobetCount = 0;
    #autobetLimit = 0;
    #autobetAmount = 0;
    #currentRoundId = '';

    // Mock balance
    #balance = 1000.0;

    // Generate random crash multiplier between 1.01 and 100x
    #generateCrashMultiplier(): number {
        const rand = Math.random();
        if (rand < 0.5) return 1.01 + Math.random() * 1.99; // 1.01-3x (50% chance)
        if (rand < 0.8) return 3 + Math.random() * 7; // 3-10x (30% chance)
        if (rand < 0.95) return 10 + Math.random() * 40; // 10-50x (15% chance)
        return 50 + Math.random() * 50; // 50-100x (5% chance)
    }

    send(message: ActionLike): boolean {
        console.log('🎭 CRASH MOCK SEND:', message);
        
        if (this.#readyState !== 'ready') {
            console.error('🚨 MockTransport not ready! Current state:', this.#readyState);
            return false;
        }
        
        if (!message.action_id) {
            console.error('🚨 MockTransport received message without action_id:', message);
            return false;
        }
        
        try {
            if (message.action === 'auth') {
                // Send authentication success response
                this.#respond({
                    type: 'Authenticate',
                    action: 'auth',
                    action_id: message.action_id,
                    eventOrder: 1,
                    data: {
                        authenticated: true,
                    },
                });
                
                // After authentication, send initial game state to exit loading mode
                setTimeout(() => {
                    this.#respond({
                        type: 'GameUpdate',
                        action: 'GameState',
                        action_id: message.action_id + '_initial',
                        eventOrder: 2,
                        round_id: Math.random().toString(36).slice(2),
                        data: {
                            next_actions: ['Bet'],
                            round_closed: true,
                            balance: this.#balance,
                            bet_amount: '0.00',
                            multiplier: '2.00',
                            isLoading: false,
                        },
                    });
                }, 100); // Reduced delay
                
                return true;
            }

            if (message.action === 'GameState') {
                // Send a basic game state response
                this.#respond({
                    type: 'GameUpdate',
                    action: 'GameState',
                    action_id: message.action_id,
                    eventOrder: 2,
                    round_id: Math.random().toString(36).slice(2),
                    data: {
                        next_actions: ['Bet'],
                        round_closed: true,
                        balance: this.#balance,
                        bet_amount: '0.00',
                        multiplier: '2.00',
                        isLoading: false,
                    },
                });
                return true;
            }

            const reply = ({
                round_id = Math.random().toString(36).slice(2),
                ...data
            }: Record<string, unknown>) => {
                this.#respond({
                    type: 'GameUpdate',
                    action: message.action,
                    round_id: round_id as string,
                    action_id: message.action_id,
                    eventOrder: Date.now(),
                    data,
                });

                this.#currentRoundId = round_id as string;
            };

            const payload = message.data as unknown as BetPayload;
            const betAmount = payload.bet_amount ?? this.#autobetAmount;
            const targetMultiplier = payload.multiplier ?? 2.0;
            const crashMultiplier = this.#generateCrashMultiplier();
            
            // Validate bet amount
            if (betAmount <= 0) {
                console.error('🚨 Invalid bet amount:', betAmount);
                this.#respond({
                    type: 'Error',
                    action: message.action,
                    action_id: message.action_id,
                    code: 'INVALID_BET_AMOUNT',
                    message: 'Bet amount must be greater than 0',
                });
                return true; // Still return true as we handled the message
            }
            
            // Determine if bet wins (target multiplier is reached before crash)
            const isWin = targetMultiplier <= crashMultiplier;
            const payout = isWin ? betAmount * targetMultiplier : 0;
            
            // Update mock balance
            this.#balance = Math.max(0, this.#balance - betAmount + payout); // Ensure balance doesn't go negative

            const response: Record<string, unknown> = {
                next_actions: ['Bet'], // Always allow betting after a round
                random_multiplier: crashMultiplier.toFixed(2),
                total_payout: payout.toFixed(2),
                previous_bet_outcome: isWin ? 'win' : 'loss',
                round_closed: true,
                bet_amount: betAmount.toFixed(2),
                multiplier: targetMultiplier.toFixed(2),
                balance: this.#balance,
            };

            switch (message.action) {
                case 'Autobet':
                    this.#autobetLimit = Number(message.data?.['autobet_limit']) || 0;
                    this.#autobetAmount = betAmount;
                    this.#autobetCount = 1;
                    
                    if (this.#autobetLimit === 0 || this.#autobetCount < this.#autobetLimit) {
                        response.next_actions = ['Nextbet', 'CloseAutobet'];
                    } else {
                        response.next_actions = ['Bet'];
                        response.round_closed = true;
                        this.#autobetCount = 0;
                    }
                    
                    reply(response);
                    break;

                case 'Nextbet':
                    this.#autobetCount++;
                    
                    if (this.#autobetLimit === 0 || this.#autobetCount < this.#autobetLimit) {
                        response.next_actions = ['Nextbet', 'CloseAutobet'];
                    } else {
                        response.next_actions = ['Bet'];
                        response.round_closed = true;
                        this.#autobetCount = 0;
                    }
                    
                    reply(response);
                    break;

                case 'Bet':
                    reply(response);
                    break;

                case 'CloseAutobet':
                    this.#autobetCount = 0;
                    reply({
                        next_actions: ['Bet'],
                        round_closed: true,
                        round_id: this.#currentRoundId,
                        balance: this.#balance,
                    });
                    break;

                default:
                    console.warn('🚨 Unknown action:', message.action);
                    this.#respond({
                        type: 'Error',
                        action: message.action,
                        action_id: message.action_id,
                        code: 'UNKNOWN_ACTION',
                        message: `Unknown action: ${message.action}`,
                    });
                    break;
            }

            return true;
        } catch (error) {
            console.error('🚨 Error in MockTransport.send:', error);
            this.#respond({
                type: 'Error',
                action: message.action,
                action_id: message.action_id,
                code: 'INTERNAL_ERROR',
                message: `Internal error: ${error instanceof Error ? error.message : 'Unknown error'}`,
            });
            return true; // Still return true as we handled the message
        }
    }
} 