const cancelSymbol = Symbol('cancel');

export type Cancel = typeof cancelSymbol;

export type TimerCallback = (cancel: Cancel) => void | Cancel;

export interface TimerStep {
    (callback: TimerCallback): {
        wait(ms: number): TimerStep;
        cancel(): void;
    };
}

export interface StatefulTimer extends TimerStep {
    cancel(): void;
}

export interface TimerOptions {
    /**
     * When timer is restarted, whether to `'stop'` immediately without invoking the
     * callback and potential timer step chain, or to `'complete'` the current chain
     * ignoring delays between steps.
     *
     * Defaults to 'stop'.
     */
    readonly onRestart?: 'stop' | 'complete';
}

export function timer(ms: number, { onRestart = 'stop' }: TimerOptions = {}): StatefulTimer {
    const chain: Array<{ callback: TimerCallback; delay: number }> = [];
    let timer: ReturnType<typeof setTimeout> | null = null;
    let pending: TimerCallback | null = null;

    const cancelChain = (isRestart: boolean) => {
        if (timer) {
            clearTimeout(timer);
            timer = null;

            if (isRestart && onRestart == 'complete') {
                if (!pending || pending(cancelSymbol) !== cancelSymbol) {
                    while (chain.length > 0) {
                        const { callback } = chain.shift()!;
                        const result = callback(cancelSymbol);
                        if (result === cancelSymbol) {
                            break;
                        }
                    }
                }
            }
        }

        chain.length = 0;
    };

    const cancel = () => cancelChain(false);

    const next = () => {
        if (chain.length > 0) {
            const { callback, delay } = chain.shift()!;

            pending = callback;
            timer = setTimeout(() => {
                pending = null;

                if (callback(cancelSymbol) === cancelSymbol) {
                    cancel();
                } else {
                    next();
                }
            }, delay);
        }
    };

    const wait = (ms: number) => {
        return (callback: TimerCallback) => {
            chain.push({ callback, delay: ms });
            return { wait, cancel };
        };
    };

    return Object.assign(
        (callback: TimerCallback) => {
            cancelChain(true);
            const instance = wait(ms)(callback);
            void Promise.resolve().then(next);
            return instance;
        },
        { cancel },
    );
}

export function wait(ms: number, fn: () => void): ReturnType<StatefulTimer> {
    return timer(ms)(fn);
}
