import type { <PERSON>ActionName } from '@monkey-tilt/client';
import type { DiceState } from './state';

interface GameStateUpdate extends Partial<DiceState> {
    readonly next_actions: ReadonlyArray<GameActionName>;
}

declare module '@monkey-tilt/client' {
    export interface MessageTypes {
        readonly Autobet: {
            readonly request: AutobetPayload;
            readonly response: GameStateUpdate;
        };

        readonly Nextbet: {
            readonly request: void;
            readonly response: GameStateUpdate;
        };

        readonly CloseAutobet: {
            readonly request: void;
            readonly response: GameStateUpdate;
        };
    }

    export interface BetPayload {
        readonly currency: string;
        readonly bet_amount?: number;
        readonly roll_over?: boolean;
        readonly multiplier?: number;
        readonly dice_roll?: number;
    }

    export interface AutobetPayload extends BetPayload {
        readonly autobet_limit?: number;
        readonly on_win?: number;
        readonly on_win_reset?: boolean;
        readonly on_loss?: number;
        readonly on_loss_reset?: boolean;
        readonly stop_on_profit?: number;
        readonly stop_on_loss?: number;
    }

    export interface GameState extends DiceState {}
}

export {};
