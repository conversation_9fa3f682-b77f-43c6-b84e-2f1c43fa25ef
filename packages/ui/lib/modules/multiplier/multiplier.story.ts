import type { Meta, StoryObj } from '@storybook/html';
import { Multiplier, type MultiplierProps } from './multiplier';
import { html } from '../../component';
import { render } from '../../../.storybook/render';

const MIN_MULTIPLIER = 1.01;

const meta: Meta<MultiplierProps> = {
    title: 'Modules/Multiplier',
    globals: {
        backgrounds: { value: 'dark' },
    },
};
export default meta;

export const Default: StoryObj<MultiplierProps> = {
    render: () =>
        render(() => {
            return Multiplier({ value: MIN_MULTIPLIER }).render();
        }),
    args: {},
};

export const Interactive: StoryObj<MultiplierProps> = {
    render: (props: MultiplierProps) => {
        return Multiplier({
            ...props,
            value: props.value ?? MIN_MULTIPLIER,
            target: props.target ?? MIN_MULTIPLIER,
        }).render();
    },
    argTypes: {
        value: {
            control: 'number',
            min: MIN_MULTIPLIER,
        },
        target: {
            control: 'number',
            min: MIN_MULTIPLIER,
        },
    },
    args: {
        value: 6,
        target: 10,
    },
};

export const KenoVariant: StoryObj<MultiplierProps> = {
    render: () =>
        render(() => {
            return Multiplier({
                value: 36.5,
                target: 30,
                variant: 'keno',
            }).render();
        }),
    args: {},
};

export const KenoList: StoryObj = {
    render: () =>
        render(() => {
            const values = [1.05, 2.5, 3.25, 4.75, 6.9, 10.0, 14.8, 21.2, 28.4, 500.0, 1.0];
            return html('div')({
                css: {
                    display: 'flex',
                },
                children: values.map((val) =>
                    Multiplier({
                        value: val,
                        variant: 'keno',
                    }).render(),
                ),
            });
        }),
    args: {},
};