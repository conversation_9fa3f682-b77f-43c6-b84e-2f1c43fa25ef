import { Body, StaticBody, type Collidable, type CollisionInfo } from './body';

export class Wall extends StaticBody {
    #direction: {
        x: number;
        y: number;
    };

    #invertedLengthSquared: number;

    public constructor(x1: number, y1: number, x2: number, y2: number) {
        super(x1, y1, 0);
        this.#direction = {
            x: x2 - x1,
            y: y2 - y1,
        };
        this.#invertedLengthSquared = 1 / (this.#direction.x ** 2 + this.#direction.y ** 2);
    }

    public override checkForCollision(body: Collidable): CollisionInfo | null {
        if (!(body instanceof Body)) {
            return null;
        }

        const { x, y } = this;
        const { x: dx, y: dy } = this.#direction;

        const diffX = body.x - x;
        const diffY = body.y - y;

        const projectionFactor = Math.max(
            0,
            Math.min(1, (diffX * dx + diffY * dy) * this.#invertedLengthSquared),
        );

        return body.checkForCollision({
            x: x + dx * projectionFactor,
            y: y + dy * projectionFactor,
            radius: 0,
        });
    }
}
