import type { GameContext } from '@monkey-tilt/client';
import { createRoot, withNamespace } from '@monkey-tilt/ui';
import { createBaccaratClient, type BaccaratClientOptions } from './game/baccarat';
import { BaccaratUI, Game } from './ui';

export interface BaccaratRunOptions extends Omit<BaccaratClientOptions, 'root'> {
    readonly container: HTMLElement | string;
}

const assetsUrl = new URL(
    globalThis && globalThis.document.currentScript instanceof HTMLScriptElement
        ? globalThis.document.currentScript.src
        : location.href,
);

if (globalThis && globalThis.document.currentScript instanceof HTMLScriptElement) {
    const link = document.createElement('link');
    link.rel = 'stylesheet';
    link.href = new URL('./baccarat.css', assetsUrl).href;
    document.head.appendChild(link);
}

export async function run({ container, ...options }: BaccaratRunOptions): Promise<GameContext> {
    const element: HTMLElement | null =
        typeof container === 'string' ? document.querySelector(container) : container;

    if (!element) {
        throw new Error('Invalid container provided');
    }

    const handleResize = () => {
        element.classList.add(
            ...['s-app', 'l-cover', 'l-cover--stretch', 'm-baccarat-container'].map(withNamespace),
        );
        element.style.setProperty(`--${withNamespace('min-block-size')}`, '100%');
    };

    handleResize();

    const root = createRoot(element, () => {
        element.innerHTML = '';
    });

    const ui = new BaccaratUI({
        client: await createBaccaratClient({ ...options, root }),
        assetsUrl,
        root,
    });

    root.render(Game({ ui, className: { 'l-cover__principal': true } }));

    return {
        unmount: () => {
            ui.dispose();
        },
        onAction: (callback) => {
            ui.hostActionDispatcher = callback;
        },
        notify: (notification) => {
            if (notification.type === 'CONTAINER_RESIZED') {
                handleResize();
            }
            ui.handleHostNotification(notification);
        },
    };
}
