// Animation feature flags - set to false to disable specific animations
export const ENABLE_WIN_ANIMATION = true;   // Controls the floating/scaling win animation
export const ENABLE_CRASH_ANIMATION = true; // Controls the fade-out/explosion crash animation
export const TARGET_MONKEY_WIDTH = 140;

// Canvas dimensions
export const CANVAS_WIDTH = 1920 * 2;
export const CANVAS_HEIGHT = 1080 * 2;

// Graph configuration
export const MAX_DATA_POINTS = 1000; // Maximum data points to keep in memory
export const SAMPLING_INTERVAL = 11; // ~90fps
export const ANIMATION_DURATION = 8; // milliseconds for animation progress
export const PROPELLER_INTERVAL = 100; // milliseconds for propeller animation

// Animation durations
export const WIN_ANIMATION_DURATION = 16000; // 16 seconds (slower)
export const CRASH_ANIMATION_DURATION = 16000; // 16 seconds (slower, same as win)

// Layout constants
export const GRAPH_MARGIN_TOP = 0.1; // 10% margin on top
export const GRAPH_MARGIN_RIGHT = 0.1; // 10% margin on right
export const GRAPH_WIDTH_FACTOR = 0.9; // 90% of available width
export const GRAPH_HEIGHT_FACTOR = 0.9; // 90% of available height

// Multiplier display constants
export const MULTIPLIER_RECT_WIDTH = 180;
export const MULTIPLIER_RECT_HEIGHT = 70;
export const MULTIPLIER_PADDING = 10;
export const MULTIPLIER_BORDER_RADIUS = 8;

// Colors
export const COLORS = {
    WHITE: 0xffffff,
    BLACK: 0x000000,
    GREEN: 0x00ff00,
    RED: 0xff0000,
    BACKGROUND: 0xffffff,
    CLOUD: 0xaaaaaa,
} as const;

// Cloud particle configuration
export const CLOUD_CONFIG = {
    PARTICLES_PER_SEGMENT: 8,
    START_RADIUS_BASE: 4,
    END_RADIUS_BASE: 8,
    RADIUS_RANDOM_RANGE: 4,
    START_ALPHA_BASE: 0.25,
    END_ALPHA_BASE: 0.2,
    ALPHA_RANDOM_RANGE: 0.075,
    POSITION_RANDOM_RANGE: 12.5,
    VERTICAL_RANDOM_RANGE: 9,
} as const;

// Rendering configuration
export const RENDERING_CONFIG = {
    LINE_WIDTH: 4,
    ENHANCED_STROKE_WIDTH: 10,
    ENHANCED_STROKE_ALPHA: 0.1,
    SUPER_RESOLUTION_MULTIPLIER: 2,
    MAX_RESOLUTION_MULTIPLIER: 2,
    BACKGROUND_SCALE_MULTIPLIER: 1.5,
    MONKEY_OFFSET_X: -10,
    MONKEY_OFFSET_Y: -30,
    LINE_ROTATION_OFFSET: Math.PI / 9,
} as const;

// Calculate background height based on canvas dimensions and scale multiplier
export const BACKGROUND_HEIGHT = CANVAS_HEIGHT * RENDERING_CONFIG.BACKGROUND_SCALE_MULTIPLIER;

// Animation configuration
export const ANIMATION_CONFIG = {
    WIN_SCALE_MULTIPLIER: 0, // Disable scaling
    WIN_Y_OFFSET: BACKGROUND_HEIGHT, // Move up the full height of the background
    WIN_SWAY_AMPLITUDE: 5, // Reduced horizontal sway
    WIN_SWAY_FREQUENCY: 0.005, // Slower sway frequency
    WIN_ROTATION_AMPLITUDE: 0.1, // Reduced rotation
    CRASH_SCALE_MULTIPLIER: 1.5, // Scale from 1x to 2.5x
    CRASH_Y_OFFSET: BACKGROUND_HEIGHT, // Move down the full height of the background
    CRASH_ROTATION_AMPLITUDE: 0.15, // Rotation during crash
    PROPELLER_OSCILLATION: 0.8,
} as const;

// Mathematical constants
export const MATH_CONFIG = {
    TIME_STEP: 0.02,
    GROWTH_RATE: Math.pow(50, 1/10), // ≈ 1.445
    MAX_MULTIPLIER: 100, // @TODO: get from API
    Y_CURVE_POWER: 0.85,
    X_CURVE_POWER: 0.3,
    MIN_MAX_TIME: 10,
    RESIZE_THRESHOLD: 2,
} as const;

// Timing constants
export const TIMING_CONFIG = {
    ANIMATION_FRAME_TIME: 16, // ~60fps
    RESIZE_DEBOUNCE: 100,
    PROCESSING_TIMEOUT: 100,
    SVG_LOAD_TIMEOUT: 100,
} as const; 