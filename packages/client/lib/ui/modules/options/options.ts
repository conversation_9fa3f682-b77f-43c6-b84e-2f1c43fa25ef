import {
    Button,
    ButtonBar,
    defineComponent,
    mergeClasses,
    ModalControls,
    RefObject,
    withNamespace,
    type ButtonProps,
    type Component,
    type Props,
    type SignalReader,
} from '@monkey-tilt/ui';

interface StateManager {
    config: {
        soundEffects: {
            read: SignalReader<boolean>;
            update: (value: boolean | ((prevValue: boolean) => boolean)) => void;
        };
        hotkeys: {
            read: () => boolean;
        };
        turboMode: {
            read: SignalReader<boolean>;
            update: (value: boolean | ((prevValue: boolean) => boolean)) => void;
        };
    };
    hotkeysModalControlsRef: RefObject<ModalControls>;
    helpModalControlsRef?: RefObject<ModalControls>;
}
export interface OptionsProps extends Props<HTMLDivElement> {
    /**
     * The Game UI state manager.
     */
    readonly ui: StateManager;
    namespace?: string;
}

export const Options = defineComponent(
    'Options',
    ({ state }) =>
        ({ ui, namespace, ...props }: OptionsProps): Component<HTMLDivElement> => {
            const { effect } = state();

            const buttons: (ButtonProps & { signal?: SignalReader<boolean> })[] = [
                {
                    signal: ui.config.soundEffects.read,
                    onClick: () => ui.config.soundEffects.update((enabled) => !enabled),
                    icon: 'sound',
                    title: 'Sound effects',
                },
                {
                    icon: 'keyboard',
                    title: 'Hotkeys',
                    signal: ui.config.hotkeys.read,
                    onClick: () => ui.hotkeysModalControlsRef.current?.open(),
                    className: { 'u-hide@m': true },
                },
                {
                    icon: 'bolt',
                    title: 'Turbo Mode',
                    signal: ui.config.turboMode.read,
                    onClick: () => ui.config.turboMode.update((enabled) => !enabled),
                },
            ];

            if (ui.helpModalControlsRef) {
                buttons.push({
                    icon: 'info',
                    title: 'Help',
                    onClick: () => ui.helpModalControlsRef?.current?.open(),
                });
            }

            return ButtonBar(
                {
                    ...props,
                    className: mergeClasses(
                        props.className,
                        withNamespace(`m-${namespace}__options`),
                    ),
                },
                ...buttons.map(({ signal, ...button }) =>
                    Button({
                        ...button,
                        variant: 'secondary',
                        ref:
                            signal &&
                            ((element: HTMLButtonElement) => {
                                effect(() => {
                                    element.classList.toggle(withNamespace('is-active'), signal());
                                });
                            }),
                    }),
                ),
            );
        },
);
