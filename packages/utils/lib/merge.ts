import { isObject } from './equal';

export function deepMerge<T extends Record<string, unknown> | readonly unknown[]>(
    target: T,
    source: Partial<T>,
): T {
    if (!isObject(target) || !isObject(source)) {
        return source as unknown as T;
    }

    if (Array.isArray(source)) {
        if (!Array.isArray(target)) {
            return source as unknown as T;
        }

        const mergedArray = [...target] as unknown[];
        source.forEach((item, index) => {
            if (index < mergedArray.length) {
                mergedArray[index] = deepMerge(mergedArray[index] as T, item);
            } else {
                mergedArray.push(item);
            }
        });
        return mergedArray as unknown as T;
    } else if (Array.isArray(target)) {
        return source as unknown as T;
    }

    const result = { ...target };

    for (const key in source) {
        const value = source[key] as T[typeof key];

        if (value === undefined) {
            continue;
        }

        const targetValue = result[key] as T[typeof key];

        Object.assign(result, {
            [key]:
                !isObject(targetValue) || !isObject(value) ? value : deepMerge(targetValue, value),
        });
    }

    return result;
}
