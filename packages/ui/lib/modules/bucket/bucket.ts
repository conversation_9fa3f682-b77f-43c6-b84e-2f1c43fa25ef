import { defineComponent, html, type Component, type Props } from '../../component';
import type { Reactive } from '../../state';
import { mergeClasses, withNamespace } from '../../utilities/classes';

export const BucketPallette = [
    '#c60000',
    '#e41b1b',
    '#ef4444',
    '#f56d0b',
    '#f59e0b',
    '#fbbf24',
    '#fcd34d',
    '#fde68a',
    '#fef3c7',
];

const PALLETTE_SIZE = BucketPallette.length;

export interface BucketProps extends Props<HTMLDivElement> {
    /**
     * Multiplier value.
     */
    readonly value: Reactive<string>;

    /**
     * The color for this bucket (as a CSS <color> value).
     */
    readonly color?: string | undefined;
    /**
     * The variant for this bucket.
     */
    readonly variant?: 'standalone' | 'default';
}

export const Bucket = defineComponent(
    'Bucket',
    ({ state }) =>
        ({
            value,
            color = BucketPallette[0]!,
            variant = 'default',
            className,
            ...props
        }: BucketProps): Component<HTMLDivElement> => {
            const { onPropChange } = state();
            return html('div')(
                {
                    ...props,
                    className: mergeClasses(
                        withNamespace('m-bucket'),
                        variant === 'standalone'
                            ? withNamespace('m-bucket--standalone')
                            : undefined,
                        className,
                    ),
                    cssProps: {
                        'bucket-color': color,
                        'bucket-text-color': color === BucketPallette[0]! ? '#FFF' : undefined,
                    },
                },
                html('span')({
                    className: mergeClasses(
                        withNamespace('m-bucket__value'),
                        variant === 'standalone'
                            ? withNamespace('m-bucket__value--standalone')
                            : undefined,
                    ),
                    ref: (element: HTMLSpanElement) => {
                        onPropChange(value, (value) => {
                            element.textContent = `${Number(value)}x`;
                        });
                    },
                }),
            );
        },
);

export interface BucketRowControls {
    hitBucket(index: number): void;
}

export interface BucketRowProps extends Props<HTMLDivElement> {
    readonly multipliers: Reactive<string[]>;
    readonly controls?: ((controls: BucketRowControls) => void) | undefined;
    readonly onBucketFocus?: ((index: number) => void) | undefined;
    readonly onBlur?: (() => void) | undefined;
}

export function getBucketColor(index: number, totalBuckets: number): string {
    return BucketPallette[
        Math.round(
            (Math.min(index, totalBuckets - index - 1) * (PALLETTE_SIZE - 1)) /
                (Math.ceil(totalBuckets / 2) - 1),
        )
    ]!;
}

export const BucketRow = defineComponent(
    'BucketRow',
    ({ state, render }) =>
        ({
            multipliers,
            controls = () => {},
            onBucketFocus = () => {},
            onBlur = () => {},
            className,
            ...props
        }: BucketRowProps): Component<HTMLDivElement> => {
            const { onPropChange, memo, read } = state();

            let buckets: HTMLDivElement[] = [];
            let focusIndex = -1;

            const handleBucketFocus = (event: Event) => {
                const index = buckets.indexOf(event.target as HTMLDivElement);
                if (index !== -1 && index !== focusIndex) {
                    focusIndex = index;
                    onBucketFocus(index);
                }
            };

            const handleBlur = () => {
                focusIndex = -1;
                onBlur();
            };

            return html('div')({
                ...props,
                className: mergeClasses(withNamespace('m-bucket__row'), className),
                onMousemove: handleBucketFocus,
                onMouseleave: handleBlur,
                ref: (element: HTMLDivElement) => {
                    let hasFocus = false;

                    element.addEventListener('focusin', (event: FocusEvent) => {
                        hasFocus = true;
                        handleBucketFocus(event);
                    });
                    element.addEventListener('focusout', () => {
                        hasFocus = false;
                        setTimeout(() => {
                            if (!hasFocus) {
                                handleBlur();
                            }
                        }, 50);
                    });

                    const hitClassName = withNamespace('m-bucket--hit');

                    controls({
                        hitBucket: (index: number) => {
                            const bucket = buckets[index];
                            if (bucket) {
                                if (!bucket.classList.contains(hitClassName)) {
                                    bucket.classList.add(hitClassName);
                                } else {
                                    bucket.classList.remove(hitClassName);
                                    bucket.offsetWidth; // forces reflow
                                    bucket.classList.add(hitClassName);
                                }
                            }
                        },
                    });

                    const bucketCount = memo(() => read(multipliers).length);
                    const bucketCountProp = `--${withNamespace('bucket-count')}`;

                    onPropChange(bucketCount, (count) => {
                        const values = read(multipliers).map((_, i) =>
                            memo(() => read(multipliers)[i]!),
                        );

                        element.innerHTML = '';
                        element.style.setProperty(bucketCountProp, count.toString());

                        buckets = [];

                        for (let i = 0; i < count; i++) {
                            const bucket = render(
                                Bucket({
                                    value: values[i]!,
                                    color: getBucketColor(i, count),
                                    tabIndex: 0,
                                    'data-index': i.toString(),
                                }),
                            );
                            buckets.push(bucket);
                            element.appendChild(bucket);
                        }
                    });
                },
            });
        },
);
