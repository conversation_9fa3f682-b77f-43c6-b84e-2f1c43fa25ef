/**
 * Options for the request() function.
 */
export interface RequestOptions<TRequest, TResponse> extends RequestInit {
    /**
     * Optional function to prepare the request options before sending.
     *
     * Default implementation will set the Content-Type header to application/json
     * if the data is not null or undefined, and it will serialize the data to JSON.
     *
     * @param options - The RequestInit.
     * @param data - The request data.
     * @returns The RequestInit object with the prepared body.
     */
    prepare?: (options: RequestInit, data?: TRequest) => RequestInit;

    /**
     * Optional function to handle the response before returning.
     *
     * Default implementation will return the response as parsed JSON if the Content-Type
     * header includes 'json', as string if it includes 'text', and as a Blob otherwise.
     *
     * @param response - The Response object.
     * @returns The response data.
     */
    handle?: (response: Response) => Promise<TResponse>;

    /**
     * Whether to automatically append a cache-busting query parameter to GET requests.
     */
    autoUrlCacheBust?: boolean;
}

/**
 * A simple wrapper around the fetch() function.
 *
 * @param method - The HTTP method.
 * @param url - The request URL.
 * @param data - The request data. If method is GET, this will be serialized to query parameters.
 * @param options - Additional RequestOptions.
 * @returns A promise that resolves to the response data.
 */
export async function request<TRequest, TResponse>(
    method: 'GET' | 'POST' | 'PUT' | 'PATCH' | 'DELETE',
    url: string | URL,
    data?: TRequest,
    {
        prepare = defaultRequestBuilder,
        handle = defaultResponseHandler,
        autoUrlCacheBust = true,
        ...options
    }: RequestOptions<TRequest, TResponse> = {},
): Promise<TResponse> {
    method = method.toUpperCase() as typeof method;

    const requestUrl = new URL(url, globalThis.location?.origin);

    if (method === 'GET') {
        if (autoUrlCacheBust) {
            requestUrl.searchParams.append('ts', Date.now().toString());
        }
        if (data) {
            for (const key in data) {
                requestUrl.searchParams.append(key, String(data[key]));
            }
            data = undefined;
        }
    }

    const response = await fetch(requestUrl, { ...prepare(options, data), method });

    return handle(response);
}

/**
 * Sends a GET request.
 *
 * @param url - The request URL.
 * @param queryParams - The query parameters.
 * @param options - Additional RequestOptions.
 * @returns A promise that resolves to the response data.
 */
export function get<TRequest, TResponse>(
    url: string | URL,
    queryParams?: TRequest,
    options?: RequestOptions<TRequest, TResponse>,
): Promise<TResponse> {
    return request('GET', url, queryParams, options);
}

/**
 * Sends a POST request.
 *
 * @param url - The request URL.
 * @param data - The request data.
 * @param options - Additional RequestOptions.
 * @returns A promise that resolves to the response data.
 */
export function post<TRequest, TResponse>(
    url: string | URL,
    data?: TRequest,
    options?: RequestOptions<TRequest, TResponse>,
): Promise<TResponse> {
    return request('POST', url, data, options);
}

function defaultRequestBuilder<T>(options: RequestInit, data?: T): RequestInit {
    return {
        ...options,
        headers: {
            ...(options.headers ?? {}),
            ...(data ? { 'Content-Type': 'application/json' } : {}),
        },
        body: data ? JSON.stringify(data) : (options.body ?? null),
    };
}

function defaultResponseHandler<T>(response: Response): Promise<T> {
    if (response.headers.get('Content-Type')?.includes('json')) {
        return response.json() as Promise<T>;
    }
    if (response.headers.get('Content-Type')?.includes('text')) {
        return response.text() as Promise<T>;
    }
    return response.blob() as Promise<T>;
}
