export class MessageQueue<T> {
    #queue: T[] = [];
    #intervalId: ReturnType<typeof setInterval> | null = null;
    #interval: number;
    #handler: (item: T) => void | Promise<void>;

    constructor(handler: (item: T) => void | Promise<void>, interval = 400) {
        this.#handler = handler;
        this.#interval = interval;
    }

    push(item: T) {
        this.#queue.push(item);
        this.#start();
    }

    clear() {
        this.#queue = [];
        this.#stop();
    }

    get isEmpty() {
        return this.#queue.length === 0;
    }

    #start() {
        if (this.#intervalId !== null) {
            return;
        }

        this.#intervalId = setInterval(async () => {
            const next = this.#queue.shift();
            if (!next) {
                this.#stop();
                return;
            }

            try {
                await this.#handler(next);
            } catch (err) {
                console.error('Queue handler error:', err);
            }

            if (this.#queue.length === 0) {
                this.#stop();
            }
        }, this.#interval);
    }

    #stop() {
        if (this.#intervalId !== null) {
            clearInterval(this.#intervalId);
            this.#intervalId = null;
        }
    }
}
