import { logDevError } from '@monkey-tilt/utils';
import { Board, type BoardOptions } from '../../physics/board';
import { CELL_SIZE } from '../../physics/constants';
import { Ball } from './ball';
import { Peg, PegHitFeedback } from './peg';
import type { BallData, Particle } from './types';
import { getPegLatticeImage } from './utils/image';
import { getStartingPositions, type StartingPositionsPool } from './utils/starting-positions';
import { DPI } from './utils/dpi';

/**
 * Options for creating a PlinkoRenderer.
 */
export interface PlinkoRendererOptions {
    /**
     * Called when a ball hits a bucket.
     *
     * @param ballData - The data associated with the ball that hit the bucket.
     */
    readonly onBucketHit?: (ballData: BallData) => void;
    /**
     * Called when a peg is hit by a ball.
     *
     * @param row - The row of the peg that was hit.
     * @param column - The column of the peg that was hit.
     */
    readonly onPegHit?: (row: number, column: number) => void;
    /**
     * Called when the board is idle (i.e., no balls are in play).
     */
    readonly onIdle?: () => void;
    /**
     * The number of rows in the peg lattice.
     */
    readonly numberOfRows?: number;
    /**
     * Whether to enable turbo mode at the start.
     */
    readonly isTurboMode?: boolean;
}

const SIMULATION_TIME_STEP = 1 / 30; // 30 FPS for physics simulation

export class PlinkoRenderer {
    #onBucketHit: ((ballData: BallData) => void) | undefined;
    #onPegHit: ((row: number, column: number) => void) | undefined;
    #onIdle: (() => void) | undefined;

    #context: CanvasRenderingContext2D;
    #pegLatticeImage!: CanvasImageSource;

    #board!: Board<Ball, Peg>;
    #boardOptions: Omit<BoardOptions<Peg>, 'rows'>;

    #startingPositions!: StartingPositionsPool;

    #ballData: WeakMap<Ball, BallData> = new WeakMap();

    #pendingBalls: BallData[] = [];
    #isReady = false;
    #isIdle = true;
    #isTurboMode: boolean;

    #previousFrameTime!: number;
    #accumulatedTime!: number;

    #particles: Particle[] = [];

    #width!: number;
    #height!: number;

    public constructor(
        canvas: HTMLCanvasElement,
        {
            onBucketHit,
            onPegHit,
            onIdle,
            numberOfRows = 8,
            isTurboMode = false,
        }: PlinkoRendererOptions = {},
    ) {
        this.#onBucketHit = onBucketHit;
        this.#onPegHit = onPegHit;
        this.#onIdle = onIdle;
        this.#isTurboMode = isTurboMode;

        const context = canvas.getContext('2d');

        if (context === null) {
            throw new Error('Failed to get 2D context');
        }

        this.#context = context;

        this.#boardOptions = {
            createPeg: (column, row, ...args) => {
                return new Peg(column, row, ...args, () =>
                    this.#handleBallPegCollision(row, Math.ceil(column)),
                );
            },
        };

        DPI.onChange(this.#resizeCanvas);

        this.#init(numberOfRows).catch((err) => logDevError('Failed to initialize renderer', err));
    }

    public dropBall(data: BallData): void {
        this.#isIdle = false;

        if (this.#isReady) {
            this.#dropBall(data);
        } else {
            this.#pendingBalls.push(data);
        }
    }

    #dropBall(data: BallData): void {
        const ball = new Ball(data.risk, ...this.#startingPositions.next(data.bucketNumber));

        this.#ballData.set(ball, data);
        this.#board.addBall(ball);
    }

    #handleBallPegCollision = (row: number, column: number): void => {
        const { x, y } = this.#board.pegs[row]![column]!;
        this.#particles.push(new PegHitFeedback(x, y));
        if (this.#onPegHit) {
            this.#onPegHit(row, column);
        }
    };

    #gameLoopFrameId: ReturnType<typeof requestAnimationFrame> | null = null;

    #handleFrame = (): void => {
        if (!this.#isReady) {
            return;
        }

        const now = performance.now() / 1000;
        let dt = now - this.#previousFrameTime;
        this.#previousFrameTime = now;

        if (dt > 0.25) {
            dt = 0.25;
        }

        this.#accumulatedTime += dt;

        while (this.#accumulatedTime >= SIMULATION_TIME_STEP) {
            this.#accumulatedTime -= SIMULATION_TIME_STEP;

            this.#board.update(SIMULATION_TIME_STEP);
            if (this.#isTurboMode) {
                this.#board.update(SIMULATION_TIME_STEP);
            }

            for (const particle of this.#particles) {
                particle.update(SIMULATION_TIME_STEP);
                if (this.#isTurboMode) {
                    particle.update(SIMULATION_TIME_STEP);
                }
            }
        }

        const alpha = this.#accumulatedTime / SIMULATION_TIME_STEP;

        this.#context.setTransform(1, 0, 0, 1, 0, 0);
        this.#context.scale(DPI.current, DPI.current);

        this.#context.clearRect(0, 0, this.#width, this.#height);

        this.#context.translate(0, CELL_SIZE / 3);

        this.#context.drawImage(this.#pegLatticeImage, 0, 0, this.#width, this.#height);

        for (const ball of this.#board.balls) {
            ball.render(this.#context, alpha);
        }

        for (const particle of this.#particles) {
            particle.render(this.#context, alpha);
        }

        for (let i = this.#particles.length - 1; i >= 0; i--) {
            if (!this.#particles[i]!.isAlive) {
                this.#particles.splice(i, 1);
            }
        }

        this.#gameLoopFrameId = requestAnimationFrame(this.#handleFrame);
    };

    async #init(rows: number): Promise<void> {
        if (!this.isIdle) {
            return;
        }

        if (this.#gameLoopFrameId !== null) {
            cancelAnimationFrame(this.#gameLoopFrameId);
            this.#gameLoopFrameId = null;
        }

        this.#isIdle = true;
        this.#isReady = false;
        this.#pendingBalls = [];
        this.#particles = [];

        this.#board = new Board({
            rows,
            ...this.#boardOptions,
        });

        this.#board.addEventListener('boardEmpty', () => {
            this.#isIdle = true;
            this.#onIdle?.();
        });

        if (this.#onBucketHit) {
            this.#board.addEventListener('hit', (event) => {
                const data = this.#ballData.get(event.detail.ball);
                if (data) {
                    this.#onBucketHit!(data);
                }
            });
        }

        this.#resizeCanvas();
        this.#startingPositions = await getStartingPositions(rows);

        this.#isReady = true;

        this.#previousFrameTime = performance.now() / 1000;
        this.#accumulatedTime = 0;

        while (this.#pendingBalls.length > 0) {
            this.#dropBall(this.#pendingBalls.shift()!);
        }

        this.#gameLoopFrameId = requestAnimationFrame(this.#handleFrame);
    }

    #resizeCanvas = (): void => {
        if (!this.#context || !this.#board) {
            return;
        }

        this.#width = this.#board.columns * CELL_SIZE;
        this.#height = (this.#board.rows + 1) * CELL_SIZE;

        const dpi = DPI.current;

        this.#context.canvas.width = this.#width * dpi;
        this.#context.canvas.height = this.#height * dpi;
        this.#context.canvas.style.width = `${this.#width}px`;
        this.#context.canvas.style.aspectRatio = `${this.#width}/${this.#height}`;

        this.#context.scale(dpi, dpi);

        this.#pegLatticeImage = getPegLatticeImage(this.#board.rows, dpi);
    };

    public get isIdle(): boolean {
        return this.#isIdle;
    }

    public set numberOfRows(rows: number) {
        if (this.#board.rows !== rows) {
            this.#init(rows).catch((err) => logDevError('Failed to initialize renderer', err));
        }
    }

    public set isTurboMode(value: boolean) {
        this.#isTurboMode = value;
    }
}
