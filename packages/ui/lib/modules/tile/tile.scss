@use '../../styles/common/bem';
@use '../../styles/common/prop';
@use '../../styles/common/respond';
@use '../../styles/common/util';


$gradient-selected: linear-gradient(145deg, #ad9522 0%, #352e0a 100%);
$gradient-hit: linear-gradient(135deg, #6b5b12 0%, #362d06 100%);
$gradient-hover: linear-gradient(135deg, #ad9522 0%, #ffe885 100%);

$border-selected: 2px solid #ffe885;
$border-bottom-selected: 6px solid #ffe885;

$border-hit: 1px solid #ffe885;
$border-bottom-hit: 6px solid #ffe885;

$border-missed: 2px solid prop.get(color-zinc-900);
$border-bottom-missed: 2px solid prop.get(color-zinc-900);

$diamond-url: url(../modules/tile/assets/diamond.svg);

@mixin state-style($background, $border, $border-bottom) {
    background: $background;
    border: $border;
    border-bottom: $border-bottom;
}

@mixin styles {
    @include bem.module('tile') {
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: 500;
        border-radius: util.to-rem(16px);
        background-color: prop.get(color-zinc-800);
        color: prop.get(color-white);
        cursor: pointer;
        transition: all 0.3s ease;
        position: relative;

        border: util.to-rem(2px) solid prop.get(color-zinc-700);
        border-bottom: util.to-rem(6px) solid prop.get(color-zinc-700);

        inline-size: 100%;
        block-size: 100%;
        min-inline-size: none;
        min-block-size: none;
        aspect-ratio: 1 / 1;
        font-size: clamp(1rem, 1.75vw, 1.85rem);

        @include respond.until('m') {
            border-block-end-width: util.to-rem(3px);
            border-radius: util.to-rem(10px);
        }

        @include respond.until('428px') {
            font-size: 0.8rem;
        }

        &__number {
            z-index: 2;
            font-weight: 600;
            white-space: nowrap;
        }

        &--selected {
            @include state-style($gradient-selected, $border-selected, $border-bottom-selected);

            @include respond.until('m') {
                border-width: util.to-rem(1.5px);
                border-block-end-width: util.to-rem(4px);
            }
        }

        &--hit {
            @include state-style($gradient-hit, $border-hit, $border-bottom-hit);
            color: prop.get(color-black);

            * {
                font-weight: 700;
            }

            &::after {
                content: '';
                position: absolute;
                inset: 0;
                background: $diamond-url center/100% no-repeat;
                pointer-events: none;
            }
        }

        &--miss {
            @include state-style(prop.get(color-zinc-950), $border-missed, $border-bottom-missed);
            color: prop.get(color-red-500);
            border-block-end-width: util.to-rem(2px) !important;
        }

        &--disabled {
            pointer-events: none;
            cursor: not-allowed;
        }

        &:hover:not(&--disabled) {
            opacity: 0.9;
            transform: scale(1.03);
        }
    }

    @include bem.module('tile-board') {
        display: grid;
        grid-template-columns: repeat(8, 1fr);
        grid-template-rows: repeat(5, 1fr);
        gap: 1rem;
        aspect-ratio: 8 / 5;
        inline-size: 100%;
        max-inline-size: 100%;
        justify-content: center;

        @include respond.until('920px') {
            gap: 0.5rem;
        }
    }
}
