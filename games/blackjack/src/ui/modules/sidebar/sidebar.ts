import {
    formatCurrency,
    timer,
    type GameActionName,
    BetAmount,
    Options,
} from '@monkey-tilt/client';
import {
    Button,
    defineComponent,
    Frame,
    html,
    Label,
    namespaced,
    Tabs,
    withNamespace,
    type Component,
    type IconName,
    type LabelControls,
    type Props,
    type RefObject,
    type TabControls,
} from '@monkey-tilt/ui';
import type { BlackjackUI } from '../../blackjack';

export interface SidebarProps extends Props<HTMLDivElement> {
    /**
     * The Blackjack UI state manager.
     */
    readonly ui: BlackjackUI;
}

export const Sidebar = defineComponent(
    'Sidebar',
    ({ state }) =>
        ({ ui, ...props }: SidebarProps): Component<HTMLDivElement> => {
            const { effect, memo } = state();

            const div = html('div');

            effect(() => {
                const {
                    bet_amount,
                    perfect_pairs_amount = '0.00',
                    poker_bet_amount = '0.00',
                } = ui.gameState();

                ui.betAmountValue.update(bet_amount);
                ui.perfectPairAmountValue.update(perfect_pairs_amount);
                ui.pokerAmountValue.update(poker_bet_amount);
            });

            const actionState = memo(() => {
                const {
                    hand_owner,
                    next_actions = [],
                    round_closed = true,
                    readyState = 'closed',
                    isAuthenticated = false,
                    isLoading,
                    isAnimatingState,
                } = ui.gameState();
                return {
                    hand_owner,
                    next_actions,
                    round_closed,
                    canPlay:
                        !isLoading &&
                        !isAnimatingState &&
                        readyState === 'ready' &&
                        isAuthenticated,
                };
            });

            const bettingLocked = memo(() => {
                const { next_actions, round_closed, canPlay } = actionState();
                return !(
                    canPlay &&
                    (next_actions.includes('Bet') || (next_actions.length == 0 && round_closed))
                );
            });

            const betAmountLimits = memo(() => ui.gameState().bet_limits.bet);
            const perfectPairBetAmountLimits = memo(() => ui.gameState().bet_limits.perfect_pairs);
            const pokerBetAmountLimits = memo(() => ui.gameState().bet_limits.poker);

            const betAmountTooltip = memo(
                () => `Maximum Bet: ${formatCurrency(betAmountLimits().max, ui.currency())}`,
            );
            const perfectPairBetAmountTooltip = memo(
                () =>
                    `Maximum Bet: ${formatCurrency(
                        perfectPairBetAmountLimits().max,
                        ui.currency(),
                    )}`,
            );
            const pokerBetAmountTooltip = memo(
                () => `Maximum Bet: ${formatCurrency(pokerBetAmountLimits().max, ui.currency())}`,
            );

            const handleInsuranceAvailable =
                ({ show }: { show: boolean }) =>
                (element: HTMLElement) => {
                    effect(() => {
                        const { next_actions, canPlay } = actionState();
                        element.classList.toggle(
                            withNamespace('u-hide'),
                            canPlay && next_actions.includes('Peek') ? !show : show,
                        );
                    });
                };

            const hideOnMobile = withNamespace('u-hide@m');

            const handleActionAvailable =
                (action: GameActionName) => (element: HTMLButtonElement) => {
                    const delay = timer(300);
                    effect(() => {
                        const { next_actions, hand_owner, canPlay } = actionState();
                        const isDisabled =
                            !canPlay || hand_owner == 'Dealer' || !next_actions.includes(action);

                        element.disabled = isDisabled;
                        delay(() => {
                            element.classList.toggle(hideOnMobile, isDisabled);
                        });
                    });
                };

            const actionButton = (
                action: 'Hit' | 'Stand' | 'Surrender' | 'Double' | 'Split',
                icon: IconName,
            ) =>
                Button({
                    label: action,
                    icon,
                    ref: handleActionAvailable(action),
                    onClick: () => ui.triggerAction(action),
                });

            const betButton = Button({
                cta: true,
                label: 'Bet',
                icon: 'plus',
                className: { 'm-blackjack__bet-button': true },
                ref: (element: HTMLButtonElement) => {
                    effect(() => {
                        const isDisabled = bettingLocked();
                        element.disabled = isDisabled;
                        element.classList.toggle(hideOnMobile, isDisabled);
                    });
                    handleInsuranceAvailable({ show: false })(element);
                },
                onClick() {
                    ui.triggerAction('Bet');
                },
            });

            const activeBetLabelControls: RefObject<LabelControls> = { current: null };
            let activeBetText: string | undefined = undefined;

            effect(() => {
                if (ui.lastAction() == 'Bet') {
                    const { bet_amount } = ui.gameState();
                    activeBetText = `Current bet: ${formatCurrency(parseFloat(bet_amount), ui.currency())}`;
                    if (activeBetLabelControls.current) {
                        activeBetLabelControls.current.setText(activeBetText);
                    }
                }
            });

            return Frame(
                { ...props, className: { 'm-blackjack__sidebar': true } },

                Frame.Section(
                    {
                        variant: 'subtle',
                        className: { 'm-blackjack__tabs': true },
                        ref: (element: HTMLDivElement) => {
                            effect(() => {
                                element.classList.toggle(hideOnMobile, bettingLocked());
                            });
                        },
                    },
                    Tabs({
                        ariaLabel: 'Bet type',
                        value: ui.activeTab.read(),
                        tabs: [
                            { label: 'Standard', value: 'standard' },
                            { label: 'Side Bet', value: 'sidebet' },
                        ],
                        onChange(value: 'standard' | 'sidebet') {
                            ui.activeTab.update(value);
                        },
                        controls({ selectTab, setEnabled }: TabControls) {
                            effect(() => {
                                selectTab(ui.activeTab.read());
                            });
                            effect(() => {
                                setEnabled(!bettingLocked());
                            });
                        },
                    }),
                ),

                Frame.Section(
                    { variant: 'subtle', className: { 'm-blackjack__bet': true } },
                    betButton,
                    Label({
                        variant: 'light',
                        className: { 'u-hide': true },
                        ref(element: HTMLSpanElement) {
                            effect(() => {
                                element.classList.toggle(
                                    withNamespace('u-show@m'),
                                    bettingLocked(),
                                );
                            });
                        },
                        controls(labelControls: LabelControls) {
                            activeBetLabelControls.current = labelControls;
                            if (activeBetText) {
                                labelControls.setText(activeBetText);
                            }
                        },
                    }),
                ),

                Frame.Section(
                    {
                        variant: 'subtle',
                        className: { 'u-gap--l': true, 'm-blackjack__controls': true },
                    },

                    div(
                        {
                            className: { 'l-stack': true },
                            ref: (element: HTMLDivElement) => {
                                effect(() => {
                                    element.classList.toggle(hideOnMobile, bettingLocked());
                                });
                            },
                        },

                        BetAmount({
                            label: 'Bet Amount',
                            tooltip: betAmountTooltip,
                            value: ui.betAmountValue,
                            disabled: bettingLocked,
                            presets: [0.1, 1, 10, 100],
                            limits: betAmountLimits,
                            currency: ui.currency,
                            conversionFactor: ui.usdRate,
                        }),

                        div(
                            {
                                className: { 'l-stack': true },
                                ref(element: HTMLDivElement) {
                                    effect(() => {
                                        element.classList.toggle(
                                            withNamespace('u-hide'),
                                            ui.activeTab.read() !== 'sidebet',
                                        );
                                    });
                                },
                            },

                            BetAmount({
                                label: 'Side Bet (Perfect Pairs)',
                                tooltip: perfectPairBetAmountTooltip,
                                value: ui.perfectPairAmountValue,
                                disabled: bettingLocked,
                                limits: perfectPairBetAmountLimits,
                                currency: ui.currency,
                                conversionFactor: ui.usdRate,
                            }),

                            BetAmount({
                                label: 'Side Bet (21 + 3)',
                                tooltip: pokerBetAmountTooltip,
                                value: ui.pokerAmountValue,
                                disabled: bettingLocked,
                                limits: pokerBetAmountLimits,
                                currency: ui.currency,
                                conversionFactor: ui.usdRate,
                            }),
                        ),
                    ),

                    div(
                        {
                            className: { 'm-blackjack__actions': true },
                            ref: handleInsuranceAvailable({ show: false }),
                        },
                        actionButton('Hit', 'raise'),
                        actionButton('Stand', 'stop'),
                        actionButton('Split', 'cards'),
                        actionButton('Double', 'coins'),
                        actionButton('Surrender', 'flag'),
                    ),

                    div(
                        {
                            className: { 'l-stack': true, 'u-gap--m': true },
                            ref: handleInsuranceAvailable({ show: true }),
                        },
                        Label({
                            text: 'Would you like insurances?',
                            variant: 'medium',
                        }),
                        Button({
                            cta: true,
                            label: 'Accept Insurance',
                            variant: 'secondary',
                            onClick() {
                                ui.triggerAction('Insure', true);
                            },
                        }),
                        Button({
                            cta: true,
                            label: 'No Insurance',
                            variant: 'tertiary',
                            onClick() {
                                ui.triggerAction('Insure', false);
                            },
                        }),
                    ),

                    div({ className: { 'l-flex': true } }, betButton),
                ),

                Frame.Section(
                    {
                        className: {
                            'l-stack__split': true,
                            'l-stack': true,
                            'u-gap--s': true,
                        },
                    },
                    Options({ ui, namespace: 'blackjack' }),
                    html('button')(
                        {
                            onClick(e) {
                                e.preventDefault();
                                ui.provablyFairModalControlsRef.current?.open();
                            },
                            className: { 'u-text--center': true },
                            css: { 'border-width': '0' },
                        },
                        Label(
                            { variant: 'subtle', icon: 'double-check' },
                            html('span')({ className: namespaced('u-hide@m') }, 'This Game is'),
                            'Provably Fair',
                        ),
                    ),
                ),
            );
        },
);
