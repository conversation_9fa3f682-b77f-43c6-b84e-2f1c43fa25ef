import {
    Button,
    defineComponent,
    Flex,
    html,
    Modal,
    Stack,
    type Component,
    type ModalControls,
    type ModalProps,
    type Renderable,
} from '@monkey-tilt/ui';
import type { BaccaratUI } from '../../baccarat';

export interface HelpProps extends ModalProps {
    /**
     * The Baccarat UI state manager.
     */
    readonly ui: BaccaratUI;
}

export const Help = defineComponent(
    'Help',
    () =>
        ({ ui, ...props }: HelpProps): Component<HTMLDialogElement> => {
            const p = html('p'),
                h2 = html('h2'),
                ul = html('ul'),
                li = html('li');

            const strong = (content: Renderable) => html('strong')(null, content);

            const section = (title: Renderable, content: Renderable) =>
                Stack({ gap: 's' }, h2({ className: { 'u-text--caption': true } }, title), content);

            const bulletList = (items: Renderable[]) =>
                ul(
                    { className: { 'l-stack': true, 'u-gap--xs': true } },
                    ...items.map((item) => li(null, item)),
                );

            return Modal(
                {
                    title: 'What is Baccarat?',
                    controls: (controls: ModalControls) => {
                        ui.helpModalControlsRef.current = controls;
                    },
                    gap: 'l',
                    wide: true,
                    ...props,
                },
                p(
                    null,
                    'Baccarat is a casino card game where players bet on which hand—Player, Banker, ',
                    'or Tie—will be closest to a\xA0total\xA0value\xA0of\xA09.',
                ),
                section(
                    'How to Play Baccarat',
                    bulletList([
                        'The goal is to bet on the hand that will come closest to 9 in value.',
                        'Dragon 7 and Panda 8 side bets are also available (explained below).',
                    ]),
                ),
                section(
                    'Baccarat Rules',
                    bulletList([
                        'The Player and Banker are dealt two cards each, with totals ranging from 0 to 9.',
                        'The value of the 1-9 cards are equivalent to their face value. Kings, Queens, Jacks and Tens have a value of 0.',
                        'If a hand’s total value exceeds 9, only the second digit counts (e.g., 15 becomes 5).',
                        'The Player or Banker wins depending on whose hand value is closer to 9. If both hands have the same total, it’s a Tie.',
                    ]),
                ),
                section(
                    'Player Rules',
                    bulletList([
                        'If the first two cards in a player’s hand have a total value of 8 or 9 (called a “natural”), no third card is drawn.',
                        'The Player stands if their hand value is 6 or 7.',
                        'The Player draws a third card if their hand value is 0-5, unless the Banker has a natural 8 or 9.',
                    ]),
                ),
                section('Banker Rules', [
                    strong('When the value of the Banker’s first two cards total:'),
                    bulletList([
                        [
                            strong('0/1/2'),
                            ' - The Banker always draws a third card, unless the Player has a natural 8 or 9.',
                        ],
                        [
                            strong('3'),
                            ' - The Banker draws if the Player’s third card is 0-7 or 9; the Banker stands if the Player has 8.',
                        ],
                        [
                            strong('4'),
                            ' - The Banker draws if the Player’s third card is 2-7; the Banker stands when the player has 0/1/8/9.',
                        ],
                        [
                            strong('5'),
                            ' - The Banker draws if the Player’s third card is 4-7; the Banker stands when the player has 0-3 or 8-9.',
                        ],
                        [
                            strong('6'),
                            ' - The Banker draws if the Player’s third card is 6-7; the Banker stands when the player has 0-5 or 8-9.',
                        ],
                        [strong('7'), ' - The Banker always stands.'],
                        [strong('8-9'), ' - Neither the Player nor Banker draws a third card.'],
                    ]),
                ]),

                section(
                    'Dragon 7 Push on Banker Rule',
                    bulletList([
                        [
                            'If the Banker wins with a ‘Dragon 7’ (a hand of three cards with a total value of exactly 7), ',
                            'all bets on the Banker hand are returned to the player as a push.',
                        ],
                    ]),
                ),

                section(
                    'Dragon 7 and Panda 8 Side Bets',
                    bulletList([
                        [
                            'Panda 8: This side bet wins if the ‘Player’ hand wins with 3 cards in hand that ',
                            'have a total value of exactly 8.',
                        ],
                        [
                            'Dragon 7: This side bet wins if the ‘Banker’ hand wins with 3 cards in hand that ',
                            'have a total value of 7.',
                        ],
                    ]),
                ),

                Flex(
                    { justify: 'center' },
                    Button(
                        {
                            css: { flex: '0' },
                            onClick: () => ui.helpModalControlsRef.current?.close(),
                        },
                        'Close',
                    ),
                ),
            );
        },
);
