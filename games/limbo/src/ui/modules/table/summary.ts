import { formatCurrency } from '@monkey-tilt/client';
import {
    Button,
    defineComponent,
    Flex,
    Input,
    Label,
    Modal,
    Stack,
    Table,
    type Component,
    type InputControls,
    type ModalControls,
    type ModalProps,
} from '@monkey-tilt/ui';
import type { RoundSummary } from '../../../game/state';
import type { LimboUI } from '../../limbo';

export interface SummaryModalControls {
    /**
     * Closes the modal.
     */
    close(): void;

    /**
     * Shows the modal.
     */
    open(summary: RoundSummary): void;
}

export interface SummaryProps extends Omit<ModalProps, 'controls'> {
    /**
     * The Limbo UI state manager.
     */
    readonly ui: LimboUI;

    readonly controls: (controls: SummaryModalControls) => void;
}

export const Summary = defineComponent(
    'Summary',
    ({ state }) =>
        ({ ui, controls, ...props }: SummaryProps): Component<HTMLDialogElement> => {
            const { signal, effect, memo } = state();
            const [data, setData] = signal({
                currency: 'USD',
                user: 'E***j',
                time: new Date(),
                round_id: '',
                bet_amount: '0',
                total_payout: '0',
                multiplier: '1.01',
                random_multiplier: '1.00',
                hash_round: '6fa2ff6474812773330cbf57c34836da6cdb29b0df92cc8',
                random_seed: '4.34_igf722PRhc286eYSOiA5dpUIW',
            });
            return Modal(
                {
                    title: 'Bets',
                    controls: (modal: ModalControls) => {
                        controls({
                            close: modal.close,
                            open(summary: RoundSummary) {
                                setData((prev) => ({
                                    ...prev,
                                    ...summary,
                                }));
                                modal.open();
                            },
                        });
                    },
                    ...props,
                },
                Flex(
                    {
                        justify: 'space-between',
                    },
                    Label({
                        variant: 'light',
                        text: memo(() => `Placed by ${data().user}`),
                    }),
                    Label({
                        variant: 'light',
                        text: memo(() =>
                            data()
                                .time.toISOString()
                                .replace(/^([^T]+)T(\d+:\d+).+$/, (_, d, t) => `${d} ${t}`),
                        ),
                    }),
                ),
                Stack(
                    { gap: 's', center: true },
                    Label({ variant: 'medium', text: 'Limbo' }),
                    Flex(
                        { gap: 's' },
                        Label({ variant: 'subtle', text: memo(() => data().round_id) }),
                        Button({
                            icon: 'copy',
                            onClick: () => {
                                navigator.clipboard.writeText(data().round_id);
                            },
                        }),
                    ),
                ),
                Table({
                    rows: memo(() => {
                        const { currency, bet_amount, multiplier, total_payout } = data();
                        return [
                            { label: 'Bet', value: formatCurrency(Number(bet_amount), currency) },
                            { label: 'Multiplier', value: multiplier + 'x' },
                            {
                                label: 'Payout',
                                value: formatCurrency(Number(total_payout), currency),
                            },
                        ];
                    }),
                }),
                Flex(
                    { gap: 'm' },
                    Label.Group({
                        text: Label({ variant: 'subtle' }, 'Target'),
                        value: Label({
                            variant: 'light',
                            text: memo(() => `${data().multiplier}x`),
                        }),
                    }),
                    Label.Group({
                        text: Label({ variant: 'subtle' }, 'Result'),
                        value: Label({
                            variant: 'highlight',
                            text: memo(() => `${data().random_multiplier}x`),
                        }),
                    }),
                ),
                Stack(
                    { gap: 'm' },
                    Label({ variant: 'medium', text: 'Hash round' }),
                    Input({
                        controls({ update }: InputControls) {
                            effect(() => {
                                update(data().hash_round);
                            });
                        },
                        readOnly: true,
                        action: Button({
                            icon: 'copy',
                            onClick: () => navigator.clipboard.writeText(data().hash_round),
                        }),
                    }),
                ),
                Stack(
                    { gap: 'm' },
                    Label({ variant: 'medium', text: 'Random Seed' }),
                    Input({
                        controls({ update }: InputControls) {
                            effect(() => {
                                update(data().random_seed);
                            });
                        },
                        readOnly: true,
                        action: Button({
                            icon: 'copy',
                            onClick: () => navigator.clipboard.writeText(data().random_seed),
                        }),
                    }),
                ),
            );
        },
);
