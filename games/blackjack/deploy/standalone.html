<!doctype html>
<html lang="en">

<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta name="robots" content="noindex" />
    <title>Monkey Tilt Blackjack</title>
    <style>
        html,
        body {
            margin: 0;
            height: 100%;
            background: #09090b;
        }
    </style>
    <script src="/og.js"></script>
</head>

<body>
    <div id="app"></div>
    <script>
        const gameSessionId = new URL(location.href).searchParams.get('game_session') ?? Math.floor(Math.random() * 1000);
        console.log('Game session:', gameSessionId);
        MT_Originals.run({
            container: '#app',
            gameId: 'blackjack-001',
            gatewayUrl: 'https://gateway.og-dev.monkeytilt.codes/v1',
            gameSessionId,
        }).then((context) => {
            console.log('Game context:', context)
        });
    </script>
</body>

</html>