import {
    create,
    defineComponent,
    html,
    type Component,
    type Props,
    type Renderable,
} from '../../component';
import type { Reactive } from '../../state';
import { mergeClasses, namespaced } from '../../utilities/classes';

export interface TableProps extends Props<HTMLTableElement> {
    rows: Reactive<{ label: Renderable; value: Renderable }[]>;
}

export const Table = defineComponent(
    'Table',
    ({ state, render }) =>
        ({ rows, className, ...props }: TableProps): Component<HTMLTableElement> => {
            const { onPropChange } = state();

            const tableBody = create('tbody', {
                props: {
                    className: namespaced('m-table__body'),
                },
            });

            const tr = html('tr');
            const th = html('th');
            const td = html('td');

            onPropChange(rows, (rows) => {
                tableBody.innerHTML = '';

                for (const { label, value } of rows) {
                    const row = render(
                        tr({
                            className: namespaced('m-table__row'),
                        }),
                    );

                    const tableHeader = render(
                        th({
                            className: mergeClasses(
                                namespaced('m-table__header'),
                                namespaced('m-table__label'),
                            ),
                            textContent: String(label),
                            scope: 'row',
                        }),
                    );

                    const tableData = render(
                        td({
                            className: mergeClasses(
                                namespaced('m-table__data'),
                                namespaced('m-table__value'),
                            ),
                            textContent: String(value),
                        }),
                    );

                    row.appendChild(tableHeader);
                    row.appendChild(tableData);
                    tableBody.appendChild(row);
                }
            });

            const tableContainer = create('table', {
                props: {
                    ...props,
                    className: mergeClasses(className, namespaced('m-table')),
                },
            });

            tableContainer.appendChild(tableBody);

            return tableContainer as unknown as Component<HTMLTableElement>;
        },
);
