import { nearlyEqual, PercentageInput, NumberInput } from '@monkey-tilt/client';
import {
    <PERSON><PERSON>,
    <PERSON><PERSON>,
    defineComponent,
    Die,
    Flex,
    forwardRef,
    Frame,
    Label,
    Slider,
    Stack,
    Toast,
    withNamespace,
    type BadgeTickerItem,
    type Component,
    type Props,
    type SignalReader,
    type ToastBarControls,
} from '@monkey-tilt/ui';
import {
    diceRollLimits as calculateDiceRollLimits,
    calculateParams,
    DICE_ROLL_PRECISION,
    MULTIPLIER_PRECISION,
    WIN_CHANCE_PRECISION,
} from '../../../util/chance';
import { type DiceUI } from '../../dice';
import { Balance } from '../balance/balance';
import { Loader } from '../loader/loader';

export interface TableProps extends Props<HTMLDivElement> {
    /**
     * The Dice UI state manager.
     */
    readonly ui: DiceUI;
}

export const Table = defineComponent(
    'Table',
    ({ state }) =>
        ({ ui, ...props }: TableProps): Component<HTMLDivElement> => {
            const { signal, memo, effect, batch, untracked } = state();

            const [isLoading, setIsLoading] = signal(ui.gameState().isLoading);
            effect(() => {
                let timer: ReturnType<typeof setTimeout> | undefined;
                const newIsLoading = ui.gameState().isLoading;

                if (newIsLoading) {
                    timer = setTimeout(() => {
                        setIsLoading(true);
                    }, 1500);
                } else {
                    setIsLoading(false);
                }

                return () => {
                    if (timer) {
                        clearTimeout(timer);
                    }
                };
            });

            const history = memo(() => ui.gameState().history);
            const badges = memo(() =>
                history().map<BadgeTickerItem>((item) => ({
                    id: `${item.round_id}-${item.time.getTime()}`,
                    text: item.random_dice_roll,
                    type: item.outcome === 'win' ? 'success' : 'fail',
                })),
            );

            const RTP = memo(() => ui.gameState().rtp);

            const winChance = signal(
                calculateParams(
                    { multiplier: Number(ui.multiplier.read()) },
                    { rtp: RTP() },
                ).win_chance.toFixed(WIN_CHANCE_PRECISION),
            );

            const diceRollLimits = memo(() => {
                const { min, max } = calculateDiceRollLimits(RTP());
                return {
                    min,
                    max,
                };
            });

            const multiplierLimits = memo(() => {
                const rtp = RTP();
                const { min, max } = diceRollLimits();
                return {
                    min: calculateParams({ dice_roll: min }, { rtp, roll_over: true }).multiplier,
                    max: calculateParams({ dice_roll: max }, { rtp, roll_over: true }).multiplier,
                };
            });

            const winChanceLimits = diceRollLimits;

            const INITIAL_DICE_ROLL = calculateParams(
                {
                    multiplier: Number(ui.multiplier.read()),
                },
                { rtp: RTP(), roll_over: ui.rollOver.read() },
            ).dice_roll;

            const randomDiceRoll = memo(() => ui.gameState().random_dice_roll ?? INITIAL_DICE_ROLL);

            const diceRollValue = signal(Number(randomDiceRoll()));
            let endDiceRollValue = Number(diceRollValue.read());

            const diceRollTarget = signal<number | undefined>(undefined);

            let isInitializing = true;

            let previousRandomDiceRoll = Number(randomDiceRoll());

            effect(() => {
                endDiceRollValue = Number(randomDiceRoll());

                if (isInitializing) {
                    isInitializing = false;
                    return;
                }

                const target = untracked(diceRollTarget.read) ?? -Infinity;
                const targetValue = Number(untracked(ui.diceRoll.read));
                const rollOver = untracked(ui.rollOver.read);

                const { min } = untracked(diceRollLimits);

                if (ui.isTurboMode || nearlyEqual(endDiceRollValue, targetValue)) {
                    previousRandomDiceRoll = endDiceRollValue;
                    diceRollValue.update(endDiceRollValue);
                    diceRollTarget.update(endDiceRollValue < min ? undefined : targetValue);
                    if (
                        (rollOver && endDiceRollValue > target) ||
                        (!rollOver && endDiceRollValue < target)
                    ) {
                        void ui.playSound('win');
                    }
                    return;
                }

                const startingValue = previousRandomDiceRoll;
                let currentValue = startingValue;

                previousRandomDiceRoll = endDiceRollValue;

                void ui.playSound('increment');

                batch(() => {
                    diceRollTarget.update(undefined);
                    diceRollValue.update(currentValue);
                });

                const timerId = setInterval(() => {
                    const delta = Math.abs(endDiceRollValue - startingValue) / 15;

                    if (endDiceRollValue > currentValue) {
                        currentValue = Math.min(currentValue + delta, endDiceRollValue);
                    } else {
                        currentValue = Math.max(currentValue - delta, endDiceRollValue);
                    }

                    if (nearlyEqual(currentValue, endDiceRollValue)) {
                        clearInterval(timerId);
                        diceRollTarget.update(Number(ui.diceRoll.read()));
                        currentValue = endDiceRollValue;
                        if (
                            (rollOver && endDiceRollValue > target) ||
                            (!rollOver && endDiceRollValue < target)
                        ) {
                            void ui.playSound('win');
                        }
                    }

                    diceRollValue.update(currentValue);
                }, 30);

                return () => {
                    clearInterval(timerId);
                };
            });

            const clamp = (value: number, limits: SignalReader<{ min: number; max: number }>) => {
                const { min, max } = limits();
                return Math.min(Math.max(value, min), max);
            };

            let isUpdating = false;

            const updateMultiplier = (multiplier: number) => {
                if (isUpdating) {
                    return Promise.resolve();
                }
                isUpdating = true;
                return Promise.resolve()
                    .then(() =>
                        ui.multiplier.update(
                            clamp(multiplier, multiplierLimits).toFixed(MULTIPLIER_PRECISION),
                        ),
                    )
                    .finally(() => {
                        isUpdating = false;
                    });
            };

            const updateWinChance = (chance: number) => {
                if (isUpdating) {
                    return Promise.resolve();
                }
                isUpdating = true;
                return Promise.resolve()
                    .then(() =>
                        winChance.update(
                            clamp(chance, winChanceLimits).toFixed(WIN_CHANCE_PRECISION),
                        ),
                    )
                    .finally(() => {
                        isUpdating = false;
                    });
            };

            const updateDiceRoll = (diceRoll: number) => {
                if (isUpdating) {
                    return Promise.resolve();
                }
                isUpdating = true;
                return Promise.resolve()
                    .then(() =>
                        ui.diceRoll.update(
                            clamp(diceRoll, diceRollLimits).toFixed(DICE_ROLL_PRECISION),
                        ),
                    )
                    .finally(() => {
                        isUpdating = false;
                    });
            };

            effect(() => {
                const roll_over = ui.rollOver.read();
                const rtp = RTP();
                if (isUpdating) {
                    return;
                }
                const { multiplier, win_chance } = calculateParams(
                    { dice_roll: Number(untracked(ui.diceRoll.read)) },
                    { rtp, roll_over },
                );
                updateMultiplier(multiplier).then(() => {
                    updateWinChance(win_chance);
                });
            });

            effect(() => {
                const value = Number(ui.diceRoll.read());
                const rtp = RTP();
                if (isUpdating) {
                    return;
                }
                const { multiplier, win_chance } = calculateParams(
                    { dice_roll: value },
                    { rtp, roll_over: untracked(ui.rollOver.read) },
                );
                updateMultiplier(multiplier).then(() => {
                    updateWinChance(win_chance);
                });
            });

            effect(() => {
                const value = Number(ui.multiplier.read());
                const rtp = RTP();
                if (isUpdating) {
                    return;
                }
                const { win_chance, dice_roll } = calculateParams(
                    { multiplier: value },
                    { rtp, roll_over: untracked(ui.rollOver.read) },
                );
                updateWinChance(win_chance).then(() => {
                    updateDiceRoll(dice_roll);
                });
            });

            effect(() => {
                const value = Number(winChance.read());
                if (isUpdating) {
                    return;
                }
                const rtp = RTP();
                const { multiplier, dice_roll } = calculateParams(
                    { win_chance: value },
                    { rtp, roll_over: untracked(ui.rollOver.read) },
                );
                updateMultiplier(multiplier).then(() => {
                    updateDiceRoll(dice_roll);
                });
            });

            return Frame(
                {
                    ...props,
                    ref: forwardRef(props.ref, (element: HTMLDivElement) => {
                        effect(() => {
                            element.classList.toggle(withNamespace('is-loading'), isLoading());
                        });
                    }),
                    className: { 'm-dice__table': true },
                },
                Loader({
                    isLoading,
                    text: [
                        { text: 'Loading, please wait...', delay: 1000 },
                        { text: 'This is taking longer than expected...', delay: 5000 },
                        { text: 'Please check your internet connection!', delay: 8000 },
                    ],
                    ref: (element: HTMLDivElement) => {
                        effect(() => {
                            element.classList.toggle(withNamespace('u-hide'), !isLoading());
                        });
                    },
                }),
                Badge.Ticker({
                    delayMs: memo(() => (ui.config.turboMode.read() ? 50 : 500)),
                    items: badges,
                    limit: 12,
                }),
                Balance({ ui }),
                Stack(
                    { className: { 'm-dice__main': true }, gap: 'm' },
                    Die({
                        number: memo(() => diceRollValue.read().toFixed(DICE_ROLL_PRECISION)),
                        status: memo(() => {
                            const value = diceRollValue.read();
                            const target = diceRollTarget.read();
                            const rollOver = ui.rollOver.read();
                            return target === undefined
                                ? 'neutral'
                                : (rollOver && value > target) || (!rollOver && value < target)
                                  ? 'win'
                                  : 'loss';
                        }),
                        ref: (element: HTMLDivElement) => {
                            const diePositionVar = `--${withNamespace('die-position')}`;
                            effect(() => {
                                element.style.setProperty(
                                    diePositionVar,
                                    `${Number(diceRollValue.read())}%`,
                                );
                            });
                        },
                    }),
                    Slider({
                        value: memo(() => Number(ui.diceRoll.read())),
                        min: memo(() => diceRollLimits().min),
                        max: memo(() => diceRollLimits().max),
                        fractionalDigits: DICE_ROLL_PRECISION,
                        labelFrom: 0,
                        labelTo: 100,
                        rollOver: ui.rollOver.read,
                        disabled: ui.isBettingLocked,
                        onChange: (value: number) => {
                            void Promise.resolve().then(() => {
                                ui.diceRoll.update(value.toFixed(DICE_ROLL_PRECISION));
                            });
                        },
                    }),
                ),
                Flex(
                    { gap: '3xs-xs', className: { 'm-dice__table-inputs': true } },
                    Stack(
                        { gap: 's' },
                        Label({
                            htmlFor: 'dice-multiplier',
                            text: 'Multiplier',
                            variant: 'medium',
                        }),
                        NumberInput({
                            id: 'dice-multiplier',
                            variant: 'medium',
                            value: ui.multiplier,
                            fractionDigits: MULTIPLIER_PRECISION,
                            limits: multiplierLimits,
                            disabled: ui.isBettingLocked,
                            showStepper: 'horizontal',
                            step: 0.01,
                        }),
                    ),
                    Stack(
                        { gap: 's' },
                        Label({
                            htmlFor: 'dice-dice-roll',
                            text: memo(() => (ui.rollOver.read() ? 'Roll Over' : 'Roll Under')),
                            variant: 'medium',
                        }),
                        NumberInput({
                            id: 'dice-dice-roll',
                            className: { 'm-dice-roll-input': true },
                            variant: 'medium',
                            value: ui.diceRoll,
                            fractionDigits: DICE_ROLL_PRECISION,
                            limits: diceRollLimits,
                            disabled: ui.isBettingLocked,
                            showStepper: false,
                            step: 0.1,
                            suffix: Button({
                                icon: 'switch',
                                iconDescription: 'Toggle Roll Over/Under',
                                onClick: () => {
                                    ui.triggerAction('ToggleRollOverUnder');
                                },
                            }),
                        }),
                    ),
                    Stack(
                        { gap: 's' },
                        Label({
                            htmlFor: 'dice-chance',
                            text: 'Win Chance',
                            variant: 'medium',
                        }),
                        PercentageInput({
                            id: 'dice-chance',
                            variant: 'medium',
                            value: winChance,
                            fractionDigits: WIN_CHANCE_PRECISION,
                            limits: winChanceLimits,
                            disabled: ui.isBettingLocked,
                        }),
                    ),
                ),

                Toast.Bar({
                    position: 'absolute',
                    controls: (toastControls: ToastBarControls) => {
                        ui.toastBarControlsRef.current = toastControls;
                    },
                }),
                Toast.Bar({
                    position: 'absolute',
                    align: 'center',
                    css: {
                        bottom: '20%',
                    },
                    controls: (toastControls: ToastBarControls) => {
                        ui.notificationsControlsRef.current = toastControls;
                    },
                }),
            );
        },
);
