import browserslist from 'browserslist-to-esbuild';
import { resolve } from 'node:path';
import { defineConfig } from 'vite';
import { ViteImageOptimizer } from 'vite-plugin-image-optimizer';
import packageJson from './package.json' with { type: 'json' };

// @ts-ignore
import { scss } from '../../packages/ui/vite.config';

const pkg = packageJson as {
    name: string;
    dependencies: Record<string, string>;
    browserslist: string[];
};

const globals = Object.keys(pkg.dependencies ?? {})
    .filter((key) => key.startsWith('@monkey-tilt/'))
    .reduce(
        (acc, key) => {
            acc[key] = 'MT_' + key.split('/').pop()!.replace(/-/g, '_');
            return acc;
        },
        {} as Record<string, string>,
    );

const name = pkg.name.split('game-')[1]!;

export default defineConfig(() => ({
    server: {
        proxy: {
            '/-/og-dev': {
                target: process.env.OG_GATEWAY_URL || 'https://og-gateway.mt-dev.monkeytilt.pro',                rewrite: (path) => path.slice('/-/og-dev'.length),
                changeOrigin: true,
                autoRewrite: true,
                followRedirects: true,
                ws: true,
                rewriteWsOrigin: true,
            },
        },
    },
    build: {
        cssCodeSplit: true,
        target: browserslist(pkg.browserslist),
        rollupOptions: {
            external: Object.keys(globals),
            output: {
                globals,
                assetFileNames({ name: assetName }) {
                    if (assetName === 'main.css') {
                        return `${name}.css`;
                    }
                    return assetName!;
                },
            },
        },
        lib: {
            entry: resolve(__dirname, 'src/main.ts'),
            name: `MT_${toPascalCase(name)}`,
            fileName: (format) => `${name}.${format}.js`,
        },
        sourcemap: true,
        assetsInlineLimit: 20000,
    },
    experimental: {
        renderBuiltUrl: (filename, { type }) => (type === 'asset' ? './' + filename : undefined),
    },
    css: {
        devSourcemap: false,
        preprocessorOptions: {
            scss,
        },
    },
    plugins: [
        ViteImageOptimizer({
            logStats: false,
            svg: {
                multipass: true,
                plugins: [
                    {
                        name: 'preset-default',
                        params: {
                            overrides: {
                                cleanupNumericValues: false,
                                removeViewBox: false,
                                cleanupIds: false,
                                convertPathData: false,
                            },
                        },
                    },
                    'sortAttrs',
                    {
                        name: 'addAttributesToSVGElement',
                        params: {
                            attributes: [{ xmlns: 'http://www.w3.org/2000/svg' }],
                        },
                    },
                ],
            },
        }),
    ],
}));

function toPascalCase(str: string): string {
    return str
        .split('-')
        .map((word) => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
        .join('');
}
