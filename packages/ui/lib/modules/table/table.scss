@use '../../styles/common/bem';
@use '../../styles/common/font-weight';
@use '../../styles/common/prop';
@use '../../styles/common/respond';
@use '../../styles/common/type-style' as *;
@use '../../styles/common/util';
@use '../../styles/config/metrics';

$-block-size: 40px;
$-divider-color: prop.get(color-zinc-700);
$-inline-size: util.to-rem(552px);
$-padding-desktop: util.to-rem(8px) util.to-rem(12px);
$-padding-mobile: util.to-rem(6px) util.to-rem(10px);
$-radius: $-block-size * 0.25;

@mixin styles {
  @include bem.module(table) {
    max-inline-size: $-inline-size;
    
    display: flex;

    padding: $-padding-desktop;
    border-radius: $-radius;
    background-color: prop.get(color-zinc-800);
    color: prop.get(color-white);

    &__body {
        max-inline-size: $-inline-size;
        min-block-size: $-block-size;
        
        display: flex;
        flex: 1;
        flex-direction: row;
        align-items: center;
        justify-content: center;

        @include respond.until(s) {
            flex-direction: column;
        }
    }

    &__row {
      display: flex;
      flex: 1;
      flex-direction: column;
      align-items: flex-start;
      text-align: left;

      @include respond.until(s) {
        inline-size: 100%;
        text-align: center;
        align-items: center;
        padding: util.to-rem(8px) 0;
        &:not(:last-child) {
            border-bottom: 1px solid $-divider-color;
            padding: util.to-rem(12px) 0;
        }
      }

      @include respond.from(s) {
        &:not(:last-child) {
            border-right: 1px solid $-divider-color;
            padding-inline-end: util.to-rem(12px);
            margin-inline-end: util.to-rem(12px);
          }
      }
    }

    &__label {
      @include font-weight.set(normal);
      color: prop.get(color-zinc-400);
      margin-block-end: util.to-rem(4px);
    }

    &__value {
      @include type-style(
        body2
      );
    }

    @include respond.until(s) {
      padding: $-padding-mobile;
    }
  }
}
