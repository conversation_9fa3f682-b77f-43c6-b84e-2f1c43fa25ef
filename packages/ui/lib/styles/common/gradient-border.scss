@use './util';
@use './prop';

///
/// Utility mixin to simulate a gradient border via ::before pseudo-element.
///
/// @param {*} $border - The border width (defaults to 1px).
/// @param {Image} $gradient - The gradient image (defaults to linear-gradient(45deg, #000000, #ffffff)).
/// @param {String} $position - The position of the element (defaults to relative, must not be static).
/// @param {String} $prop-name - The name of CSS variable to control the gradient. Defaults to 'border-gradient'.
///
@mixin gradient-border($args...) {
    $args: util.keywords($args);
    $border: util.get-first($args, (1, 'border'), 1px);
    $gradient: util.get-first($args, (2, 'gradient'), linear-gradient(45deg, #000000, #ffffff));
    $position: util.get($args, 'position', relative);
    $prop-name: util.get($args, 'prop-name', 'border-gradient');

    position: $position;
    border: $border solid transparent;
    background-clip: padding-box;

    &::before {
        content: '';
        position: absolute;
        inset: 0;
        z-index: -1;
        margin: calc(-1 * #{$border});
        border-radius: inherit;
        background: prop.get($prop-name, $gradient);
    }
}
