import type { StorybookConfig } from '@storybook/html-vite';
import { config as libConfig } from '../lib/config';

const config: StorybookConfig = {
    addons: [
        '@storybook/addon-a11y',
        'storybook-addon-pseudo-states',
    ],
    features: {
        backgroundsStoryGlobals: true,
    },
    framework: '@storybook/html-vite',
    core: {
        disableTelemetry: true,
    },
    stories: ['../lib/**/*.stor{ies,y}.ts'],
    previewBody: (body) => `
        <script>document.body.classList.add('${libConfig.ns}s-app')</script>
        ${body}
    `,
};

export default config;
