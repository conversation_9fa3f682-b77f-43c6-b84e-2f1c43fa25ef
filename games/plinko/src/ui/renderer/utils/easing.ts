export type EasingFunction = (t: number) => number;

const EPSILON = 1e-6;

/**
 * Creates a cubic-bezier easing function.
 *
 * @param x1 — first control-point's X (must be in [0,1])
 * @param y1 — first control-point's Y
 * @param x2 — second control-point's X (must be in [0,1])
 * @param y2 — second control-point's Y
 */
export function cubicBezier(x1: number, y1: number, x2: number, y2: number): EasingFunction {
    const cx = 3 * x1;
    const bx = 3 * (x2 - x1) - cx;
    const ax = 1 - cx - bx;

    const cy = 3 * y1;
    const by = 3 * (y2 - y1) - cy;
    const ay = 1 - cy - by;

    const sample = (t: number): number => ((ax * t + bx) * t + cx) * t;

    function solve(x: number): number {
        let t = x;

        for (let i = 0; i < 8; i++) {
            const diff = sample(t) - x;
            const dx = (3 * ax * t + 2 * bx) * t + cx;
            if (Math.abs(diff) < EPSILON) {
                return t;
            }
            if (Math.abs(dx) < EPSILON) {
                break;
            }
            t -= diff / dx;
        }

        let t0 = 0,
            t1 = 1;

        t = x;
        while (t0 < t1) {
            const estimate = sample(t);
            if (Math.abs(estimate - x) < EPSILON) {
                return t;
            }
            if (estimate > x) {
                t1 = t;
            } else {
                t0 = t;
            }
            t = (t0 + t1) / 2;
        }
        return t;
    }

    return (t) => {
        if (t <= 0) {
            return 0;
        }
        if (t >= 1) {
            return 1;
        }

        const x = solve(t);
        return ((ay * x + by) * x + cy) * x;
    };
}

export const linear: EasingFunction = (t) => t;
export const ease: EasingFunction = cubicBezier(0.25, 0.1, 0.25, 1);
