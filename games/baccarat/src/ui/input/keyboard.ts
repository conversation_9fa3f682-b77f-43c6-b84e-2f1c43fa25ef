import { D<PERSON><PERSON><PERSON>boardController, KeyboardController, type KeyMapping } from '@monkey-tilt/ui';
import type { UIAction } from '../actions';

const increaseBet = (
    type: 'player' | 'tie' | 'banker',
): Omit<KeyMapping<UIAction, unknown>, 'key'> => ({
    action: 'IncreaseSingleBet',
    data: `${type}_bet_amount`,
    description: `Place a bet on ${type[0]!.toUpperCase()}${type.slice(1)}`,
});

export const keyboardShortcuts: KeyboardController<UIAction> = new DOMKeyboardController<UIAction>({
    keyMap: new Map([
        ['Space', { action: 'BetOrStop', description: 'Make a bet' }],
        ['W', { action: 'MultiplyAllBets', data: 2, description: 'Double bet amount' }],
        ['S', { action: 'MultiplyAllBets', data: 0.5, description: 'Halve bet amount' }],
        ['Q', { action: 'SetAllBets', data: 0, description: 'Zero bet amount' }],
        ['A', increaseBet('player')],
        ['E', increaseBet('tie')],
        ['D', increaseBet('banker')],
        ['Z', { action: 'UndoBet', description: 'Undo last bet change' }],
    ]),
});
