/* eslint-disable @typescript-eslint/no-unsafe-call */
/* eslint-disable @typescript-eslint/no-unsafe-return */
import { nearlyEqual, NumberInput, PercentageInput } from '@monkey-tilt/client';
import {
    Badge,
    defineComponent,
    Flex,
    forwardRef,
    Frame,
    Label,
    Multiplier,
    Stack,
    Toast,
    withNamespace,
    type BadgeTickerItem,
    type Component,
    type LabelControls,
    type Props,
    type RefObject,
    type ToastBarControls,
} from '@monkey-tilt/ui';
import { multiplierForWinChance, winChanceForMultiplier } from '../../../util/chance';
import { type CrashUI } from '../../crash';
import { Balance } from '../balance/balance';
import { Loader } from '../loader/loader';
import { Summary, type SummaryModalControls } from './summary';
import { Graph } from '../animation/graph';
import forestBackgroundSvgWithSun from '../../assets/mt-crash-desert-background.svg';
import monkeyHeadSvg from '../../assets/meymin.svg';
import monkeyHeadSvg2 from '../../assets/meymin-2.svg';
import monkeyHeadCrash from '../../assets/meymin-crash.svg';
import monkeyHeadWin from '../../assets/meymin-win.svg';



export interface TableProps extends Props<HTMLDivElement> {
    /**
     * The Crash UI state manager.
     */
    readonly ui: CrashUI;
}

export const Table = defineComponent(
    'Table',
    ({ state }) =>
        ({ ui, ...props }: TableProps): Component<HTMLDivElement> => {
            const { signal, memo, effect, batch, untracked } = state();

            const [isLoading, setIsLoading] = signal(ui.gameState().isLoading);
            effect(() => {
                let timer: ReturnType<typeof setTimeout> | undefined;
                const newIsLoading = ui.gameState().isLoading;

                if (newIsLoading) {
                    timer = setTimeout(() => {
                        setIsLoading(true);
                    }, 1500);
                } else {
                    setIsLoading(false);
                }

                return () => {
                    if (timer) {
                        clearTimeout(timer);
                    }
                };
            });

            const summaryModalRef: RefObject<SummaryModalControls> = { current: null };

            const history = memo(() => ui.gameState().history);
            const badges = memo(() =>
                history().map<BadgeTickerItem>((item) => ({
                    id: `${item.round_id}-${item.time.getTime()}`,
                    text: `${item.random_multiplier}x`,
                    type: item.outcome === 'win' ? 'success' : 'fail',
                    // onClick: () => {
                    //     if (summaryModalRef.current) {
                    //         summaryModalRef.current.open(item);
                    //     }
                    // },
                })),
            );

            const multiplierLimits = memo(() => ui.gameState().multiplier_limits);

            const randomMultiplier = memo(() => ui.gameState().random_multiplier ?? '1.00');

            const multiplierValue = signal(Number(randomMultiplier()));
            let endMultiplierValue = Number(multiplierValue.read());

            const multiplierTarget = signal<number | undefined>(undefined);

            let isInitializing = true;

            effect(() => {
                endMultiplierValue = Number(randomMultiplier());

                if (isInitializing) {
                    isInitializing = false;
                    return;
                }

                const target = untracked(multiplierTarget.read) ?? -Infinity;
                const targetValue = Number(untracked(ui.multiplier.read));

                if (ui.isTurboMode || nearlyEqual(endMultiplierValue, targetValue)) {
                    multiplierValue.update(endMultiplierValue);
                    multiplierTarget.update(
                        endMultiplierValue < untracked(multiplierLimits).min
                            ? undefined
                            : targetValue,
                    );
                    if (endMultiplierValue >= targetValue) {
                        void ui.playSound('win');
                    }
                    return;
                }

                const startingValue = 1.0;
                let currentValue = startingValue;

                void ui.playSound('increment');

                batch(() => {
                    multiplierTarget.update(undefined);
                    multiplierValue.update(currentValue);
                });

                const timerId = setInterval(() => {
                    const delta = Math.abs(endMultiplierValue - startingValue) / 15;

                    if (endMultiplierValue > currentValue) {
                        currentValue = Math.min(currentValue + delta, endMultiplierValue);
                    } else {
                        currentValue = Math.max(currentValue - delta, endMultiplierValue);
                    }

                    if (nearlyEqual(currentValue, endMultiplierValue)) {
                        clearInterval(timerId);
                        multiplierTarget.update(Number(ui.multiplier.read()));
                        currentValue = endMultiplierValue;
                        if (endMultiplierValue >= target) {
                            void ui.playSound('win');
                        }
                    }

                    multiplierValue.update(currentValue);
                }, 30);

                return () => {
                    clearInterval(timerId);
                };
            });

            const winChance = signal(
                winChanceForMultiplier(Number(ui.multiplier.read())).toFixed(8),
            );

            const winChanceLimits = memo(() => {
                const { min, max } = multiplierLimits();
                return {
                    min: winChanceForMultiplier(max),
                    max: winChanceForMultiplier(min),
                };
            });

            const clamp = (value: number, limits: { min: number; max: number }, precision = 2) =>
                Math.min(Math.max(value, limits.min), limits.max).toFixed(precision);

            let isUpdating = false;

            const updateMultiplier = (multiplier: number) => {
                if (isUpdating) {
                    return;
                }
                isUpdating = true;
                void Promise.resolve()
                    .then(() => ui.multiplier.update(clamp(multiplier, multiplierLimits())))
                    .finally(() => {
                        isUpdating = false;
                    });
            };

            const updateWinChance = (chance: number) => {
                isUpdating = true;
                void Promise.resolve()
                    .then(() => winChance.update(clamp(chance, winChanceLimits(), 8)))
                    .finally(() => {
                        isUpdating = false;
                    });
            };

            effect(() => {
                const value = Number(ui.multiplier.read());
                if (isUpdating) {
                    return;
                }
                updateWinChance(winChanceForMultiplier(value));
            });

            effect(() => {
                const value = Number(winChance.read());
                if (isUpdating) {
                    return;
                }
                updateMultiplier(multiplierForWinChance(value));
            });

            return Frame(
                {
                    ...props,
                    ref: forwardRef(props.ref, (element: HTMLDivElement) => {
                        effect(() => {
                            element.classList.toggle(withNamespace('is-loading'), isLoading());
                        });
                    }),
                    className: { 'm-crash__table': true },
                },
                // Loader({
                //     isLoading,
                //     text: [
                //         { text: 'Loading, please wait...', delay: 1000 },
                //         { text: 'This is taking longer than expected...', delay: 5000 },
                //         { text: 'Please check your internet connection!', delay: 8000 },
                //     ],
                //     ref: (element: HTMLDivElement) => {
                //         effect(() => {
                //             element.classList.toggle(withNamespace('u-hide'), !isLoading());
                //         });
                //     },
                // }),
                Summary({
                    ui,
                    controls: (summaryControls) => {
                        summaryModalRef.current = summaryControls;
                    },
                }),
                Badge.Ticker({
                    delayMs: memo(() => (ui.config.turboMode.read() ? 50 : 500)),
                    items: badges,
                    limit: 12,
                }),
                Graph({
                    value: () => {
                        const current = multiplierValue.read();
                        // Pass the actual multiplier value
                        return current;
                    },
                    ui: ui,
                    backgroundSvg: forestBackgroundSvgWithSun,
                    monkeyHeadSvg: monkeyHeadSvg,
                    monkeyHeadWinSvg: monkeyHeadWin,
                    monkeyHeadCrashSvg: monkeyHeadCrash,
                    monkeyHeadSvg2: monkeyHeadSvg2,
                    onGraphReady: (graph) => {
                        // Connect graph to betting system
                        ui.setGraphInstance(graph);
                    },
                }),
                Flex(
                    { gap: '3xs-xs', className: { 'm-crash__table-inputs': true } },
                    Stack(
                        { gap: 's' },
                        Label({
                            htmlFor: 'crash-multiplier',
                            text: 'Multiplier',
                            variant: 'medium',
                            controls: ({ setTooltip }: LabelControls) => {
                                effect(() => {
                                    setTooltip(
                                        `Maximum multiplier: ${multiplierLimits().max.toFixed(2)}x`,
                                    );
                                });
                            },
                        }),
                        NumberInput({
                            id: 'crash-multiplier',
                            variant: 'medium',
                            value: ui.multiplier,
                            limits: multiplierLimits,
                            disabled: ui.isBettingLocked,
                            showStepper: 'horizontal',
                            step: 0.01,
                            onBlur: (event) => {
                                if (event.target instanceof HTMLInputElement) {
                                    ui.updateInputs({ multiplier: event.target.value });
                                }
                            },
                        }),
                    ),
                    Stack(
                        { gap: 's' },
                        Label({
                            htmlFor: 'crash-chance',
                            text: 'Win Chance',
                            variant: 'medium',
                        }),
                        PercentageInput({
                            id: 'crash-chance',
                            variant: 'medium',
                            value: winChance,
                            fractionDigits: 8,
                            limits: winChanceLimits,
                            disabled: ui.isBettingLocked,
                            onBlur: (event) => {
                                if (event.target instanceof HTMLInputElement) {
                                    ui.updateInputs({ winChance: event.target.value });
                                }
                            },
                        }),
                    ),
                ),

                Toast({
                    position: 'absolute',
                    controls: (toastControls: ToastBarControls) => {
                        ui.toastBarControlsRef.current = toastControls;
                    },
                }),
                Toast({
                    position: 'absolute',
                    align: 'center',
                    css: {
                        bottom: '20%',
                    },
                    controls: (toastControls: ToastBarControls) => {
                        ui.notificationsControlsRef.current = toastControls;
                    },
                }),
            );
        },
);
