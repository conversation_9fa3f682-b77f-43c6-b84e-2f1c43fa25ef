<!doctype html>
<html lang="en">
    <head>
        <meta charset="UTF-8" />
        <meta name="viewport" content="width=device-width, initial-scale=1.0" />
        <meta name="robots" content="noindex" />
        <title>Monkey Tilt Keno</title>
        <style>
            html,
            body {
                margin: 0;
                height: 100%;
                background: #09090b;
            }
        </style>
        <script src="/og.js"></script>
    </head>

    <body>
        <div id="app"></div>
        <script>
            const gameSessionId =
                new URL(location.href).searchParams.get('game_session') ??
                'e938d0fd-eb17-4d9e-82f7-4400d394efdc';
            console.log('Game session:', gameSessionId);
            MT_Originals.run({
                container: '#app',
                gameId: 'keno-005',
                gatewayUrl: 'https://gateway.og-dev.monkeytilt.codes/v1',
                gameSessionId,
            }).then((context) => {
                console.log('Game context:', context);
            });
        </script>
    </body>
</html>
