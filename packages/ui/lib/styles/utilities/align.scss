@use 'sass:meta';
@use '../common/bem';
@use '../common/prop';
@use '../common/utility';

///
/// The align utility classes for controlling the inline and block alignment of elements.
/// These classes only control the --align and --align-block variables, not the alignment
/// properties themselves.
///
/// Usage:
///
/// <div class="l-cluster u-align--center">
///    <div>First child</div>
///    <div>Second child</div>
/// </div>
///
/// Configuration:
///     <none>
///
/// Block:
///     .u-align                The default value for --align variable (flex-start).
///     .u-align-block          The default value for --align-block variable (center).
///
/// Elements:
///     <none>
///
/// Modifiers:
///     .u-align--{X}           X is the inline alignment value:
///                             start, center, end, stretch, flex-start, flex-end,
///                             space-between, space-around, space-evenly
///
///     .u-align-block--{Y}     Y is the block alignment value:
///                             start, center, end, stretch, flex-start, flex-end,
///                             self-start, self-end, baseline
///
///

@mixin -inline-align-utility($align) {
    @include prop.set('align', $align, $important: true);
}

@mixin -block-align-utility($align) {
    @include prop.set('align-block', $align, $important: true);
}

@mixin styles {
    @include bem.utility('align') {
        @include -inline-align-utility(flex-start);

        &-block {
            @include -block-align-utility(center);
        }

        @include utility.modifiers(
            '&',
            (
                start,
                center,
                end,
                stretch,
                flex-start,
                flex-end,
                space-between,
                space-around,
                space-evenly
            ),
            meta.get-mixin('-inline-align-utility')
        );

        @include utility.modifiers(
            '&-block',
            (start, center, end, stretch, flex-start, flex-end, self-start, self-end, baseline),
            meta.get-mixin('-block-align-utility')
        );
    }
}
