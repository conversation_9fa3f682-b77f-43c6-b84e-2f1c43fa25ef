import { defineComponent, forwardRef, html, type Component, type Props } from '../../component';
import type { Reactive } from '../../state';
import { mergeClasses, withNamespace } from '../../utilities/classes';
import { Badge } from './badge';

export interface BadgeTickerItem {
    /**
     * The unique identifier for the item.
     */
    readonly id: string;
    /**
     * The item text to display.
     */
    readonly text: string;
    /**
     * The item type - can be success (green) or fail (red).
     */
    readonly type?: 'success' | 'fail';
    /**
     * Optional click handler for the item.
     */
    readonly onClick?: (e: MouseEvent) => void;
    /**
     * Custom color variant – overrides `type` if provided.
     */
    readonly color?: string;
    /**
     * If true, renders the badge in a pill shape.
     */
    readonly pill?: boolean;
}

export interface BadgeTickerProps extends Props<HTMLDivElement> {
    readonly items: Reactive<ReadonlyArray<BadgeTickerItem>>;
    readonly limit?: number;
    readonly delayMs?: Reactive<number>;
    readonly orientation?: 'horizontal' | 'vertical';
}

const div = html('div');

export const BadgeTicker = defineComponent(
    'BadgeTicker',
    ({ state, render, onUnmount }) =>
        ({
            items,
            limit = 5,
            delayMs = 0,
            orientation = 'horizontal',
            ...props
        }: BadgeTickerProps): Component<HTMLDivElement> => {
            const { onPropChange, read } = state();

            const itemClass = 'm-badge-ticker__item';
            const enterClass = withNamespace(`${itemClass}--enter`);
            const leaveClass = withNamespace(`${itemClass}--leave`);

            return div({
                ...props,
                className: mergeClasses(props.className, {
                    'm-badge-ticker': true,
                    [`m-badge-ticker--${orientation}`]: true,
                }),
                ref: forwardRef(props.ref, (element: HTMLDivElement) => {
                    let initialRender = true;
                    const knownItems = new Set<string>();
                    const visibleElements: HTMLDivElement[] = [];
                    const pendingElements: HTMLDivElement[] = [];

                    const renderItem = ({ id, ...props }: BadgeTickerItem) => {
                        knownItems.add(id);
                        return render(
                            div({ 'data-id': id, className: { [itemClass]: true } }, Badge(props)),
                        );
                    };

                    let timeout: ReturnType<typeof setTimeout> | undefined;

                    const dropFirst = () => {
                        const first = visibleElements.shift();
                        if (!first) return;

                        const onTransitionEnd = (e: TransitionEvent) => {
                            const shouldWaitFor =
                                orientation === 'vertical' ? 'max-height' : 'flex-basis';

                            if (e.propertyName !== shouldWaitFor) return;

                            knownItems.delete(first.dataset.id!);
                            first.remove();
                        };

                        first.addEventListener('transitionend', onTransitionEnd, { once: true });
                        first.classList.add(leaveClass);

                        first.offsetHeight;
                    };

                    const insertNext = () => {
                        if (timeout || pendingElements.length === 0) return;

                        timeout = setTimeout(() => {
                            timeout = undefined;

                            const next = pendingElements.shift()!;
                            next.classList.add(enterClass);
                            element.appendChild(next);

                            if (visibleElements.length >= limit) {
                                dropFirst();
                            }

                            visibleElements.push(next);

                            next.offsetWidth;
                            next.classList.remove(enterClass);

                            insertNext();
                        }, read(delayMs));
                    };

                    onPropChange(items, (newItems) => {
                        const newList = [...newItems];
                        if (initialRender) {
                            initialRender = false;
                            element.innerHTML = '';
                            const initial = newList.slice(-limit);
                            for (const item of initial) {
                                const el = renderItem(item);
                                element.appendChild(el);
                                visibleElements.push(el);
                            }
                            return;
                        }

                        for (const item of newList) {
                            if (!knownItems.has(item.id)) {
                                pendingElements.push(renderItem(item));
                            }
                        }

                        insertNext();
                    });

                    onUnmount(() => {
                        if (timeout) clearTimeout(timeout);
                    });
                }),
            });
        },
);