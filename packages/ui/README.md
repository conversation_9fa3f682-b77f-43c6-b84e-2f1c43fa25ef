# `@monkey-tilt/ui`

This package provides a CSS/UI library for building Monkey Tilt original game interfaces.

The library is CSS-first, meaning that it provides a set of CSS classes that can be used to style HTML elements in a consistent way. But, it also provides a set of JavaScript "components" that can be used to setup add interactivity to those elements conveniently.

A component is a simple wrapper around `document.createElement` and does not manage any state. It is meant to be used in conjunction with the `@monkey-tilt/state` package for managing application state.

## CSS Framework

The CSS framework is designed to streamline and enhance the process of creating consistent, modular, and scalable styles for websites and web applications. It follows a combination of methodologies like BEMIT (BEM + ITCSS), Every Layout principles, and fluid typography.

### Code Style

To maintain a consistent codebase, please adhere to the following coding standards:

-   Follow the BEM (Block, Element, Modifier) naming convention.
    -   we use `__` separator for elements and `--` separator for modifiers (there is a `common/bem.scss` file that provides some helper functions and mixins for generating BEM classes)
-   Use ITCSS (Inverted Triangle CSS) for structuring styles.
    -   we combine ITCSS with BEM to get BEMIT (BEM + ITCSS) naming convention, where classes are prefixed with the first letter of a "layer" name: `l-` for layouts, `m-` for modules, `u-` for utilities.
-   Write clear, concise, and well-documented code.
-   Keep CSS specificities low and avoid using `!important` except in utility classes (i.e. use `!important` proactively instead of reactively).
-   Ensure all styles are fluid/responsive and adapt correctly to various screen sizes.
-   Refer to [cssguidelin.es](https://cssguidelin.es/) for high-level advice and guidelines for writing sane, manageable, scalable CSS
-   Refer to [Every Layout Book](https://every-layout.dev) for detailed guidance on creating consistent, maintainable, and scalable layouts.
-   Refer to [Utopia](https://utopia.fyi) for guidance on creating and working with fluid typography and spacing systems.
-   Do _not_ use `@import` in SCSS, use `@use` instead.

    > The Sass team discourages the continued use of the `@import` rule. Sass will gradually phase it out over the next few years, and eventually remove it from the language entirely. Prefer the `@use` rule instead.
    >
    > _See [Sass documentation on `@import`](https://sass-lang.com/documentation/at-rules/import) for more information._


### BEMIT

BEMIT is a combination of BEM (Block, Element, Modifier) and ITCSS (Inverted Triangle CSS) methodologies. It is a way of organizing CSS code that helps to create reusable and maintainable styles.

The layers used in this library are renamed from the original ITCSS layers to better reflect their purpose:

 - **Common**: Sass functions, and mixins (a.k.a. the "tools" layer).
 - **Config**: Global variables, config switches, etc. This is the "settings" layer from ITCSS (N.B.: we have switched the order of the first two layers), all configuration happens at this layer, this layer is also the first one which outputs some styles (the css custom properties, a.k.a. run-time configuration).
 - **Defaults**: this is the "generic" layer from ITCSS - here we put all reset, normalizer code, the point of this layer is to create a common cross-browser style set we can build upon -- all selectors in this layer should ideally have 0 specificity (via `:where()` or `:is()` pseudo-classes).
 - **Elements**: this is the "base" layer from ITCSS - here we put all "bare" tag selectors (e.g. h1, p, a, etc.), this layer is for styling bare elements, it should not contain any classes.
 - **Layouts**: this is the "objects" layer from ITCSS - this is were we put all "layouts" (a.k.a CSS Objects), a layout is only concerned with how something is positioned on the screen (i.e. layouts are simply "box organizers"), not how it looks (i.e. they define the structure, not the look & feel -- although, some default look & feel may be defined, if required) -- all selectors in this layer must be class selectors with `.l-` prefix.
 - **Modules**: this is the "components" layer from ITCSS - the modules (components) are usually made up of one or more layouts with look & feel styling applied -- all selectors in this layer must be class selectors with `.m-` prefix.
 - **Utilities**: this is the "trumps" layer from ITCSS - these are utility classes, they are usually using `!important` but this is fine since this is proactive usage, which is what `!important` is meant for. These classes should ideally control a single property (e.g. `.u-hide` just controls the `display` property), utility classes can have responsive suffixes if needed (e.g. `.u-hide@m`) -- all selectors in this layer must be class selectors with `.u-` prefix.

### Namespace

Since the Monkey Tilt Original Games are meant to be integrated into various websites and web applications, it is important to namespace all classes to avoid conflicts with existing styles.

By default, the `-MT-` namespace is used for all classes and custom properties. The namespace can be customized at build time via the `MT_UI_CSS_NAMESPACE` environment variable.

Additionally, all "global" styles/resets and custom properties are scoped to the `<namespace>s-app` class (by default, that is `.-MT-s-app`) which should be added to the element wrapping the Monkey Tilt Original Game.

If you wish to use the Monkey Tilt UI library in a whole page, you can add the `.-MT-s-app` class to the `<body>` element. This can be done either manually or by setting the `insertScope` script option before loading the Monkey Tilt UI library javascript file:

```html
<script>
    window.__MT_ui_config__ = {
        insertScope: true
    };
</script>
<script src="path/to/ui.umd.cjs"></script>
```

### Web Fonts

The UI library uses Poppins and Rubik fonts from Google Fonts. The fonts are loaded automatically by the library, but this can be disabled by setting the `loadFonts` script option to `false`, in case these fonts are already loaded by the embedding page.


### Resources

To get familiar with the concepts the CSS framework is built upon, here are some recommended readings:

-   [Every Layout Book](https://every-layout.dev)
-   ITCSS: [Managing CSS Projects with ITCSS](https://www.youtube.com/watch?v=1OKZOV-iLj4&t=477s) ([slides](https://csswizardry.net/talks/2014/11/itcss-dafed.pdf))
-   [BEM Methodology](https://en.bem.info/methodology)
-   [CSS Coding Guidelines](https://cssguidelin.es)
-   Articles:
    -   most of all about CSS by Harry Roberts - https://csswizardry.com/archive/ e.g.:
        -   https://csswizardry.com/2012/06/single-direction-margin-declarations/
        -   https://csswizardry.com/2012/04/the-single-responsibility-principle-applied-to-css/
    -   most of the content from https://utopia.fyi/blog:
        -   https://utopia.fyi/blog/designing-with-fluid-type-scales/
        -   https://utopia.fyi/blog/css-modular-scales/
        -   the calculator for visualizing type and space scales - https://utopia.fyi/type/calculator
    -   the https://css-tricks.com is always a great resource, notable articles:
        -   https://css-tricks.com/a-complete-guide-to-custom-properties/
        -   https://css-tricks.com/snippets/css/a-guide-to-flexbox/
        -   https://css-tricks.com/snippets/css/complete-guide-grid/
        -   https://css-tricks.com/a-guide-to-the-responsive-images-syntax-in-html/