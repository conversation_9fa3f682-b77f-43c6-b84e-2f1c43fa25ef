import {
    create,
    defineComponent,
    forwardRef,
    html,
    type Props,
    type RefObject,
    type Renderable,
} from '../../component';
import type { Signal } from '../../state';
import { mergeClasses, withNamespace } from '../../utilities/classes';
import { Icon } from '../icon/icon';
import { Chip } from './chip';
import { ChipValues, type ChipValue } from './props';

export interface ChipSelectProps extends Props<HTMLDivElement> {
    /**
     * The currently selected chip.
     */
    readonly selectedChip: Signal<ChipValue | null>;

    /**
     * The available chip denominations.
     *
     * Defaults to `['1', '10', '100', '1K', '10K', '100K', '1M', '10M', '100M']`.
     */
    readonly denominations?: ReadonlyArray<
        ChipValue | { readonly value: ChipValue; readonly text: string }
    >;
}

const isOverflowingStartClassName = withNamespace('is-overflowing-start');
const isOverflowingEndClassName = withNamespace('is-overflowing-end');

export const ChipSelect = defineComponent(
    'Chip.Select',
    ({ state, onUnmount }) =>
        ({ selectedChip, denominations = ChipValues, ...props }: ChipSelectProps): HTMLElement => {
            const { signal, effect } = state();

            const chips: Renderable[] = [];
            const options = new Map<
                ChipValue,
                { ref: RefObject<HTMLDivElement>; isSelected: Signal<boolean> }
            >();

            for (const item of denominations) {
                const { value, text = value } = typeof item === 'string' ? { value: item } : item;

                const ref: RefObject<HTMLDivElement> = { current: null };
                const isSelected = signal(value === selectedChip.read());

                options.set(value, {
                    ref,
                    isSelected,
                });

                chips.push(
                    Chip({
                        ref,
                        value,
                        text,
                        tabIndex: 0,
                        role: 'radio',
                        isSelected: isSelected.read,
                        ariaChecked: String(isSelected.read()),
                        onClick: () => selectedChip.update(value),
                    }),
                );
            }

            let currentSelected = selectedChip.read();

            effect(() => {
                const selected = selectedChip.read();

                if (selected === currentSelected) {
                    return;
                }

                if (currentSelected) {
                    const { isSelected, ref } = options.get(currentSelected)!;
                    isSelected.update(false);
                    if (ref.current) {
                        ref.current.ariaChecked = 'false';
                    }
                }

                currentSelected = selected;

                if (selected) {
                    const { isSelected, ref } = options.get(selected)!;
                    isSelected.update(true);
                    if (ref.current) {
                        ref.current.ariaChecked = 'true';
                        ref.current.scrollIntoView({
                            behavior: 'smooth',
                            inline: 'center',
                            block: 'nearest',
                        });
                    }
                }
            });

            const wrapperRef: RefObject<HTMLDivElement> = { current: null };
            const reelRef: RefObject<HTMLDivElement> = { current: null };

            const updateArrows = () => {
                if (!wrapperRef.current || !reelRef.current) {
                    return;
                }

                const { scrollWidth, scrollLeft, clientWidth } = reelRef.current;

                wrapperRef.current.classList.toggle(
                    isOverflowingStartClassName,
                    scrollWidth > clientWidth && scrollLeft > 0,
                );
                wrapperRef.current.classList.toggle(
                    isOverflowingEndClassName,
                    scrollWidth > clientWidth && scrollLeft < scrollWidth - clientWidth,
                );
            };

            return create(
                'div',
                {
                    props: {
                        role: 'radiogroup',
                        ariaLabel: 'Select a chip',
                        ...props,
                        ref: forwardRef(props.ref, (element: HTMLDivElement) => {
                            wrapperRef.current = element;

                            const observer = new ResizeObserver(updateArrows);
                            observer.observe(element);
                            onUnmount(() => observer.disconnect());
                        }),
                        className: mergeClasses(props.className, {
                            'm-chip-select': true,
                        }),
                    },
                },
                html('div')(
                    {
                        className: { 'l-reel': true, 'm-chip-select__reel': true },
                        ref: (element: HTMLDivElement) => {
                            reelRef.current = element;
                            requestAnimationFrame(updateArrows);
                        },
                        onScroll: updateArrows,
                    },
                    ...chips,
                ),
                html('button')(
                    {
                        className: { 'm-chip-select__button': true },
                        onClick: (e) => {
                            e.preventDefault();
                            if (reelRef.current) {
                                reelRef.current.scrollBy({
                                    left: -reelRef.current.clientHeight * 2,
                                    behavior: 'smooth',
                                });
                            }
                        },
                    },
                    Icon({ name: 'chevron-l' }),
                ),
                html('button')(
                    {
                        className: { 'm-chip-select__button': true },
                        onClick: (e) => {
                            e.preventDefault();
                            if (reelRef.current) {
                                reelRef.current.scrollBy({
                                    left: reelRef.current.clientHeight * 2,
                                    behavior: 'smooth',
                                });
                            }
                        },
                    },
                    Icon({ name: 'chevron-r' }),
                ),
            );
        },
);
