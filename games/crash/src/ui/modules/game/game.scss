@use 'sass:math';
@use '@monkey-tilt/ui/scss/common/bem';
@use '@monkey-tilt/ui/scss/common/prop';
@use '@monkey-tilt/ui/scss/common/respond';
@use '@monkey-tilt/ui/scss/common/util';
@use '@monkey-tilt/ui/scss/config/metrics';

@mixin styles {
    @include bem.module('crash') {
        display: grid;
        grid-template-columns: metrics.fluid-size(308, 340) 1fr;
        grid-template-rows: 1fr util.to-rem(42px);
        gap: metrics.fluid-size(10, 12);
        padding: metrics.fluid-size(8, 0);
        background: prop.get(color-zinc-950);
        user-select: none;

        &-container {
            container: crash / inline-size;

            @include prop.set(measure, 100%);
        }

        &__sidebar {
            grid-column: 1;
            grid-row: 1 / span 2;
            max-block-size: 100vh;
            overflow-y: auto;

            @include bem.module('frame__section') {
                @include prop.set(frame-padding, util.to-rem(20px));
            }

            & > :last-child {
                @include prop.set(frame-padding, util.to-rem(12px));
            }
        }

        &__sidebar &__options,
        &__sidebar > &__bet {
            display: none;
        }

        @container crash (inline-size < #{respond.breakpoint-value('m')}) {
            grid-template-columns: 1fr;
            grid-template-rows: 50vh auto;

            &__tabs {
                order: 2;

                &#{bem.block-selector(module, frame__section)}::before {
                    display: block;
                }
            }

            &__sidebar {
                grid-row: 2;

                & > :last-child {
                    order: 3;
                }
            }

            &__sidebar &__options,
            &__sidebar > &__bet {
                display: flex;
            }

            &__options-bar {
                display: none;
            }
        }

        &__table {
            position: relative;
            display: grid;
            grid-template-columns: 1fr;
            grid-template-rows: 1fr auto;
            justify-content: center;
            align-items: center;
            gap: prop.get(size-m-xl);
            min-block-size: 100%;
            overflow: hidden;

            @include prop.set(
                frame-padding,
                prop.get(size-3xl) prop.get(size-m) prop.get(size-m) prop.get(size-m),
                $important: true
            );

            @include bem.module('badge-ticker') {
                position: absolute;
                inset: prop.get(frame-padding);
                inset-block-start: calc(prop.get(size-3xl) + prop.get(size-m));
                inset-block-end: auto;
            }

            @include bem.module('toast-bar') {
                z-index: 100;
            }

            @include bem.module('multiplier') {
                max-inline-size: fit-content;
                justify-self: center;

                @include respond.until(m) {
                    transform: translate(0, 20%) scale(0.85);
                }
            }

            &-inputs {
                inline-size: 100%;
                display: grid;
                gap: prop.get(size-l);
                grid-template-columns: 1fr 1fr;
                border-radius: metrics.fluid-size(12, 20);
                border: util.to-rem(1px) solid prop.get(color-zinc-800);
                background: prop.get(color-zinc-950);
                padding: metrics.fluid-size(8, 16);
                margin: 0 auto;

                @include respond.until(m) {
                    gap: prop.get(size-xs);

                    @include bem.module('input') {
                        @include prop.set(
                            (
                                input-scale: 0.8,
                                input-font-scale: 1,
                            )
                        );

                        input {
                            padding-inline: 0.2em;
                        }
                    }
                }
            }
        }

        &__table#{bem.namespaced-selector('is-loading')}
            > :not(#{bem.block-selector(module, loader)}) {
            opacity: 0;
        }

        &__bet-button {
            inline-size: 100%;
        }
    }
}
