import type { CardInfo } from '../game/state';

export function sumCards(cards?: CardInfo[]): string {
    if (!cards || !cards.length) {
        return '0';
    }

    let hasAce = false;

    const sum = cards.reduce((sum, card) => {
        if (card.index == 0) {
            hasAce = true;
        }
        return sum + card.value;
    }, 0);

    if (!hasAce || sum + 10 > 21) {
        return `${sum}`;
    }
    if (hasAce && sum + 10 == 21) {
        return '21';
    }

    return `${sum}/${sum + 10}`;
}
