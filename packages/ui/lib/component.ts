import {
    createStore,
    type EffectContext,
    type EffectOptions,
    type SignalOptions,
    type SignalReader,
    type Store,
} from '@monkey-tilt/state';
import { extendStore, type UIStateStore } from './state';
import { UnmountObserver } from './utilities';
import { abortOnAny } from './utilities/abort';
import { addEventListeners, type ListenersMap } from './utilities/event';
import { setProps, type HasRequiredProps, type HTMLProps } from './utilities/props';
import {
    applyClasses,
    mergeClasses,
    type ClassName,
    namespaced,
    withNamespace,
} from './classes.ts';
import { config } from '../config.ts';

type AnyElement = HTMLElement | SVGElement;
type AnyElementTagNameMap = HTMLElementTagNameMap &
    Pick<SVGElementTagNameMap, Exclude<keyof SVGElementTagNameMap, keyof HTMLElementTagNameMap>>;

type ElementByTagName<K extends keyof AnyElementTagNameMap> = K extends keyof HTMLElementTagNameMap
    ? HTMLElementTagNameMap[K]
    : K extends keyof SVGElementTagNameMap
      ? SVGElementTagNameMap[K]
      : never;

type RenderableItem<T> =
    | (T extends AnyElement ? Component<AnyElement> : never)
    | T
    | string
    | false
    | number
    | null
    | undefined;

/**
 * A thing that can be rendered to an HTMLElement instance.
 */
export type Renderable<T extends AnyElement | void = AnyElement> = T extends void
    ? void
    : RenderableItem<T> | ReadonlyArray<RenderableItem<T>>;

/**
 * A reference to some value, typically an HTMLElement instance.
 */
export interface RefObject<T = HTMLElement> {
    current: T | null;
}

export type Ref<T> = ((element: any) => void) | ((element: any) => () => void) | RefObject<T>;

/**
 * The base props type for a component.
 */
export type Props<T extends AnyElement = AnyElement, C extends AnyElement | void = AnyElement> = {
    readonly children?: ReadonlyArray<Renderable<C>>;
    readonly ref?: Ref<T> | undefined;
} & ListenersMap<T> &
    HTMLProps<T>;

/**
 * Represents a component instance with bound props and render methods.
 */
export interface Component<T extends AnyElement = AnyElement> {
    /**
     * The properties of the component instance.
     */
    readonly props: Props<T>;

    /**
     * Returns a clone of this component instance with the given properties merged
     * with the existing properties ("shallow" merge is done, i.e., props given to `with()`
     * will overwrite existing props with the same key).
     *
     * @param props - The properties to merge with the existing properties.
     */
    with(props: Props<T>): Component<T>;

    /**
     * Renders the component to an HTMLElement instance and appends it to the given parent element.
     *
     * @param parent -  The parent element to append the component to.
     *                  Either a selector string or an HTMLElement instance.
     * @param root - The Root to attach the component instance to. This must be provided if the
     *                  component is rendered asynchronously.
     * @returns The component's HTMLElement instance.
     */
    renderTo(parent: string | AnyElement, root?: Root): T;

    /**
     * Renders the component to an HTMLElement instance.
     *
     * @param root - The Root to attach the component instance to. This must be provided if the
     *                  component is rendered asynchronously.
     */
    render(root?: Root): T;
}

/**
 * A component that can be rendered to an HTMLElement instance.
 */
export interface ComponentFactory<
    Name extends string = string,
    T extends AnyElement = AnyElement,
    C extends AnyElement | void = AnyElement,
    P extends Props<T, C> = Props<T, C>,
> {
    /**
     * The type (i.e., name) of the component.
     */
    readonly type: Name;

    /**
     * Creates a renderable component instance.
     *
     * @param props - The properties to apply to the component.
     * @param children  - The children elements to append to the component.
     *                    If props.children is defined, this will be appended to the props.children array.
     */
    (props: MaybeOptionalProps<P>, ...children: Renderable<C>[]): Component<T>;
}

type ChildrenElements<T extends Props<any>> = T extends Props<any, infer U> ? U : void;

type ElementType<T> = T extends AnyElement | Text ? T : T extends Component<infer U> ? U : never;

type MaybeOptionalProps<T> = HasRequiredProps<T> extends true ? T : T | null | void;

interface ScopedState {
    state(this: void): UIStateStore;
    scopeStateTo<T extends AnyElement>(this: void, element: T): void;
}

export interface Root {
    readonly element: AnyElement;
    readonly store: Store;
    render(this: void, node: Renderable): void;
    append(this: void, node: Renderable): void;
    unmount(this: void): void;
    onUnmount(this: void, callback: () => void): void;
    onElementUnmount(this: void, element: AnyElement, callback: () => void): void;
}

interface StateRoot extends Root {
    createScopedState(this: void): ScopedState;
}

const rootStack: StateRoot[] = [];

export function createRoot(
    rootElement: AnyElement,
    unmount: () => void = () => rootElement.remove(),
): Root {
    const store = createStore();
    const observer = new UnmountObserver(rootElement);

    observer.signal.addEventListener('abort', () => void store.unlink(), { once: true });

    const root: StateRoot = {
        element: rootElement,
        store,
        createScopedState(this: void) {
            const controller = new AbortController();

            let element: AnyElement | null = null;
            let scopedStore: UIStateStore | null = null;

            return {
                state(this: void) {
                    if (!scopedStore) {
                        const { signal: abortSignal } = controller;

                        const withAbortSignal = <T extends EffectOptions>(
                            options: T | undefined,
                        ): T => {
                            if (!options) {
                                return { abortSignal } as T;
                            }
                            if (options.abortSignal) {
                                return {
                                    ...options,
                                    abortSignal: abortOnAny([options.abortSignal, abortSignal]),
                                } as T;
                            }
                            return { ...options, abortSignal } as T;
                        };

                        const effect = (
                            execute: (context: EffectContext) => void,
                            options?: EffectOptions,
                        ): (() => void) => store.effect(execute, withAbortSignal(options));

                        const memo = <T>(
                            compute: () => T,
                            options?: SignalOptions & EffectOptions,
                        ): SignalReader<T> => store.memo(compute, withAbortSignal(options));

                        scopedStore = extendStore({
                            ...store,
                            effect,
                            memo,
                        });

                        if (element) {
                            observer.observe(element, controller);
                        }
                    }

                    return scopedStore;
                },

                scopeStateTo<T extends AnyElement>(this: void, elementScope: T) {
                    if (element) {
                        if (element !== elementScope) {
                            throw new Error('Cannot reuse scoped store');
                        }
                        return;
                    }

                    element = elementScope;

                    if (scopedStore) {
                        observer.observe(element, controller);
                    }
                },
            };
        },
        render(this: void, node: Renderable): void {
            rootElement.innerHTML = '';
            root.append(node);
        },
        append(this: void, node: Renderable): void {
            const refNode = rootElement.firstElementChild;
            rootStack.push(root);
            const child = render(node, root);
            rootStack.pop();
            if (child) {
                if (refNode) {
                    refNode.after(child);
                } else {
                    rootElement.prepend(child);
                }
            }
        },
        unmount(this: void): void {
            unmount();
            observer.disconnect();
            void store.unlink();
        },
        onUnmount(this: void, callback: () => void): void {
            observer.signal.addEventListener('abort', callback, { once: true });
        },
        onElementUnmount(this: void, element: AnyElement, callback: () => void): void {
            const controller = new AbortController();
            controller.signal.addEventListener('abort', callback, { once: true });
            observer.observe(element, controller);
        },
    };

    return root;
}

let defaultRoot: StateRoot | null = null;
function getDefaultRoot(): StateRoot {
    if (import.meta.env.MODE === 'development') {
        if (!import.meta.env.STORYBOOK) {
            throw new Error('Rendering component without a Root');
        }
    }
    if (!defaultRoot) {
        defaultRoot = Object.assign(createRoot(document.body) as StateRoot, {
            render(node: Renderable): void {
                defaultRoot!.append(node);
            },
            append(node: Renderable): void {
                const child = render(node, defaultRoot!);
                if (child) {
                    document.body.appendChild(child);
                }
            },
            unmount(): void {
                document.body.innerHTML = '';
            },
        });
    }
    return defaultRoot;
}

/**
 * The context object passed to the render function provider.
 */
export interface DefineComponentContext {
    /**
     * The current render root.
     */
    readonly root: Root;

    /**
     * Renders the given component attaching it to the current root.
     *
     * Returns the created Element instance.
     *
     * This method should be used to render child components asynchronously and have
     * them be a part of the same state root.
     */
    readonly render: <T extends AnyElement>(this: void, component: Component<T>) => T;

    /**
     * Returns a derived state store scoped to the current component instance.
     */
    readonly state: (this: void) => UIStateStore;

    /**
     * Scopes the Store instance, returned by the state() method, to the given element.
     *
     * This is automatically called when the component returns an Element instance. Call this method
     * manually only if the component returns a null (i.e. the element is created asynchronously, or
     * is rendered directly to Root, like a popover).
     */
    readonly scopeStateTo: (this: void, element: AnyElement) => void;

    /**
     * Called when the component is unmounted (i.e. the Element instance is removed from the DOM tree).
     */
    readonly onUnmount: (this: void, callback: () => void) => void;
}

/**
 * Creates a Component out of the given render function.
 *
 * @param name - The name of the component.
 * @param renderProvider - The render function provider.
 * @returns The ElementRenderer instance.
 */
export function defineComponent<
    Name extends string,
    Renderer extends (props: any) => any,
    T extends AnyElement = ElementType<ReturnType<Renderer>>,
>(
    name: Name,
    renderProvider: (context: DefineComponentContext) => Renderer,
): ComponentFactory<Name, T, ChildrenElements<Parameters<Renderer>[0]>, Parameters<Renderer>[0]> {
    type C = ChildrenElements<Parameters<Renderer>[0]>;
    type P = Parameters<Renderer>[0];
    const createComponent: ComponentFactory<Name, T, C, P> = Object.assign(
        {
            [name]: (props: MaybeOptionalProps<P>, ...children: Renderable<C>[]) => {
                const givenProps = (props ?? {}) as P;
                const existingChildren: unknown = givenProps.children;
                const instanceProps: P = {
                    ...givenProps,
                    children: (existingChildren
                        ? (Array.isArray(existingChildren)
                              ? existingChildren
                              : [existingChildren]
                          ).concat(children)
                        : children) as Renderable<C>[],
                };
                return {
                    props: instanceProps,
                    with(newProps: P): Component<T> {
                        return createComponent({ ...instanceProps, ...newProps });
                    },
                    render(root: Root = rootStack.at(-1) ?? getDefaultRoot()): T {
                        const stateRoot = root as StateRoot; // N.B. This is safe for all Roots created by createRoot()

                        const { state, scopeStateTo } = stateRoot.createScopedState();

                        const { ref, ...restProps } = instanceProps;
                        let unmountCallback: (() => void) | null = null;

                        const element = render(
                            renderProvider({
                                root,
                                state,
                                scopeStateTo,
                                render: <T extends AnyElement>(component: Component<T>): T => {
                                    rootStack.push(stateRoot);
                                    const renderedElement = component.render(root);
                                    rootStack.pop();
                                    return renderedElement;
                                },
                                onUnmount: (callback: () => void) => {
                                    unmountCallback = callback;
                                },
                            })(restProps),
                            root,
                        ) as T;

                        if (element) {
                            scopeStateTo(element);
                            if (unmountCallback) {
                                stateRoot.onElementUnmount(element, unmountCallback);
                            }
                        }

                        if (ref) {
                            if (typeof ref === 'function') {
                                const onUnmount = (ref as (...args: any[]) => any)(element);
                                if (typeof onUnmount === 'function') {
                                    stateRoot.onElementUnmount(element, onUnmount);
                                }
                            } else {
                                (ref as RefObject<T>).current = element;
                            }
                        }

                        return element;
                    },
                    renderTo(parent: string | HTMLElement, root?: Root): T {
                        const parentElement =
                            typeof parent === 'string' ? document.querySelector(parent) : parent;

                        const instance = this.render(root);

                        if (parentElement) {
                            parentElement.appendChild(instance);
                        } else {
                            console.error(
                                `Failed to render ${name} component: parent element not found`,
                                parent,
                            );
                        }

                        return instance;
                    },
                };
            },
        }[name]!,
        { type: name },
    );

    return createComponent;
}

export function forwardRef<T extends AnyElement>(...refs: Array<Ref<T> | undefined>): Ref<T> {
    for (const ref of refs) {
        if (ref && typeof ref !== 'function') {
            ref.current = null;
        }
    }
    return (element: T) => {
        for (const ref of refs) {
            if (ref) {
                if (typeof ref === 'function') {
                    ref(element);
                } else {
                    ref.current = element;
                }
            }
        }
    };
}

/**
 * HTMLElement/SVGElement component factory cache.
 * @internal
 */
const componentCache = new Map<
    keyof AnyElementTagNameMap,
    ComponentFactory<keyof AnyElementTagNameMap, any, any, any>
>();

/**
 * Creates a new component factory for the given HTML element.
 *
 * Intended usage is to simplify the creation of components for basic HTML elements.
 *
 * @example
 * ```typescript
 * const ul = html('ul');
 * const li = html('li');
 * const component = ul({ children: [
 *      li({ children: ['Item 1'] }),
 *      li({ children: ['Item 2'] }),
 * ] });
 * ```
 *
 * @param element - The HTML tag name to create a component factory for.
 * @returns The component factory for the given HTML element.
 */
export function html<T extends keyof HTMLElementTagNameMap>(
    element: T,
): ComponentFactory<T, HTMLElementTagNameMap[T]> {
    if (!componentCache.has(element)) {
        componentCache.set(
            element,
            defineComponent(
                element,
                () => (props: Props<ElementByTagName<T>>) => create(element, { props }),
            ),
        );
    }

    return componentCache.get(element)! as ComponentFactory<T, HTMLElementTagNameMap[T]>;
}

/**
 * Creates a new component factory for the given SVG element.
 *
 * Intended usage is to simplify the creation of components for basic SVG elements.
 *
 * @example
 * ```typescript
 * const path = svg('path');
 * const component = svg('svg')({ children: [
 *      path({ d: 'M5 12h14m-7-7v14', stroke: 'currentColor' }),
 * ] });
 * ```
 *
 * @param element - The SVG tag name to create a component factory for.
 * @returns The component factory for the given SVG element.
 */
export function svg<T extends keyof SVGElementTagNameMap>(
    element: T,
): ComponentFactory<T, SVGElementTagNameMap[T]> {
    return html(element as string as keyof HTMLElementTagNameMap) as unknown as ComponentFactory<
        T,
        SVGElementTagNameMap[T]
    >;
}

/**
 * Options for creating a new HTMLElement instance.
 */
export interface CreateOptions<T extends AnyElement> {
    /**
     * The options to use when creating the element.
     */
    readonly options?: ElementCreationOptions;

    /**
     * The props to apply to the element.
     */
    readonly props?: Props<T>;
}

/**
 * Create a new HTMLElement instance with the given tag name.
 *
 * @param tagName - The tag name of the element to create.
 * @param options - The options to use when creating the element.
 * @returns The new HTMLElement instance.
 */
export function create<K extends keyof AnyElementTagNameMap>(
    tagName: K,
    { options, props }: CreateOptions<ElementByTagName<K>> = {},
    ...children: Renderable[]
): ElementByTagName<K> {
    type TElement = ElementByTagName<K>;

    const element = document.createElement(tagName, options) as TElement;

    if (props) {
        const { ref, children: propsChildren, className, ...rest } = props;

        if (className) {
            applyClasses(element, mergeClasses(className));
        }

        const restProps = Object.keys(rest) as Array<
            Exclude<keyof Props<TElement>, 'children' | 'ref' | 'className'> & string
        >;

        if (restProps.length > 0) {
            const listeners: ListenersMap<TElement> = {};
            const htmlProps: HTMLProps<TElement> = {};

            for (const key of restProps) {
                const value = rest[key];
                if (key.startsWith('on')) {
                    (listeners as Record<string, unknown>)[key] = value;
                } else {
                    (htmlProps as Record<string, unknown>)[key] = value;
                }
            }

            setProps(element, htmlProps);
            addEventListeners(element, listeners);
        }

        for (const child of [...(propsChildren ?? []), ...children]) {
            const childElement = render(child);
            if (childElement) {
                element.appendChild(childElement);
            }
        }

        if (ref) {
            if (typeof ref === 'function') {
                const onUnmount = ref(element);
                if (typeof onUnmount === 'function') {
                    void Promise.resolve().then(() => {
                        if (!element.parentElement) return;
                        const controller = new AbortController();
                        controller.signal.addEventListener('abort', onUnmount, { once: true });
                        new UnmountObserver(element.parentElement).observe(element, controller);
                    });
                }
            } else {
                ref.current = element;
            }
        }
    }

    return element;
}

/**
 * Renders the given renderable to a Node instance.
 *
 * @param renderable - The renderable to render.
 * @returns The rendered element, or null if the renderable should not be rendered.
 */
export function render<T extends Renderable>(renderable: T, root?: Root): Node | null {
    if (Array.isArray(renderable)) {
        if (renderable.length === 0) {
            return null;
        }
        const fragment = document.createDocumentFragment();
        for (const child of renderable) {
            const childElement = render(child, root);
            if (childElement) {
                fragment.appendChild(childElement);
            }
        }
        return fragment;
    }

    let value: RenderableItem<T> = renderable;
    if (typeof value === 'number') {
        if (Number.isNaN(value)) {
            return null;
        }
        value = String(value);
    }
    if (!value) {
        return null;
    }
    if (typeof value === 'string') {
        return document.createTextNode(value);
    }
    return value instanceof Element ? value : (value as Component).render(root);
}
