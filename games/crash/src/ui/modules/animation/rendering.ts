import * as <PERSON><PERSON><PERSON> from 'pixi.js';
import { 
    RENDERING_CONFIG, 
    CLOUD_CONFIG, 
    COLORS,
    MATH_CONFIG
} from './constants';
import { PositionData, ScreenPoint } from './positioning';

export class RenderingHelper {
    private lineGraphics: PIXI.Graphics;
    private cloudContainer: PIXI.Container;
    private cloudParticles: PIXI.Graphics[] = [];

    constructor(lineGraphics: PIXI.Graphics, cloudContainer: PIXI.Container) {
        this.lineGraphics = lineGraphics;
        this.cloudContainer = cloudContainer;
    }

    public plotLine(
        data: Array<PositionData>, 
        toScreenFunc: (point: PositionData) => ScreenPoint,
        hasCrashed: boolean
    ): void {
        this.lineGraphics.clear();
        if (data.length === 0 || hasCrashed) {
            return;
        }

        const screenPoints = data.map(toScreenFunc);

        if (screenPoints.length === 0) {
            return;
        }

        // Draw the main line
        this.lineGraphics.moveTo(screenPoints[0]!.x, screenPoints[0]!.y);
        for (let i = 1; i < screenPoints.length; i++) {
            this.lineGraphics.lineStyle(RENDERING_CONFIG.LINE_WIDTH, COLORS.WHITE);
            this.lineGraphics.lineTo(screenPoints[i]!.x, screenPoints[i]!.y);
        }

        // Draw enhanced strokes for a better look
        this.drawEnhancedStrokes(COLORS.WHITE);
    }

    public drawClouds(
        data: Array<PositionData>,
        toScreenFunc: (point: PositionData) => ScreenPoint,
        width: number,
        height: number,
        isRunning: boolean,
        hasCrashed: boolean
    ): void {
        if (!this.cloudContainer || data.length === 0 || !isRunning || hasCrashed) return;

        let particleIndex = 0;
        const particlesPerSegment = CLOUD_CONFIG.PARTICLES_PER_SEGMENT;

        const createOrUpdateParticle = (x: number, y: number, progress: number) => {
            // Skip if coordinates are invalid
            if (!Number.isFinite(x) || !Number.isFinite(y) || x < 0 || y < 0 || x > width || y > height) {
                return;
            }
            
            let particle: PIXI.Graphics;
            if (particleIndex < this.cloudParticles.length) {
                particle = this.cloudParticles[particleIndex]!;
            } else {
                particle = new PIXI.Graphics();
                this.cloudParticles.push(particle);
                this.cloudContainer.addChild(particle);
            }
            particleIndex++;
            
            particle.clear();

            const baseRadius = CLOUD_CONFIG.START_RADIUS_BASE + 
                              (CLOUD_CONFIG.END_RADIUS_BASE - CLOUD_CONFIG.START_RADIUS_BASE) * progress;
            const radius = Math.random() * CLOUD_CONFIG.RADIUS_RANDOM_RANGE + baseRadius;

            const baseAlpha = CLOUD_CONFIG.START_ALPHA_BASE + 
                             (CLOUD_CONFIG.END_ALPHA_BASE - CLOUD_CONFIG.START_ALPHA_BASE) * progress;
            const alpha = Math.random() * CLOUD_CONFIG.ALPHA_RANDOM_RANGE + baseAlpha;

            particle.circle(0, 0, radius);
            particle.fill({ color: COLORS.CLOUD, alpha });
            particle.x = x + (Math.random() - 0.5) * CLOUD_CONFIG.POSITION_RANDOM_RANGE;
            particle.y = y + (Math.random() - 0.5) * CLOUD_CONFIG.VERTICAL_RANDOM_RANGE;
        };

        if (data.length === 1) {
            const screenP = toScreenFunc(data[0]!);
            for (let j = 0; j < particlesPerSegment; j++) {
                createOrUpdateParticle(screenP.x, screenP.y, 0);
            }
        } else {
            for (let i = 0; i < data.length - 1; i++) {
                const p1 = data[i]!;
                const p2 = data[i + 1]!;

                const screenP1 = toScreenFunc(p1);
                const screenP2 = toScreenFunc(p2);

                const progress = i / (data.length - 2 || 1);

                for (let j = 0; j < particlesPerSegment; j++) {
                    const t = particlesPerSegment <= 1 ? 1 : j / (particlesPerSegment - 1);
                    const x = screenP1.x + (screenP2.x - screenP1.x) * t;
                    const y = screenP1.y + (screenP2.y - screenP1.y) * t;
                    createOrUpdateParticle(x, y, progress);
                }
            }
        }

        // Remove unused particles
        while (this.cloudParticles.length > particleIndex) {
            const particle = this.cloudParticles.pop();
            if (particle) {
                this.cloudContainer.removeChild(particle);
                particle.destroy();
            }
        }
    }

    public clearClouds(): void {
        this.cloudParticles.forEach(particle => {
            if (particle.parent) {
                particle.parent.removeChild(particle);
            }
            particle.destroy();
        });
        this.cloudParticles = [];
    }

    private drawEnhancedStrokes(lineColor: number): void {
        this.lineGraphics.stroke({
            color: lineColor,
            width: RENDERING_CONFIG.ENHANCED_STROKE_WIDTH,
            cap: 'butt',
            join: 'round',
            alignment: 0.5,
            alpha: RENDERING_CONFIG.ENHANCED_STROKE_ALPHA,
            miterLimit: 10,
        });
    }

    public dispose(): void {
        this.clearClouds();
    }
}

export class DataHelper {
    public static generateNextPoint(
        currentPoint: number,
        crashMultiplier: number,
        hasCrashed: boolean
    ): { point: PositionData; shouldCrash: boolean } {
        const x = currentPoint * MATH_CONFIG.TIME_STEP;
        const y = Math.pow(MATH_CONFIG.GROWTH_RATE, x);
        
        const shouldCrash = y >= crashMultiplier && !hasCrashed;
        const finalY = shouldCrash ? crashMultiplier : y;
        
        return {
            point: { x, y: finalY },
            shouldCrash
        };
    }

    public static trimDataArray(data: Array<PositionData>, maxPoints: number): void {
        if (data.length > maxPoints) {
            data.shift(); // Remove oldest point
        }
    }
} 