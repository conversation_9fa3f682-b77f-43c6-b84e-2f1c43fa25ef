@use '../common/bem';
@use '../common/prop';
@use '../common/respond';

///
/// The Grid Layout primitive creates a flexible layout that allows you to create a grid of items
/// that will automatically adjust to the available space. The minimum item inline size, after which
/// the number of grid columns will reduce is configurable via --min-item-size variable.
///
///
///                                  Grid Layout
///     /---------------------------------------------------------------------\
///     | +---------+ # +---------+ # +---------+ # +---------+ # +---------+ |
///     | |         | # |         | # |         | # |         | # |         | |
///     | |         | # |         | # |         | # |         | # |         | |  <- Children
///     | |         | # |         | # |         | # |         | # |         | |
///     | +---------+ # +---------+ # +---------+ # +---------+ # +---------+ |
///     | ################################################################### |  <- Gap
///     | +---------+ # +---------+ # +---------+ # +---------+ # +---------+ |
///     | |         | # |         | # |         | # |         | # |         | |
///     | |         | # |         | # |         | # |         | # |         | |
///     | |         | # |         | # |         | # |         | # |         | |
///     | +---------+ # +---------+ # +---------+ # +---------+ # +---------+ |
///     | ################################################################### |
///     | +---------+ # +---------+ # +---------+                             |
///     | |         | # |         | # |         |                             |
///     | |         | # |         | # |         |                             |
///     | |         | # |         | # |         |                             |
///     | +---------+ # +---------+ # +---------+                             |
///     \---------------------------------------------------------------------/
///                   ↑                    ↑
///                  Gap                Children
///
/// Configuration:
///     <none>
///
/// Variables:
///     --gap                   The size of padding between elements. Defaults to var(--size-s).
///     --grid-gap              The grid-specific gap that overrides the --gap. Unset by default.
///     --min-item-size         The minimum inline size of the grid item. Defaults to calc(var(--measure) / 3).
///     --max-item-size         The maximum inline size of the grid item. Defaults to 100%.
///
/// Usage:
///    <div class="l-grid u-gap--xs">
///      <div>First child</div>
///      <div>Second child</div>
///      <div>Third child</div>
///      <div>Fourth child</div>
///   </div>
///
/// Block:
///   .l-grid                  The grid layout container.
///
/// Elements:
///     <none>
///
/// Modifiers:
///     <none>
///

@mixin styles {
    @include bem.layout('grid') {
        display: grid;
        gap: prop.get(grid-gap, prop.get(gap, prop.get(size-s)));
        grid-template-columns: repeat(
            auto-fit,
            minmax(
                min(
                    prop.get(min-item-size, calc(prop.get(measure) / 3)),
                    prop.get(max-item-size, 100%)
                ),
                1fr
            )
        );
    }
}
