{"name": "@monkey-tilt/ui", "version": "0.1.0-dev", "description": "Monkey Tilt Original Games UI component library", "private": true, "type": "module", "license": "UNLICENSED", "author": "Monkey Tilt", "contributors": ["Alek<PERSON><PERSON> <<EMAIL>>"], "files": ["dist", "lib/styles/**/*.scss"], "module": "./dist/ui.es.js", "main": "./dist/ui.umd.js", "types": "./dist/index.d.ts", "exports": {"./style.scss": "./lib/styles/index.scss", "./scss/common/*": "./lib/styles/common/*.scss", "./scss/config/*": "./lib/styles/config/*.scss", ".": {"types": "./dist/index.d.ts", "import": "./dist/ui.es.js", "require": "./dist/ui.umd.js"}}, "scripts": {"dev": "storybook dev", "build": "vite build", "lint": "eslint ./lib", "typecheck": "tsc --noEmit"}, "dependencies": {"@floating-ui/dom": "1.6.12"}, "peerDependencies": {"@monkey-tilt/state": "workspace:*"}, "devDependencies": {"@eslint/js": "catalog:eslint", "@hail2u/css-mqpacker": "catalog:postcss", "@monkey-tilt/state": "workspace:*", "@monkey-tilt/utils": "workspace:*", "@storybook/addon-a11y": "catalog:storybook", "@storybook/addon-actions": "catalog:storybook", "@storybook/addon-backgrounds": "catalog:storybook", "@storybook/addon-controls": "catalog:storybook", "@storybook/addon-measure": "catalog:storybook", "@storybook/addon-viewport": "catalog:storybook", "@storybook/html-vite": "catalog:storybook", "@storybook/html": "catalog:storybook", "@storybook/test": "catalog:storybook", "@types/node": "catalog:typescript", "eslint": "catalog:eslint", "postcss-logical": "catalog:postcss", "sass-embedded": "catalog:sass", "sharp": "catalog:vite-images", "storybook-addon-pseudo-states": "catalog:storybook", "storybook": "catalog:storybook", "svgo": "catalog:vite-images", "typescript-eslint-language-service": "catalog:eslint", "typescript-eslint": "catalog:eslint", "typescript": "catalog:typescript", "vite-plugin-dts": "catalog:vite", "vite-plugin-image-optimizer": "catalog:vite-images", "vite": "catalog:vite"}, "packageManager": "pnpm@9.15.9+sha512.68046141893c66fad01c079231128e9afb89ef87e2691d69e4d40eee228988295fd4682181bae55b58418c3a253bde65a505ec7c5f9403ece5cc3cd37dcf2531"}