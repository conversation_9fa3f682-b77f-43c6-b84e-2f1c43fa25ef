import { CELL_SIZE, PEG_RADIUS } from '../constants';
import { StaticBody } from './body';

export class Peg extends StaticBody {
    #row: number;
    #column: number;

    public constructor(column: number, row: number, radius = PEG_RADIUS, cellSize = CELL_SIZE) {
        super((column + 0.5) * cellSize, (row + 0.5) * cellSize, radius);

        this.#row = row;
        this.#column = column;
    }

    public get row(): number {
        return this.#row;
    }

    public get column(): number {
        return this.#column;
    }
}
