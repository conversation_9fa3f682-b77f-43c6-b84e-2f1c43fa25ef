import {
    type ActionLike,
    type ReadyState,
    type StateLike,
    type Transport,
} from '@monkey-tilt/client';
import type { CardInfo } from './state';

export class MockTransport implements Transport {
    #handler?: (message: StateLike) => void;
    #num_player_hands = 1;
    #hand_index = 0;
    #hand_owner = 'Player';
    #hands: CardInfo[][] = [];
    readonly isReady = true;
    readonly readyState: ReadyState = 'ready';

    switchGameSession(_gameSessionId: string): Promise<void> {
        return Promise.resolve();
    }

    onMessage(handler: (message: StateLike) => void): void {
        this.#handler = handler;
    }

    onReadyStateChanged(handler: (readyState: ReadyState) => void): void {
        handler('ready');
    }

    canSend(_: ActionLike): boolean {
        return true;
    }

    #deal(): CardInfo {
        return this.#card(Math.floor(Math.random() * 52));
    }

    #card(deckIndex: number | number[]): CardInfo {
        if (Array.isArray(deckIndex)) {
            deckIndex = deckIndex[Math.floor(Math.random() * deckIndex.length)]!;
        }
        const index = deckIndex % 13;
        return {
            index,
            suit: Math.floor(deckIndex / 13),
            value: index < 9 ? index + 1 : 10,
        } as CardInfo;
    }

    #sum(cards: CardInfo[]): number[] {
        const sums: number[] = [0];
        let hasAce = false;
        for (const card of cards) {
            sums[0]! += card.value;
            if (card.value === 1) {
                hasAce = true;
            }
        }
        return hasAce ? [sums[0]!, sums[0]! + 10] : sums;
    }

    #respond(state: StateLike): void {
        setTimeout(
            () => {
                this.#handler?.(state);
            },
            50 + Math.random() * 200,
        );
    }

    send(message: ActionLike): boolean {
        if (message.action === 'auth') {
            this.#respond({
                type: 'Authenticate',
                action: 'auth',
                action_id: message.action_id,
                data: {
                    authenticated: true,
                },
            });
            return true;
        }

        const reply = ({ round_id, ...data }: Record<string, unknown>) => {
            this.#respond({
                type: 'GameUpdate',
                action: message.action,
                round_id: round_id as string,
                action_id: message.action_id,
                data,
            });
        };

        switch (message.action) {
            case 'Bet': {
                let dealer_card: CardInfo;
                const player_cards = [this.#deal(), this.#deal()];
                const next_actions = [];

                this.#hand_index = 0;
                this.#hand_owner = 'Player';
                this.#num_player_hands = 1;
                this.#hands = [player_cards];

                if (Math.random() > 0.8) {
                    dealer_card = this.#card([
                        9, 10, 11, 12, 13, 23, 24, 25, 26, 35, 37, 38, 39, 48, 49, 51,
                    ]);
                    next_actions.push('Peek');
                } else {
                    dealer_card = this.#deal();
                    if (!this.#sum(player_cards).includes(21)) {
                        next_actions.push('Hit', 'Stand', 'Surrender', 'Double');
                    }
                    if (player_cards[0]!.value === player_cards[1]!.value) {
                        next_actions.push('Split');
                    }
                }
                reply({
                    next_actions,
                    hand_owner: 'Player',
                    hand_index: 0,
                    dealer_hand: { cards: [dealer_card] },
                    player_hands: [
                        {
                            cards: player_cards,
                            bet_amount: message.data.bet_amount,
                        },
                    ],
                });
                break;
            }

            case 'Hit': {
                if (this.#hand_owner !== 'Player') {
                    if (Math.random() >= 0.3) {
                        this.#hand_index = 0;
                        this.#hand_owner = 'Player';
                    }
                    reply({
                        next_actions: ['Hit'],
                        dealer_hand: { cards: [this.#deal()] },
                        hand_index: this.#hand_index,
                        hand_owner: this.#hand_owner,
                    });
                } else {
                    const card = this.#deal();
                    this.#hands[this.#hand_index]!.push(card);
                    reply({
                        next_actions: ['Hit', 'Stand', 'Surrender', 'Double', 'Split'].filter(
                            () => Math.random() >= 0.5,
                        ),
                        player_hands: [{ cards: [card] }],
                    });
                }
                break;
            }

            case 'Split': {
                this.#num_player_hands++;

                const new_cards = [this.#deal(), this.#deal()] as const;

                this.#hands.splice(this.#hand_index, 0, [this.#hands[this.#hand_index]!.pop()!]);

                this.#hands[this.#hand_index]!.push(new_cards[0]);
                this.#hands[this.#hand_index + 1]!.push(new_cards[1]);

                reply({
                    next_actions: ['Hit', 'Stand', 'Surrender', 'Double'],
                    player_hands: [
                        {
                            cards: this.#hands[this.#hand_index]!,
                            bet_amount: message.data.bet_amount,
                        },
                        {
                            cards: this.#hands[this.#hand_index + 1]!,
                            bet_amount: message.data.bet_amount,
                        },
                    ],
                });
                break;
            }

            case 'Stand': {
                const hasNextHand =
                    this.#hand_owner === 'Player' && this.#hand_index < this.#num_player_hands - 1;

                this.#hand_index = hasNextHand ? this.#hand_index + 1 : 0;
                this.#hand_owner =
                    this.#hand_owner == 'Dealer' || hasNextHand ? 'Player' : 'Dealer';

                reply({
                    hand_index: this.#hand_index,
                    hand_owner: this.#hand_owner,
                    next_actions: ['Hit'],
                });

                break;
            }

            case 'Surrender': {
                reply({
                    next_actions: ['Open'],
                    total_payout: '-100.00',
                    round_closed: true,
                });
                break;
            }

            case 'Double': {
                const card = this.#deal();
                this.#hands[this.#hand_index]!.push(card);
                reply({
                    next_actions: ['Hit', 'Stand', 'Surrender', 'Double', 'Split'],
                    player_hands: [
                        {
                            cards: [card],
                            bet_amount: (Math.random() * 100 + 100).toFixed(2),
                        },
                    ],
                });
                break;
            }

            case 'Peek': {
                const status = Math.random() >= 0.5 ? 'insurance_won' : 'insurance_lost';
                reply({
                    next_actions: ['Hit', 'Stand', 'Surrender', 'Double', 'Split'],
                    insurance: { status, payout: status == 'insurance_won' ? 100 : -50 },
                });
                break;
            }
        }

        return true;
    }

    close(): void {
        // No-op
    }
}
