import { create, defineComponent, html, type Props } from '../../component';
import { mergeClasses, namespaced } from '../../utilities/classes';
import { Icon } from '../icon/icon';

export interface StepperProps extends Props<HTMLDivElement> {
    /**
     * Callback for incrementing the stepper value.
     */
    readonly onIncrement?: (e: MouseEvent) => void;

    /**
     * The description for the increment action. Used for accessibility.
     *
     * Defaults to `'Increment'`.
     */
    readonly incrementDescription?: string;

    /**
     * Ref callback to receive the increment button element instance.
     */
    readonly incrementRef?: (element: HTMLButtonElement) => void;

    /**
     * Callback for decrementing the stepper value.
     */
    readonly onDecrement?: (e: MouseEvent) => void;

    /**
     * The description for the decrement action. Used for accessibility.
     *
     * Defaults to `'Decrement'`.
     */
    readonly decrementDescription?: string;

    /**
     * Ref callback to receive the decrement button element instance.
     */
    readonly decrementRef?: (element: HTMLButtonElement) => void;

    /**
     * Whether the stepper should be displayed horizontally. Defaults to `false`.
     */
    readonly horizontal?: boolean;

    /**
     * Whether the stepper should be split into two separate buttons. Defaults to `false`, unless
     * `horizontal` is `true`.
     */
    readonly split?: boolean;
}

export const Stepper = defineComponent(
    'Stepper',
    () =>
        ({
            onIncrement,
            onDecrement,
            incrementDescription = 'Increment',
            decrementDescription = 'Decrement',
            incrementRef,
            decrementRef,
            horizontal = false,
            split = horizontal,
            ...props
        }: StepperProps): HTMLElement => {
            const increment = html('button')(
                {
                    className: namespaced('m-stepper__button', 'm-stepper__button--inc'),
                    ref: incrementRef,
                    onClick: onIncrement,
                    title: incrementDescription,
                },
                Icon({
                    name: horizontal ? 'arrow-right' : 'arrow-up',
                    description: incrementDescription,
                }),
            );

            const decrement = html('button')(
                {
                    className: namespaced('m-stepper__button', 'm-stepper__button--dec'),
                    ref: decrementRef,
                    onClick: onDecrement,
                    title: decrementDescription,
                },
                Icon({
                    name: horizontal ? 'arrow-left' : 'arrow-down',
                    description: decrementDescription,
                }),
            );

            return create(
                'div',
                {
                    props: {
                        ...props,
                        className: mergeClasses(props.className, {
                            'm-stepper': true,
                            'm-stepper--horizontal': horizontal,
                            'm-stepper--split': split,
                        }),
                    },
                },
                ...(horizontal ? [decrement, increment] : [increment, decrement]),
            );
        },
);
