import { <PERSON><PERSON><PERSON><PERSON>boardController, KeyboardController } from '@monkey-tilt/ui';
import type { UIAction } from '../actions';

export const keyboardShortcuts: KeyboardController<UIAction> = new DOMKeyboardController<UIAction>({
    keyMap: new Map([
        ['Space', { action: 'BetOrStop', description: 'Make a bet' }],
        ['W', { action: 'MultiplyBetAmount', data: 2, description: 'Double bet amount' }],
        ['S', { action: 'MultiplyBetAmount', data: 0.5, description: 'Halve bet amount' }],
        ['Q', { action: 'SetBetAmount', data: 0, description: 'Zero bet amount' }],
        ['A', { action: 'AutoPick', data: 0, description: 'Auto Pick' }],
        ['S', { action: 'Clear', data: 0, description: 'Clear the Board' }],
        ['Z', { action: 'PickRisk', data: 0, description: 'Select Classic risk' }],
        ['X', { action: 'PickRisk', data: 1, description: 'Select Low risk' }],
        ['C', { action: 'PickRisk', data: 2, description: 'Select Medium risk' }],
        ['V', { action: 'PickRisk', data: 3, description: 'Select High risk' }],
    ]),
});
