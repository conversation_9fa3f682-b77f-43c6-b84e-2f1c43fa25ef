import { format<PERSON>ur<PERSON>cy, BetAmount, Options } from '@monkey-tilt/client';
import {
    <PERSON>ton,
    Chip,
    defineComponent,
    Frame,
    html,
    Label,
    namespaced,
    Tabs,
    withNamespace,
    type Component,
    type LabelControls,
    type Props,
    type TabControls,
} from '@monkey-tilt/ui';
import {
    AllBetAmounts,
    BetAmountToBetLimit,
    SidebetAmounts,
    type BaccaratUI,
} from '../../baccarat';
import { chipText, chipValue, ChipValues } from '../../types/chips';
import { Autobet } from './autobet';

export interface SidebarProps extends Props<HTMLDivElement> {
    /**
     * The Baccarat UI state manager.
     */
    readonly ui: BaccaratUI;
}

export const Sidebar = defineComponent(
    'Sidebar',
    ({ state }) =>
        ({ ui, ...props }: SidebarProps): Component<HTMLDivElement> => {
            const { effect, memo } = state();

            const div = html('div');

            effect(() => {
                const state = ui.gameState();

                for (const bet of AllBetAmounts) {
                    ui.bets[bet].update(Number.parseFloat(state[bet] ?? '0'));
                }
            });

            const actionState = memo(() => {
                const {
                    next_actions = [],
                    round_closed = true,
                    readyState = 'closed',
                    isAuthenticated = false,
                    isLoading,
                    isAnimatingState,
                } = ui.gameState();
                return {
                    next_actions,
                    round_closed,
                    canPlay:
                        !isLoading &&
                        !isAnimatingState &&
                        readyState === 'ready' &&
                        isAuthenticated,
                };
            });

            const isAutobetActive = memo(() => ui.gameState().isAutobetActive);

            const bettingLocked = memo(() => {
                const { next_actions, round_closed, canPlay } = actionState();
                return !(
                    canPlay &&
                    (next_actions.includes('Bet') || (next_actions.length == 0 && round_closed))
                );
            });

            const totalBetAmountLimits = memo(() => {
                const { bet_limits } = ui.gameState();

                let min = bet_limits.bet.min;
                let max = bet_limits.bet.max;

                for (const bet of SidebetAmounts) {
                    const limit = bet_limits[BetAmountToBetLimit[bet]];
                    min = Math.min(min, limit.min);
                    max += limit.max;
                }

                return { min, max };
            });

            const betButton = Button({
                cta: true,
                label: memo(() => (isAutobetActive() ? 'Stop' : 'Bet')),
                icon: 'plus',
                className: { 'm-baccarat__bet-button': true },
                ref: (element: HTMLButtonElement) => {
                    effect(() => {
                        const isDisabled =
                            bettingLocked() && (!isAutobetActive() || ui.shouldCloseAutobet());
                        element.disabled = isDisabled;
                    });
                },
                onClick() {
                    if (ui.activeTab.read() === 'auto') {
                        ui.triggerAction(isAutobetActive() ? 'CloseAutobet' : 'Autobet');
                    } else {
                        ui.triggerAction('Bet');
                    }
                },
            });

            const hideOnMobile = withNamespace('u-hide@m');
            const showOnMobile = withNamespace('u-show@m');

            return Frame(
                { ...props, className: { 'm-baccarat__sidebar': true } },

                Frame.Section(
                    {
                        variant: 'subtle',
                        className: { 'm-baccarat__tabs': true },
                        ref: (element: HTMLDivElement) => {
                            effect(() => {
                                element.classList.toggle(hideOnMobile, bettingLocked());
                            });
                        },
                    },
                    Tabs({
                        ariaLabel: 'Bet type',
                        value: ui.activeTab.read(),
                        tabs: [
                            { label: 'Manual', value: 'manual' },
                            { label: 'Auto', value: 'auto' },
                        ],
                        onChange(value: 'manual' | 'auto') {
                            ui.activeTab.update(value);
                        },
                        controls({ selectTab, setEnabled }: TabControls) {
                            effect(() => {
                                selectTab(ui.activeTab.read());
                            });
                            effect(() => {
                                setEnabled(!bettingLocked());
                            });
                        },
                    }),
                ),

                Frame.Section({ variant: 'subtle', className: showOnMobile }, betButton),

                Frame.Section(
                    {
                        variant: 'subtle',
                        className: { 'u-gap--l': true, 'm-baccarat__controls': true },
                    },

                    div(
                        { className: { 'l-stack': true, 'l-stack--scroll': true } },
                        div(
                            { className: { 'l-stack': true, 'u-gap--s': true } },
                            Label(
                                null,
                                'Chip Value',
                                Label({
                                    variant: 'bubble',
                                    controls: ({ setText }: LabelControls) => {
                                        effect(() => {
                                            const chip = ui.selectedChip.read();
                                            const usdRate = ui.usdRate();
                                            const value = chip ? chipValue(chip) : 0;
                                            setText(
                                                formatCurrency(value, ui.currency(), {
                                                    factor: usdRate,
                                                    truncate: 'auto',
                                                }),
                                            );
                                        });
                                    },
                                }),
                            ),
                            Chip.Select({
                                selectedChip: ui.selectedChip,
                                denominations: ChipValues.map((value) => ({
                                    value,
                                    text: chipText(value),
                                })),
                            }),
                        ),

                        BetAmount({
                            label: 'Total Bet',
                            value: ui.totalBetAmount,
                            disabled: bettingLocked,
                            limits: totalBetAmountLimits,
                            readOnly: true,
                            step: 0,
                            currency: ui.currency,
                            onMultiply: (factor) => {
                                ui.triggerAction('MultiplyAllBets', factor);
                            },
                        }),

                        Autobet({
                            ui,
                            actionState,
                            bettingLocked,
                            ref(element: HTMLDivElement) {
                                const hide = withNamespace('u-hide');
                                effect(() => {
                                    element.classList.toggle(hide, ui.activeTab.read() !== 'auto');
                                });
                            },
                        }),
                    ),
                    div({ className: hideOnMobile }, betButton),
                ),

                Frame.Section(
                    {
                        className: {
                            'l-stack__split': true,
                            'l-stack': true,
                            'u-gap--s': true,
                        },
                    },
                    Options({ ui, namespace: 'baccarat' }),
                    html('button')(
                        {
                            onClick(e) {
                                e.preventDefault();
                                ui.provablyFairModalControlsRef.current?.open();
                            },
                            className: { 'u-text--center': true },
                            css: { 'border-width': '0' },
                        },
                        Label(
                            { variant: 'subtle', icon: 'double-check' },
                            html('span')({ className: namespaced('u-hide@m') }, 'This Game is'),
                            'Provably Fair',
                        ),
                    ),
                ),
            );
        },
);
