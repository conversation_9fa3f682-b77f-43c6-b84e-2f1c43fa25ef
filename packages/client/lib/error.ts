import type { ActionLike } from './messages';

export class GameError extends Error {
    readonly code: string;
    readonly action_id: string;
    readonly action: ActionLike | undefined;

    public constructor(message: string, code: string, action_id: string, action?: ActionLike) {
        super(message);
        this.code = code;
        this.action_id = action_id;
        this.action = action;
    }
}
