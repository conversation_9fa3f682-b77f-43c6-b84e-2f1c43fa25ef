<!-- 
    RUN:
        pnpm build:physics && pnpx serve .
    OPEN:
        http://localhost:3000/test
-->
<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Plinko</title>
</head>

<body>
    <style>
        html,
        body {
            margin: 0;
            width: 100%;
            height: 100%;
            background-color: darkslategray;
            color: #e4e4e7;
            display: flex;
            align-items: center;
            justify-content: center;
            font-family: monospace;
        }

        #row-select {
            position: absolute;
            top: 10px;
            left: 10px;
            display: flex;
            flex-direction: column;
            gap: 5px;
        }

        .m-board {
            --columns: calc(var(--rows) + 2);
            position: relative;
            width: 100vmin;
            display: grid;
            grid-template-columns: repeat(calc(var(--columns) * 2), 1fr);
            grid-template-rows: repeat(calc(var(--rows) + 1), 1fr);
            overflow: hidden;
        }

        .m-ball {
            grid-column: 1 / span 2;
            grid-row: 1;
            aspect-ratio: 1/1;
            position: relative;
            transform: translate(var(--x, 0), var(--y, 0));
        }

        .m-ball::before {
            content: "";
            position: absolute;
            width: 40%;
            aspect-ratio: 1/1;
            border-radius: 100%;
            left: 0;
            top: 0;
            transform: translate(-50%, -50%);
            background: radial-gradient(99.97% 99.97% at 49.98% 0.02%,
                    rgba(250, 204, 21, 0) 15.71%,
                    rgba(217, 119, 6, 0.49) 64.02%,
                    rgba(202, 138, 4, 0.55) 79.19%,
                    rgba(202, 138, 4, 0.45) 86.85%,
                    rgba(202, 138, 4, 0) 100%),
                radial-gradient(51.07% 51.07% at 49.98% 20.75%,
                    rgba(255, 255, 255, 0.22) 0%,
                    rgba(255, 255, 255, 0) 100%),
                radial-gradient(64.87% 64.87% at 49.98% 14.64%,
                    #ffeeab 0%,
                    #ff9043 100%);
            border: 1px solid rgba(255, 255, 255, 0.1);
            background-blend-mode: multiply, lighten, normal;
            box-shadow: 0px -0.936px 4.188px 0px #ffa0a0 inset,
                0px -3.51px 21.057px 0px rgba(221, 84, 84, 0.48) inset,
                0.936px -1.404px 7.487px 0px rgba(0, 0, 0, 0.25) inset;
            filter: drop-shadow(0px 2.34px 4.867px rgba(217, 108, 108, 0.32));
        }

        .m-bucket {
            grid-column: calc(var(--column) - 1) / span 2;
            grid-row: var(--row);
            aspect-ratio: 1/1;
            position: relative;
            cursor: pointer;
        }

        .m-bucket::before,
        .m-bucket::after {
            content: attr(data-bucket);
            position: absolute;
            left: 5%;
            right: 5%;
            top: 0;
            height: 50%;
            background: darkcyan;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .m-bucket::after {
            content: "";
            background-color: #e4e4e7;
            opacity: 0;
        }

        .m-bucket:hover::before {
            background: darkgoldenrod
        }

        .m-bucket:active::before {
            top: 2%;
        }

        .m-bucket.is-hit::after {
            animation: bucket-hit 0.3s forwards;
        }

        @keyframes bucket-hit {
            0% {
                transform: scale(1);
                opacity: 0.8;
            }

            100% {
                transform: scale(1.5);
                opacity: 0;
            }
        }

        .m-peg {
            grid-column: var(--column) / span 2;
            grid-row: var(--row);
            aspect-ratio: 1/1;
            position: relative;
        }

        .m-peg::before,
        .m-peg::after {
            content: "";
            position: absolute;
            width: 30%;
            aspect-ratio: 1/1;
            left: 50%;
            top: 50%;
            transform: translate(-50%, -50%);
            border-radius: 100%;
            background: #e4e4e7;
        }

        .m-peg::after {
            background: lightcyan;
            opacity: 0;
        }

        .m-peg.is-hit::after {
            animation: hit 0.3s forwards;
        }

        @keyframes hit {
            0% {
                transform: translate(-50%, -50%) scale(1);
                opacity: 1;
            }

            100% {
                transform: translate(-50%, -50%) scale(2);
                opacity: 0;
            }
        }
    </style>

    <div id="game"></div>
    <div id="row-select">
        <label for="rows">Rows:</label>
        <input type="range" id="rows" min="8" max="16" value="8" />
    </div>

    <script type="importmap">
        {
            "imports": {
                "src/": "./build/"
            }
        }
    </script>

    <script type="module">
        import { Board } from "src/physics/board.js";
        import { Ball } from "src/physics/bodies/ball.js";
        import { Peg } from "src/physics/bodies/peg.js";

        const boardElement = document.querySelector("#game");
        boardElement.classList.add("m-board");

        class PegElement extends Peg {
            constructor(column, row) {
                super(column, row);

                this.element = document.createElement("div");

                this.element.classList.add("m-peg");
                this.element.style.setProperty("--column", column * 2 + 1);
                this.element.style.setProperty("--row", row + 1);
                this.element.addEventListener('animationend', () => {
                    this.element.classList.remove('is-hit');
                });
                this.element.addEventListener('animationcancel', () => {
                    this.element.classList.remove('is-hit');
                });

                boardElement.appendChild(this.element);
            }
        }

        class BallElement extends Ball {
            #previousPosition;
            constructor(x, y) {
                super(x, y);

                this.#previousPosition = { x, y };

                this.element = document.createElement("div");
                this.element.classList.add("m-ball");
                this.element.onclick = () => {
                    console.log(this);
                };
                boardElement.appendChild(this.element);
            }

            update(dt) {
                this.#previousPosition = { x: this.x, y: this.y };
                super.update(dt);
            }

            interpolate(alpha) {
                return {
                    x: this.#previousPosition.x * (1 - alpha) + (this.x * alpha),
                    y: this.#previousPosition.y * (1 - alpha) + (this.y * alpha),
                };
            }
        }

        async function setRows(rows) {
            document.querySelector("#row-select > label").innerText = `Rows: ${rows}`;
            boardElement.innerHTML = "";

            const { default: [startFactors, ...startPositions] } = await import(`./src/data/${rows}.json`, {
                with: { type: "json" }
            });

            if (startFactors[0] !== rows || startPositions.length !== rows + 1) {
                throw new Error(`Invalid startPositions loaded for ${rows} rows`);
            }

            const nextStartPositions = new Array(startPositions.length);
            for (let i = 0; i < startPositions.length; i++) {
                shuffle(startPositions[i]);
                nextStartPositions[i] = -1;
            }

            const board = new Board({ rows, createPeg: (...args) => new PegElement(...args) });
            const bucketElements = new Array(board.columns - 1);
            const bucketHits = new Array(board.columns - 1);

            board.addEventListener('pegCollision', (event) => {
                const { peg } = event.detail;
                peg.element.classList.add('is-hit');
            });

            board.addEventListener('hit', (event) => {
                const { ball, bucketNumber } = event.detail;

                bucketElements[bucketNumber - 1].classList.add('is-hit');
                bucketHits[bucketNumber - 1]++;
                bucketElements[bucketNumber - 1].dataset.bucket = `${bucketNumber} (${bucketHits[bucketNumber - 1]})`;

                ball.element.remove();
            });

            boardElement.style.setProperty("--rows", board.rows);

            for (let i = 1; i < board.columns; i++) {
                const bucket = document.createElement("div");
                bucketHits[i - 1] = 0;
                bucket.dataset.bucket = `${i} (0)`;
                bucket.classList.add("m-bucket");
                bucket.style.setProperty("--column", i * 2 + 1);
                bucket.style.setProperty("--row", board.rows + 1);

                bucket.addEventListener('animationend', () => {
                    bucket.classList.remove('is-hit');
                });

                bucket.addEventListener('click', (e) => {

                    if (e.shiftKey) {
                        for (let j = 0; j < startPositions[i - 1].length; j++) {
                            board.addBall(new BallElement(...getStartPosition(i)));
                        }
                    } else {
                        board.addBall(new BallElement(...getStartPosition(i)));
                    }
                });

                bucketElements[i - 1] = bucket;
                boardElement.appendChild(bucket);
            }

            let previousTime = performance.now() / 1000;
            let accumulator = 0;
            const PHYSICS_DT = 1 / 30;

            let isTurbo = false;
            document.addEventListener('keydown', (e) => {
                if (e.key === 't') {
                    isTurbo = !isTurbo;
                }
            });

            function render() {
                const now = performance.now() / 1000;
                let dt = now - previousTime;
                previousTime = now;

                if (dt > 0.25) {
                    dt = 0.25;
                }

                accumulator += dt;

                while (accumulator >= PHYSICS_DT) {
                    board.update(PHYSICS_DT);
                    if (isTurbo) {
                        board.update(PHYSICS_DT);
                    }
                    accumulator -= PHYSICS_DT;
                }

                const alpha = accumulator / PHYSICS_DT;

                for (const ball of board.balls) {
                    const { x, y } = ball.interpolate(alpha);

                    ball.element.style.setProperty("--x", `${x / board.width * board.columns * 100}%`);
                    ball.element.style.setProperty("--y", `${y / board.height * board.rows * 100}%`);
                }

                requestAnimationFrame(render);
            }

            requestAnimationFrame(render);

            function getStartPosition(bucketNumber) {

                const index = bucketNumber - 1;

                nextStartPositions[index] = (nextStartPositions[index] + 1) % startPositions[index].length;
                const [i, j] = startPositions[index][nextStartPositions[index]];

                const { x, y, w, h } = board.startRect;
                const dx = w / (rows * startFactors[1]);
                const dy = h / ((rows / 2) * startFactors[2]);

                return [
                    i * dx + x,
                    j * dy + y,
                ]
            }
        }

        function shuffle(array) {
            for (let i = array.length - 1; i > 0; i--) {
                const j = Math.floor(Math.random() * (i + 1));
                [array[i], array[j]] = [array[j], array[i]];
            }
            return array;
        }

        const rowsInput = document.querySelector("#row-select > input");
        rowsInput.addEventListener('input', (e) => {
            setRows(Number(e.target.value));
        });
        setRows(Number(rowsInput.value));
    </script>
</body>

</html>