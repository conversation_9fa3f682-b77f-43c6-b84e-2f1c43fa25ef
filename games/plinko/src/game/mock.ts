import {
    type ActionLike,
    type BetPayload,
    type ReadyState,
    type StateLike,
    type Transport,
} from '@monkey-tilt/client';

export class MockTransport implements Transport {
    #handler?: (message: StateLike) => void;
    readonly isReady = true;
    readyState: ReadyState = 'closed';

    switchGameSession(_gameSessionId: string): Promise<void> {
        return Promise.resolve();
    }

    onMessage(handler: (message: StateLike) => void): void {
        this.#handler = handler;
    }

    onReadyStateChanged(handler: (readyState: ReadyState) => void): void {
        setTimeout(() => {
            this.readyState = 'ready';
            handler('ready');
        }, 100);
    }

    canSend(_: ActionLike): boolean {
        return true;
    }

    #respond(state: StateLike): void {
        setTimeout(
            () => {
                console.log('RECEIVE:', state);
                this.#handler?.(state);
            },
            100 + Math.random() * 100,
        );
    }

    #autobetCount = 0;
    #autobetLimit = 0;
    #autobetAmount = 0;
    #currentRoundId = '';

    send(message: ActionLike): boolean {
        console.log('SEND:', message);
        if (message.action === 'auth') {
            this.#respond({
                type: 'Authenticate',
                action: 'auth',
                action_id: message.action_id,
                data: {
                    authenticated: true,
                },
            });
            return true;
        }
        if (message.action === 'GameState') {
            this.#respond({
                type: 'Error',
                action: 'GameState',
                action_id: message.action_id,
                code: 'GAME_ROUND_NOT_FOUND',
            } as unknown as StateLike);
            return true;
        }

        const reply = ({
            round_id = Math.random().toString(36).slice(2),
            ...data
        }: Record<string, unknown>) => {
            this.#respond({
                type: 'GameUpdate',
                action: message.action,
                round_id: round_id as string,
                action_id: message.action_id,
                data,
            });

            this.#currentRoundId = round_id as string;
        };

        const payload = message.data as unknown as BetPayload;
        const response: Record<string, unknown> = {
            next_actions: [],
            bucket: 1 + Math.floor(Math.random() * ((payload.number_of_rows ?? 8) + 1)),
            total_payout:
                Math.random() > 0.2
                    ? (payload.bet_amount ?? this.#autobetAmount) * (Math.random() * 2)
                    : 0,
            round_closed: true,
        };

        switch (message.action) {
            // @ts-ignore
            case 'Autobet':
                this.#autobetLimit = message.data.autobet_limit as number;
                this.#autobetAmount = message.data.bet_amount as number;
                this.#autobetCount++;
                response.next_actions = ['Nextbet', 'CloseAutobet'];
            // @ts-ignore
            case 'Nextbet':
                if (this.#autobetLimit == 0 || this.#autobetCount < this.#autobetLimit) {
                    response.next_actions = ['Nextbet', 'CloseAutobet'];
                    this.#autobetCount++;
                } else {
                    response.round_closed = true;
                    this.#autobetCount = 0;
                }

            case 'Bet': {
                reply(response);
                break;
            }
            case 'CloseAutobet': {
                this.#autobetCount = 0;
                reply({
                    next_actions: [],
                    round_closed: true,
                    round_id: this.#currentRoundId,
                });
                break;
            }
        }

        return true;
    }

    close(): void {
        // No-op
    }
}
