/**
 * Merge two URLs, including the search params.
 *
 * If `relative` is absolute (or already an URL instance), only the search params
 * of the `base` URL are used.
 *
 * If `relative` is a relative path string (i.e., path-only which does not start
 * with `'/'`), it is resolved relative to the `base` URL.
 *
 * @param base - The base URL.
 * @param relative - The relative URL, or a relative path.
 * @returns The merged URL object.
 */
export function mergeURL(base: string | URL, relative: string | URL): URL {
    const baseUrl = base instanceof URL ? base : new URL(base.toString());
    const url = new URL(
        relative instanceof URL
            ? relative
            : /^([^:/]+:|\/)/.test(relative)
              ? relative
              : baseUrl.pathname.replace(/\/$/, '') + '/' + relative.replace(/^\.?\//, ''),
        baseUrl,
    );

    for (const [key, value] of baseUrl.searchParams.entries()) {
        if (!url.searchParams.has(key)) {
            url.searchParams.append(key, value);
        }
    }

    return url;
}

export interface WebSocketUrlInfo {
    /**
     * The game session ID.
     */
    readonly gameSessionId: string;
    /**
     * The base URL of the gateway.
     */
    readonly gatewayUrl: URL | string;
    /**
     * The WebSocket URL. If provided, this will be used instead of the gateway URL.
     */
    readonly websocketUrl?: URL | string | undefined;
    /**
     * The path to append to the gateway URL for the WebSocket connection.
     * Defaults to 'ws'. Ignored if websocketUrl is provided.
     */
    readonly websocketPath?: string | undefined;
    /**
     * The name of the query parameter to use for the game session ID.
     * Defaults to 'game_session'.
     */
    readonly gameSessionParamName?: string | undefined;
}

/**
 * Builds a WebSocket URL for the given game session.
 *
 * @param info - The information needed to build the WebSocket URL.
 * @returns The constructed WebSocket URL.
 */
export function buildWebSocketUrl({
    gatewayUrl,
    websocketUrl,
    gameSessionId,
    websocketPath = 'ws',
    gameSessionParamName = 'game_session',
}: WebSocketUrlInfo): URL {
    const baseUrl = new URL(gatewayUrl);
    const url = websocketUrl ? new URL(websocketUrl, baseUrl) : mergeURL(baseUrl, websocketPath);

    url.searchParams.set(gameSessionParamName, gameSessionId);

    return url;
}
