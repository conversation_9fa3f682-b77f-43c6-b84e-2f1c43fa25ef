import { logDevError, structuralEqual } from '@monkey-tilt/utils';

/**
 * The `SignalConstructor` options.
 */
export interface SignalOptions {
    /**
     * The custom equality function used to compare the previous and the next value.
     *
     * If the function returns `true` the signal will not trigger an update.
     *
     * Defaults to `Object.is`.
     */
    readonly equals?: (a: unknown, b: unknown) => boolean;
}

/**
 * The `EffectConstructor` options.
 */
export interface EffectOptions {
    /**
     * The optional abort signal to cancel the effect.
     */
    readonly abortSignal?: AbortSignal;
}

/**
 * A function that reads and returns a signal value.
 */
export type SignalReader<T> = () => T;

/**
 * A function that updates a signal value.
 */
export type SignalUpdater<T> = (value: T | ((prevValue: T) => T)) => void;

/**
 * A tuple of signal reader and signal updater functions.
 */
export type Signal<T> = [get: SignalReader<T>, set: SignalUpdater<T>] & {
    read: SignalReader<T>;
    update: SignalUpdater<T>;
};

/**
 * Creates a new signal.
 *
 * A signal is a reactive unit of state that can be read and updated.
 *
 * Returns a tuple with two functions:
 * - `get` - returns the current value of the signal.
 * - `set` - updates the value of the signal.
 *
 * @param initialValue The initial value of the signal.
 * @param options Optional parameters for customizing the behavior.
 * @returns A `[get, set]` tuple of accessor functions.
 */
export type SignalConstructor = <T>(initialValue: T, options?: SignalOptions) => Signal<T>;

/**
 * A function that reads the value of a signal without tracking it.
 */
export type UntrackedReader = <T>(read: SignalReader<T>) => T;

/**
 * The context object passed to the effect function.
 */
export interface EffectContext {
    /**
     * Cancels the effect.
     */
    cancel: (this: void) => void;
}

/**
 * Creates and executes a new effect.
 *
 * An effect is a function which will be automatically
 * re-executed whenever any of its dependencies change.
 *
 * Returns a cleanup function that should be called when the effect is no longer needed.
 *
 * @param execute The function to execute.
 * @param options Optional parameters for customizing the behavior.
 * @returns A cleanup function.
 */
export type EffectConstructor = (
    execute: (context: EffectContext) => void,
    options?: EffectOptions,
) => () => void;

/**
 * Creates a new computed (and read-only) signal.
 *
 * A memo is a special signal that is only re-computed
 * when any of its dependencies change.
 *
 * Returns a getter function that returns the current value of the computation.
 *
 * @param compute The function to compute the value.
 * @param options Optional parameters for customizing the behavior.
 * @returns A getter function.
 */
export type MemoConstructor = <T>(
    compute: () => T,
    options?: SignalOptions & EffectOptions,
) => SignalReader<T>;

/**
 * Executes a batch of updates.
 *
 * The batch function allows you to execute multiple updates while
 * ensuring that the signals are only updated once at the end of the batch.
 *
 * @param execute The function to execute.
 */
export type BatchFunction = (execute: () => void) => void;

declare const IsSnapshot: unique symbol;
declare type Brand<T extends symbol> = {
    readonly [K in T]: true;
};

/**
 * A snapshot of the store. An opaque type that should not be mutated.
 */
export interface StoreSnapshot extends Brand<typeof IsSnapshot> {}

/**
 * A store is a collection of related signals and effects.
 *
 * Signals, memos, effects and batch processes from different stores MUST NOT be mixed.
 */
export interface Store {
    /**
     * Creates a new signal.
     *
     * A signal is a reactive unit of state that can be read and updated.
     *
     * Returns a tuple with two functions:
     * - `get` - returns the current value of the signal.
     * - `set` - updates the value of the signal.
     *
     * @param initialValue The initial value of the signal.
     * @param options Optional parameters for customizing the behavior.
     * @returns A `[get, set]` tuple of accessor functions.
     */
    readonly signal: SignalConstructor;

    /**
     * Reads the value of a signal without tracking it.
     *
     * @param read The signal reader function.
     */
    readonly untracked: UntrackedReader;

    /**
     * Creates and executes a new effect.
     *
     * An effect is a function which will be automatically
     * re-executed whenever any of its dependencies change.
     *
     * Returns a cleanup function that should be called when the effect is no longer needed.
     *
     * @param execute The function to execute.
     * @returns A cleanup function.
     */
    readonly effect: EffectConstructor;

    /**
     * Creates a new computed (and read-only) signal.
     *
     * A memo is a special signal that is only re-computed
     * when any of its dependencies change.
     *
     * Returns a getter function that returns the current value of the computation.
     *
     * @param compute The function to compute the value.
     * @param options Optional parameters for customizing the behavior.
     * @returns A getter function.
     */
    readonly memo: MemoConstructor;

    /**
     * Executes a batch of updates.
     *
     * The batch function allows you to execute multiple updates while
     * ensuring that the signals are only updated once at the end of the batch.
     *
     * @param execute The function to execute.
     */
    readonly batch: BatchFunction;

    /**
     * Unlinks all effects in this Store.
     *
     * Use this when you want to "dispose" of a store.
     *
     * After this method is called, all effects and memos will become inert.
     * Reads are still allowed, but updates will not trigger any effects.
     *
     * Creating new signals/memos/effects after this method is called is not recommended.
     *
     * Treat this as a "destruct" method.
     */
    unlink(): Promise<void>;

    /**
     * Pauses the store updates.
     *
     * While a store is paused, any updates to signals will not trigger any effects.
     */
    pause(): void;

    /**
     * Resumes the store updates.
     *
     * When a store is resumed, any pending updates to signals will trigger dependant effects.
     */
    resume(): void;

    /**
     * Creates a snapshot of the store.
     */
    createSnapshot(): StoreSnapshot;

    /**
     * Restores the store from a snapshot.
     *
     * @param snapshot The snapshot to restore.
     */
    restoreSnapshot(snapshot: StoreSnapshot): void;
}

interface EffectInstance {
    readonly isMemo: boolean;
    readonly update: () => void;
    readonly onCleanup: (unlink: () => void) => void;
    readonly cancel: () => void;
}

const signalTuple = class Signal<T> extends Array<SignalReader<T> | SignalUpdater<T>> {
    public readonly read: SignalReader<T>;
    public readonly update: SignalUpdater<T>;

    constructor(read: SignalReader<T>, update: SignalUpdater<T>) {
        super(2);
        this.read = this[0] = read;
        this.update = this[1] = update;
    }
};

const __snapshot = Symbol('@@snapshot');

const store = class Store implements Store {
    #batchLevel = 0;
    #isUpdating = false;
    #isTracking = true;
    #isPaused = false;
    readonly #pendingEffects: Set<EffectInstance> = new Set();
    readonly #runningEffects: EffectInstance[] = [];
    readonly #activeEffects: Set<EffectInstance> = new Set();
    readonly #allSignals: Set<Signal<any>> = new Set();

    public signal = <T>(
        initialValue: T,
        { equals = structuralEqual }: SignalOptions = {},
    ): Signal<T> => {
        const dependencies = new Set<EffectInstance>();
        let value = initialValue;

        const read = (): T => {
            if (this.#isTracking) {
                const fx = this.#runningEffects.at(-1);
                if (fx && !dependencies.has(fx)) {
                    dependencies.add(fx);
                    fx.onCleanup(() => dependencies.delete(fx));
                }
            }
            return value;
        };

        const update = (newValue: T | ((prevValue: T) => T)): void => {
            newValue = newValue instanceof Function ? newValue(value) : newValue;

            if (equals(value, newValue)) {
                return;
            }

            value = newValue;

            for (const fx of dependencies) {
                this.#pendingEffects.add(fx);
            }

            this.#flush();
        };

        const signal = new signalTuple(read, update) as unknown as Signal<T>;

        this.#allSignals.add(signal);

        return signal;
    };

    public untracked = <T>(read: SignalReader<T>): T => {
        const wasTracking = this.#isTracking;

        this.#isTracking = false;

        const value = read();

        this.#isTracking = wasTracking;

        return value;
    };

    #flush(): void {
        if (this.#isUpdating) {
            return;
        }
        this.#isUpdating = true;

        const memos = Array.from(this.#pendingEffects).filter((fx) => fx.isMemo);

        for (const fx of memos) {
            fx.update();
            this.#pendingEffects.delete(fx);
        }

        this.#isUpdating = false;

        if (this.#batchLevel > 0 || this.#isPaused) {
            return;
        }

        const effects = Array.from(this.#pendingEffects);
        this.#pendingEffects.clear();

        for (const fx of effects) {
            fx.update();
        }
    }

    #createEffect(
        execute: (context: EffectContext) => void,
        { isMemo = false, abortSignal }: { isMemo?: boolean } & EffectOptions = {},
    ): () => void {
        if (abortSignal && abortSignal.aborted) {
            return () => {};
        }

        // eslint-disable-next-line prefer-const
        let fx: EffectInstance;

        const dependencies = new Set<() => void>();
        let onCleanup: (() => void) | void;

        const cancel = () => {
            for (const unlink of dependencies) {
                unlink();
            }

            dependencies.clear();
            this.#activeEffects.delete(fx);

            if (onCleanup) {
                try {
                    onCleanup();
                } catch (error) {
                    logDevError('Error during effect cleanup:', error);
                }
            }
        };

        if (abortSignal) {
            abortSignal.addEventListener('abort', cancel, { once: true });
        }

        const update = () => {
            cancel();

            if (this.#runningEffects.includes(fx)) {
                throw new Error('Cyclic dependency detected');
            }

            this.#runningEffects.push(fx);

            try {
                this.#activeEffects.add(fx);
                onCleanup = execute({ cancel });
            } catch (error) {
                cancel();
                throw error;
            } finally {
                this.#runningEffects.pop();
            }
        };

        fx = {
            isMemo,
            update,
            cancel,
            onCleanup(unlink) {
                dependencies.add(unlink);
            },
        };

        update();

        return cancel;
    }

    public effect = (
        execute: (context: EffectContext) => void,
        options?: EffectOptions,
    ): (() => void) => {
        return this.#createEffect(execute, options);
    };

    public memo = <T>(
        compute: () => T,
        options?: SignalOptions & EffectOptions,
    ): SignalReader<T> => {
        const [read, write] = this.signal<T>(undefined as T, options);

        this.#createEffect(() => write(compute()), { ...options, isMemo: true });

        return read;
    };

    public batch = (execute: () => void): void => {
        this.#batchLevel++;

        try {
            execute();
        } finally {
            if (--this.#batchLevel === 0) {
                this.#flush();
            }
        }
    };

    public unlink(): Promise<void> {
        return Promise.resolve().then(() => {
            this.#allSignals.clear();
            for (const fx of this.#activeEffects) {
                fx.cancel();
            }
            this.#activeEffects.clear();
        });
    }

    public pause(): void {
        this.#isPaused = true;
    }

    public resume(): void {
        if (this.#isPaused) {
            this.#isPaused = false;
            this.#flush();
        }
    }

    public createSnapshot(): StoreSnapshot {
        const data = new Map<Signal<any>, unknown>();

        for (const signal of this.#allSignals) {
            let value = signal.read();
            try {
                value = structuredClone(value);
            } catch {
                // ignore
            }
            data.set(signal, value);
        }

        return { [__snapshot]: data } as unknown as StoreSnapshot;
    }

    public restoreSnapshot(snapshot: StoreSnapshot): void {
        if (
            !snapshot ||
            typeof snapshot !== 'object' ||
            !(__snapshot in snapshot) ||
            !(snapshot[__snapshot] instanceof Map)
        ) {
            throw new Error('Invalid snapshot object provided');
        }

        this.batch(() => {
            for (const [signal, value] of snapshot[__snapshot] as Map<Signal<any>, unknown>) {
                signal.update(value);
            }
        });
    }
};

/**
 * Creates a new store.
 *
 * A store is a collection of related signals and effects.
 *
 * Signals, memos, effects and batch processes from different stores MUST NOT be mixed.
 *
 * @returns A new store.
 */
export function createStore(): Store {
    return new store();
}
