@use 'sass:meta';
@use '../common/bem';
@use '../common/utility';
@use '../common/type-style' as *;
@use '../config/typography';

@mixin -align-utility($align) {
    text-align: $align !important;
}

@mixin styles {
    @include bem.utility('text') {
        @each $type-style in typography.$type-style-names {
            @include bem.modifier($type-style, '&') {
                @include type-style($type-style, $important: true);
            }
        }

        @include utility.modifiers('&', (start, center, end), meta.get-mixin('-align-utility'));

        &--suffix {
            position: relative;
            max-inline-size: max-content;

            &::after {
                content: attr(data-suffix);
                position: absolute;
                inset: 50% 0 auto auto;
                transform: translate(100%, -50%) scale(1, 0.65);
            }
        }
    }

    @include bem.utility('nowrap') {
        white-space: nowrap !important;
        flex-wrap: nowrap !important;
    }
}
