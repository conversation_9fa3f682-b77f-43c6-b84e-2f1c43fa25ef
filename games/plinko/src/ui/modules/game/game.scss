@use 'sass:math';
@use '@monkey-tilt/ui/scss/common/bem';
@use '@monkey-tilt/ui/scss/common/prop';
@use '@monkey-tilt/ui/scss/common/respond';
@use '@monkey-tilt/ui/scss/common/util';
@use '@monkey-tilt/ui/scss/config/metrics';

@mixin styles {
    @include bem.module('plinko') {
        display: grid;
        grid-template-columns: metrics.fluid-size(308, 340) 1fr;
        grid-template-rows: 1fr util.to-rem(42px);
        gap: metrics.fluid-size(10, 12);
        padding: metrics.fluid-size(8, 0);
        background: prop.get(color-zinc-950);
        user-select: none;

        &-container {
            container: plinko / inline-size;

            @include prop.set(measure, 100%);
        }

        &__sidebar {
            grid-column: 1;
            grid-row: 1 / span 2;
            max-block-size: 100vh;
            overflow-y: auto;

            @include bem.module('frame__section') {
                @include prop.set(frame-padding, util.to-rem(20px));

                @include respond.until(m) {
                    @include prop.set(frame-padding, util.to-rem(8px));
                }
            }

            & > :last-child {
                @include prop.set(frame-padding, util.to-rem(12px));
            }

            @include respond.until('376px') {
                margin-top: util.to-rem(90px);
            }
        }

        &__sidebar &__options,
        &__sidebar > &__bet {
            display: none;
        }

        &__risk {
            @include respond.from(m) {
                margin-top: 5px;
            }
        }

        &__pick {
            button {
                padding: metrics.fluid-size(10, 15);
            }
        }

        @container plinko (inline-size < #{respond.breakpoint-value('m')}) {
            grid-template-columns: 1fr;
            grid-template-rows: 50vh auto;

            &__tabs {
                order: 2;

                &#{bem.block-selector(module, frame__section)}::before {
                    display: block;
                }
            }

            &__sidebar {
                grid-row: 2;

                & > :last-child {
                    order: 3;
                }
            }

            &__sidebar &__options,
            &__sidebar > &__bet {
                display: flex;
            }

            &__options-bar {
                display: none;
            }
        }

        &__table {
            position: relative;
            justify-content: center;
            gap: calc(3 * prop.get(size-m-xl));
            min-block-size: 100%;
            overflow: hidden;
            container-type: size;

            @include prop.set(
                frame-padding,
                prop.get(size-3xl) prop.get(size-m) prop.get(size-m) prop.get(size-m),
                $important: true
            );

            @include respond.until('376px') {
                min-block-size: util.to-rem(420px);
            }

            @include bem.module('toast-bar') {
                z-index: 100;
            }
        }

        &__table#{bem.namespaced-selector('is-loading')}
            > :not(#{bem.block-selector(module, loader)}) {
            opacity: 0;
        }

        &__main {
            padding: 0 prop.get(size-2xl);

            @include bem.module('tile-board') {
                margin-block-start: 2.5rem;
            }
        }

        &__bet-button {
            inline-size: 100%;
        }

        &__multipliers {
            gap: metrics.fluid-size(1, 4);
        }
        &__payout-section {
            min-block-size: 7rem;
            @include respond.until('m') {
                min-block-size: 5rem;
            }
        }

        &__ticker {
            position: absolute;
            top: metrics.fluid-size(15, 70);
            right: metrics.fluid-size(5, 10);

            @include respond.until('s') {
                position: relative;
                top: 0;
                right: 0;

                [class*='ticker'] {
                    justify-content: center;
                }
            }

            [class*='pill'] {
                @include respond.until('m') {
                    min-inline-size: util.to-rem(60px);
                    font-size: 0.6rem;
                    padding: 0.4rem;
                }
            }
        }
    }
}
