import type { <PERSON><PERSON>, StoryObj } from '@storybook/html';
import { fn } from '@storybook/test';
import { html } from '../../component';
import { namespaced } from '../../utilities/classes';
import { Frame } from '../frame/frame';
import iconNames from '../icon/icon-names';
import { Button, ButtonBar, type ButtonProps } from './button';

const meta: Meta = {
    title: 'Modules/Button',
};
export default meta;

export const Default: StoryObj = (props: ButtonProps) => {
    return Button(props).render();
};
Default.argTypes = {
    label: { control: 'text' },
    icon: { control: 'select', options: ['', ...iconNames] },
    variant: { control: 'select', options: ['primary', 'secondary', 'tertiary', 'accent', 'glass'] },
    cta: { control: 'boolean' },
    disabled: { control: 'boolean' },
};
Default.args = {
    label: 'Label',
    icon: '',
    onClick: fn(),
    disabled: false,
    cta: false,
};

export const Grid: StoryObj = () => {
    const grid: ButtonProps[][] = [
        [
            { label: 'Hit', icon: 'raise' },
            { label: 'Stand', icon: 'stop' },
        ],
        [
            { label: 'Split', icon: 'cards' },
            { label: 'Double', icon: 'coins' },
        ],
        [{ label: 'Surrender', icon: 'flag' }],
        [{ label: 'Bet', cta: true, icon: 'plus' }],
    ];

    return html('div')({
        children: [
            html('div')(
                { className: namespaced('l-stack', 'u-gap--xs') },
                ...grid.map((row) =>
                    html('div')({
                        children: row.map((props) => Button(props)),
                        className: namespaced('l-flex'),
                    }),
                ),
            ),
        ],
        className: namespaced('l-box', 'u-gap--xl'),
        css: {
            isolation: 'isolate',
            background: '#09090B',
            width: '40%',
        },
    }).render();
};
Grid.args = {};

export const buttonBar: StoryObj = () => {
    return Frame(
        { variant: 'small' },
        ButtonBar(
            null,
            Button({ variant: 'secondary', icon: 'sound', title: 'Sound effects' }),
            Button({ variant: 'secondary', icon: 'music', title: 'Music', isActive: true }),
            Button({
                variant: 'secondary',
                icon: 'notepad',
                title: 'Notes',
                href: '#',
                target: '_blank',
            }),
            Button({ variant: 'secondary', icon: 'clockwise', title: 'History' }),
            Button({ variant: 'secondary', icon: 'lock', title: 'Lock something' }),
            Button({
                variant: 'secondary',
                icon: 'circle-dollar',
                title: 'Account balance',
            }),
        ),
    ).render();
};
buttonBar.storyName = 'Button Bar';


export const RiskLevelButtons: StoryObj = {
    render: () => {
        return html('div')({
            children: [
                Button({ label: 'Classic', variant: 'risk--classic' }),
                Button({ label: 'Low', variant: 'risk--low' }),
                Button({ label: 'Medium', variant: 'risk--medium' }),
                Button({ label: 'High', variant: 'risk--high' }),
            ],
            className: namespaced('l-cluster', 'u-gap--s'),
        }).render();
    },
};
RiskLevelButtons.storyName = 'Risk Level Buttons';
