import {
    currencyToFixedString,
    GameUI,
    timer,
    type Intent,
    type MessageName,
    type RequestData,
    type VerifySeedParams,
    type Writable,
} from '@monkey-tilt/client';
import {
    type ModalControls,
    type RefObject,
    type Root,
    type Signal,
    type SignalReader,
    type ToastBarControls,
    type ToastControls,
    type ToastProps,
} from '@monkey-tilt/ui';
import { logDevError } from '@monkey-tilt/utils';
import { type PlinkoClient } from '../game/plinko';
import {
    createInitialPlinkoStats,
    BallData,
    PlinkoRisk,
    type AutobetState,
    type FinalState,
    type PlinkoState,
    type PlinkoStats,
} from '../game/state';
import type { UIAction } from './actions';
import { keyboardShortcuts } from './input/keyboard';

import effects from './assets/effects.json';
import effectsMP3 from './assets/effects.mp3';
import effectsOGG from './assets/effects.ogg';
import effectsWEBM from './assets/effects.webm';

export type SoundEffect = keyof typeof effects;

export const INITIAL_RISK = 0;
export const INITIAL_NUMBER_OF_ROWS = 8;

type Config = {
    readonly soundEffects: boolean;
    readonly hotkeys: boolean;
    readonly turboMode: boolean;
};

export interface PlinkoUIOptions {
    /**
     * The base URL used to load assets from.
     */
    readonly assetsUrl: URL;

    /**
     * The Plinko GameClient instance.
     */
    readonly client: PlinkoClient;

    /**
     * The state root.
     */
    readonly root: Root;
}

export interface BetIntent extends Intent<'Bet'> {
    readonly bet_amount: string;
}

export interface AutobetIntent extends Intent<'Autobet'> {
    readonly bet_amount: string;
}

declare module '@monkey-tilt/client' {
    interface IntentTypeMap {
        Bet: BetIntent;
        Autobet: AutobetIntent;
    }
}

export interface GameState extends PlinkoState {}

export class PlinkoUI extends GameUI<GameState, PlinkoClient, UIAction, Config, SoundEffect> {
    #toastBarControlsRef: RefObject<ToastBarControls> = { current: null };
    #notificationsControlsRef: RefObject<ToastBarControls> = { current: null };
    #hotkeysModalControlsRef: RefObject<ModalControls> = { current: null };
    #provablyFairModalControlsRef: RefObject<ModalControls> = { current: null };

    #pendingIntents: {
        CloseAutobet: Signal<boolean>;
    };

    #activeAction: Signal<MessageName | null>;

    public readonly activeTab: Signal<'manual' | 'auto'>;

    public readonly currency: SignalReader<string>;
    public readonly usdRate: SignalReader<number>;

    public readonly history: Signal<Array<BallData>>;
    public readonly betAmount: Signal<string>;

    public readonly risk: Signal<PlinkoRisk>;
    public readonly number_of_rows: Signal<number>;
    public readonly droppedBall: SignalReader<BallData | undefined>;

    public readonly isBoardIdle: Signal<boolean>;
    public readonly payout: Signal<number>;

    public readonly stats: Signal<PlinkoStats>;

    public readonly autobet: {
        [K in Exclude<keyof AutobetState, 'autobet_count'>]: Signal<
            AutobetState[K] extends boolean ? boolean : string
        >;
    };

    public readonly multipliers: SignalReader<[string, string][]>;
    public readonly maxBetAmount: SignalReader<number>;
    public readonly maxPayout: SignalReader<number>;
    public readonly defaultMaxBetAmount: SignalReader<number>;
    public readonly isBetAllowed: SignalReader<boolean>;
    public readonly isBettingLocked: SignalReader<boolean>;
    public readonly isBoardEditingAllowed: SignalReader<boolean>;

    #setBetAmount: (amount: number) => void;
    #multiplyBetAmount: (multiplier: number) => void;
    incrementBalance: (balance: number) => void;
    getMultiplier: (
        number_of_rows: number,
        risk: PlinkoRisk,
        bucket: number,
        rtp?: number,
    ) => string;

    public constructor({ client, assetsUrl, root }: PlinkoUIOptions) {
        const initialState = client.state();

        super({
            client,
            assetsUrl,
            root,
            keyboardShortcuts,
            config: {
                soundEffects: true,
                hotkeys: false,
                turboMode: window.matchMedia(`(prefers-reduced-motion: reduce)`).matches,
            },
            soundEffects: {
                sources: [
                    { src: new URL(effectsWEBM, assetsUrl).href, type: 'audio/webm; codecs=opus' },
                    { src: new URL(effectsOGG, assetsUrl).href, type: 'audio/ogg; codecs=vorbis' },
                    { src: new URL(effectsMP3, assetsUrl).href, type: 'audio/mpeg' },
                ],
                sprites: effects,
            },
            initialState: {
                ...initialState,
            },
            pendingUpdatesDelay: 200,
        });

        const { signal, effect, memo, untracked } = this.root.store;

        this.#pendingIntents = {
            CloseAutobet: signal(false),
        };

        this.client.on('intentAdded', ({ type }) => {
            if (type === 'CloseAutobet') {
                this.#pendingIntents.CloseAutobet.update(true);
            }
        });
        this.client.on('intentRemoved', ({ type }) => {
            if (type === 'CloseAutobet') {
                this.#pendingIntents.CloseAutobet.update(false);
            }
        });

        this.#activeAction = signal<MessageName | null>(null);
        this.history = signal<Array<BallData>>([]);

        this.activeTab = signal<'manual' | 'auto'>('manual');

        this.currency = memo(() => this.client.session().currency);
        this.usdRate = memo(() => this.client.session().usdRate);

        this.betAmount = signal('0.00');
        this.isBoardIdle = signal(true);
        this.payout = signal(0);
        this.stats = signal(createInitialPlinkoStats());

        effect(() => {
            if (this.isBoardIdle.read()) {
                this.client.updateBalance();
            }
        });

        effect(() => {
            const { bet_amount } = untracked(this.client.state);
            const currency = this.currency();

            if (bet_amount) {
                const value = Number.parseFloat(bet_amount);
                if (Number.isFinite(value)) {
                    this.betAmount.update(currencyToFixedString(value, currency));
                }
            }
        });

        const { number_of_rows, risk } = this.client.state();

        this.number_of_rows = signal(number_of_rows ?? 8);

        this.risk = signal(risk ?? INITIAL_RISK);

        effect(() => {
            const { risk } = this.client.state();
            if (risk) {
                this.risk.update(risk);
            }
        });

        effect(() => {
            const { number_of_rows } = this.client.state();
            if (number_of_rows) {
                this.number_of_rows.update(number_of_rows);
            }
        });

        this.droppedBall = memo(() => this.client.state().dropped_ball);

        this.autobet = {
            autobet_limit: signal('0'),
            on_win: signal('0'),
            on_win_reset: signal(true),
            on_loss: signal('0'),
            on_loss_reset: signal(true),
            stop_on_profit: signal('0.00'),
            stop_on_loss: signal('0.00'),
            autobet_cumulative_payout: signal('0.00'),
        };

        let previousCurrencyCode = this.currency();
        let previousUsdRate = this.usdRate();
        effect(() => {
            const currency = this.currency();
            if (currency !== previousCurrencyCode) {
                void Promise.resolve().then(() => {
                    const usdRate = this.usdRate();
                    const factor = (1 / previousUsdRate) * usdRate;

                    previousCurrencyCode = currency;
                    previousUsdRate = usdRate;

                    const update = (amount: string) =>
                        currencyToFixedString(Number(amount) * factor, currency);

                    this.client.setAutobetState({
                        bet_amount: update(this.betAmount.read()),
                        stop_on_profit: update(this.autobet.stop_on_profit.read()),
                        stop_on_loss: update(this.autobet.stop_on_loss.read()),
                    });
                });
            }
        });

        this.multipliers = memo(() => {
            const { rtp, multipliersChancesMap } = this.gameState();

            return (
                multipliersChancesMap[rtp]?.[this.risk.read()]?.[this.number_of_rows.read()] ?? []
            );
        });

        this.maxBetAmount = memo(() => {
            const { rtp, multipliersChancesMap, bet_limits, max_multiplier } = this.gameState();

            const highestMultiplierStr =
                multipliersChancesMap[rtp]?.[this.risk.read()]?.[
                    this.number_of_rows.read()
                ]?.[0]?.[0];

            const highestMultiplier = highestMultiplierStr
                ? parseFloat(highestMultiplierStr)
                : max_multiplier;

            return Math.min(bet_limits.bet.max, bet_limits.bet.max_payout / highestMultiplier);
        });

        this.maxPayout = memo(() => {
            const { bet_limits } = this.client.state();
            return bet_limits.bet.max_payout;
        });

        this.defaultMaxBetAmount = memo(() => {
            const { bet_limits } = this.client.state();
            return bet_limits.bet.max;
        });

        this.isBetAllowed = memo(() => {
            const bet_amount = Number(this.betAmount.read());
            const max_bet_amount = this.maxBetAmount();

            return bet_amount <= max_bet_amount;
        });

        this.isBettingLocked = memo(() => {
            const activeAction = this.#activeAction.read();
            const hasActiveNonBettingAction = activeAction
                ? !['Bet', 'Autobet', 'Nextbet'].includes(activeAction)
                : false;

            const { readyState = 'closed', isAuthenticated = false, isLoading } = this.gameState();
            return !(
                !isLoading &&
                !hasActiveNonBettingAction &&
                readyState === 'ready' &&
                isAuthenticated
            );
        });

        this.isBoardEditingAllowed = memo(() => !this.isBettingLocked() && this.isBoardIdle.read());

        this.incrementBalance = (payout: number) => {
            this.payout.update(payout);
            this.client.incrementBalance(payout);
            this.payout.update(0);
        };

        this.getMultiplier = (
            number_of_rows: number,
            risk: PlinkoRisk,
            bucket: number,
            rtp?: number,
        ): string => {
            const { multipliersChancesMap, rtp: liveRtp } = this.gameState();

            return (
                multipliersChancesMap[rtp || liveRtp]?.[risk]?.[number_of_rows]?.[bucket]?.[0] ||
                '0.0'
            );
        };

        this.#setBetAmount = (amount) => {
            this.betAmount.update(
                currencyToFixedString(this.#clampBetAmount(amount), this.currency()),
            );
        };

        this.#multiplyBetAmount = (factor) => {
            this.betAmount.update((amount) =>
                currencyToFixedString(
                    this.#clampBetAmount(Number(amount) * factor),
                    this.currency(),
                ),
            );
        };

        effect(() => {
            const newState = client.state();

            this.updateGameState((gameState) => {
                return {
                    ...gameState,
                    ...newState,
                };
            });
        });

        const hasNextbet = memo(() => {
            const { isAnimatingState, next_actions } = this.gameState();
            return !isAnimatingState && next_actions.includes('Nextbet');
        });
        const nextbetTimer = timer(800);
        const turboModeNextbetTimer = timer(350);

        this.client.on('roundEnd', () => {
            if (this.client.hasIntent('CloseAutobet')) {
                this.client.closeAutobet().catch(logDevError);
            }
            const betIntent = this.client.dequeueIntent('Bet');
            if (betIntent) {
                this.triggerAction('Bet', betIntent);
            } else {
                const autobetIntent = this.client.dequeueIntent('Autobet');
                if (autobetIntent) {
                    this.triggerAction('Autobet', autobetIntent);
                }
            }
        });

        this.onHostNotification((notification) => {
            if (notification.type === 'CURRENCY_CHANGED') {
                this.client.removeIntent('Bet');
                this.client.removeIntent('Autobet');
            }
        });

        effect(() => {
            if (hasNextbet()) {
                nextbetTimer.cancel();
                turboModeNextbetTimer.cancel();
                (this.isTurboMode ? turboModeNextbetTimer : nextbetTimer)(() => {
                    this.triggerAction('Nextbet');
                });
            }
        });

        let previousReadyState = this.client.readyState;
        let connectionStatusToast: ToastControls | null = null;
        const connectionStatusTimer = timer(1000);

        this.client.on('readyStateChanged', (newReadyState) => {
            if (newReadyState !== previousReadyState) {
                if (newReadyState === 'ready') {
                    connectionStatusTimer.cancel();
                    connectionStatusToast?.dismiss();
                    this.initSession().catch(logDevError);
                } else if (!connectionStatusToast && !untracked(this.gameState).isLoading) {
                    connectionStatusTimer(() => {
                        connectionStatusToast = this.showToast({
                            variant: 'error',
                            content: 'Lost connection. Trying to reconnect...',
                            isDismissable: false,
                            dismiss: undefined,
                            onDismiss() {
                                connectionStatusToast = null;
                            },
                        });
                    });
                }
                previousReadyState = newReadyState;
            }
        });

        this.client.on('beforeSend', ({ action }) => {
            this.#activeAction.update(action as MessageName);
        });

        this.client.on('afterHandle', () => {
            this.#activeAction.update(null);
        });

        this.client.on('beforeGameSessionSwitch', () => {
            const limit = this.autobet.autobet_limit.read();
            this.client.setAutobetState({
                bet_amount: this.betAmount.read(),
                number_of_rows: this.number_of_rows.read(),
                risk: this.risk.read(),
                autobet_limit: limit === '∞' ? Infinity : Number(limit),
                on_win: this.autobet.on_win.read(),
                on_win_reset: this.autobet.on_win_reset.read(),
                on_loss: this.autobet.on_loss.read(),
                on_loss_reset: this.autobet.on_loss_reset.read(),
                stop_on_profit: this.autobet.stop_on_profit.read(),
                stop_on_loss: this.autobet.stop_on_loss.read(),
            });
        });

        this.onStateReload(() => {
            const { isAutobetActive } = untracked(this.client.state);
            this.activeTab.update(isAutobetActive ? 'auto' : 'manual');
            if (isAutobetActive) {
                this.triggerAction('CloseAutobet');
            }
        });
    }

    public get shouldCloseAutobet(): SignalReader<boolean> {
        return this.#pendingIntents.CloseAutobet.read;
    }

    public onStateReload(handler: () => void): void {
        this.client.onStateReload(handler);
    }

    #clampBetAmount(value: number): number {
        const { bet_limits, balance } = this.gameState();
        return Number.isFinite(value)
            ? Math.min(bet_limits.bet.max, balance, Math.max(0, value))
            : 0;
    }

    public triggerAction(action: UIAction, data?: unknown): void {
        const { next_actions, readyState, isAutobetActive } = this.gameState();

        if (readyState !== 'ready') {
            return;
        }
        if (action === 'BetOrStop') {
            action = isAutobetActive
                ? 'CloseAutobet'
                : this.activeTab.read() === 'auto'
                  ? 'Autobet'
                  : 'Bet';
        }

        const canBet = next_actions.includes('Bet');
        switch (action) {
            case 'MultiplyBetAmount':
                if (typeof data !== 'number' || !canBet) {
                    return;
                }
                this.#multiplyBetAmount(data);
                break;

            case 'SetBetAmount':
                if (typeof data !== 'number' || !canBet) {
                    return;
                }
                this.#setBetAmount(data * this.usdRate());
                break;

            case 'Bet':
            case 'Autobet': {
                const uiState = {
                    bet_amount:
                        data &&
                        typeof data == 'object' &&
                        'bet_amount' in data &&
                        data.bet_amount &&
                        typeof data.bet_amount === 'string'
                            ? data.bet_amount
                            : this.betAmount.read(),
                    risk: this.risk.read(),
                    number_of_rows: this.number_of_rows.read(),
                };

                if (action === 'Autobet') {
                    const limit = this.autobet.autobet_limit.read();
                    this.client.setAutobetState({
                        ...uiState,
                        autobet_limit: limit === '∞' ? Infinity : Number(limit),
                        on_win: this.autobet.on_win.read(),
                        on_win_reset: this.autobet.on_win_reset.read(),
                        on_loss: this.autobet.on_loss.read(),
                        on_loss_reset: this.autobet.on_loss_reset.read(),
                        stop_on_profit: this.autobet.stop_on_profit.read(),
                        stop_on_loss: this.autobet.stop_on_loss.read(),
                    });
                } else {
                    this.client.setBetState(uiState);
                }

                if (!this.isBetAllowed()) {
                    return;
                }

                const { bet_limits, balance } = this.gameState();
                const currency = this.currency();

                const request: Writable<RequestData<'Bet' | 'Autobet'>> = {
                    currency,
                    risk: Number(uiState.risk),
                    number_of_rows: uiState.number_of_rows,
                };

                const betAmount = Number(uiState.bet_amount);
                if (betAmount > 0 && (betAmount < bet_limits.bet.min || betAmount > balance)) {
                    this.showToast({
                        content: 'Please place a valid bet amount.',
                        variant: 'error',
                    });
                    return;
                }

                if (!canBet) {
                    this.client.queueIntent({ type: action, bet_amount: uiState.bet_amount });
                    return;
                }

                request.bet_amount = betAmount;

                if (action === 'Autobet') {
                    const autobetRequest = {} as Writable<RequestData<'Autobet'>>;
                    const limit = this.autobet.autobet_limit.read();

                    if (limit == '∞') {
                        autobetRequest.autobet_limit = 0;
                    } else {
                        autobetRequest.autobet_limit = Number(limit);
                        if (
                            !Number.isFinite(autobetRequest.autobet_limit) ||
                            autobetRequest.autobet_limit < 0
                        ) {
                            this.showToast({
                                content: 'Please enter a valid number of bets.',
                                variant: 'error',
                            });
                            return;
                        }
                    }

                    for (const key of [
                        'on_win',
                        'on_loss',
                        'stop_on_profit',
                        'stop_on_loss',
                    ] as const) {
                        const value = this.autobet[key].read();
                        autobetRequest[key] = Number(value);
                        if (Number.isFinite(autobetRequest[key])) {
                            autobetRequest[key] = Number(value);
                        }
                    }

                    autobetRequest.on_win_reset = this.autobet.on_win_reset.read();
                    autobetRequest.on_loss_reset = this.autobet.on_loss_reset.read();

                    Object.assign(request, autobetRequest);
                }

                Promise.resolve()
                    .then(() => {
                        this.client[action.toLowerCase() as 'bet' | 'autobet'](request);
                    })
                    .catch(logDevError);

                this.isBoardIdle.update(false);

                break;
            }
            case 'CloseAutobet':
                if (!isAutobetActive) {
                    return;
                }
                if (next_actions.includes('CloseAutobet')) {
                    void this.client.closeAutobet().catch(logDevError);
                } else {
                    this.client.addIntent({ type: 'CloseAutobet' });
                }
                break;
            case 'Nextbet':
                if (!next_actions.includes('Nextbet') || !this.isBetAllowed()) {
                    return;
                }

                void this.client.nextbet().catch(logDevError);
                this.isBoardIdle.update(false);
                break;
            case 'SetRisk':
                if (this.isBoardEditingAllowed()) {
                    this.risk.update(data as PlinkoRisk);
                }
                break;
            case 'SetNumberOfRows':
                if (this.isBoardEditingAllowed()) {
                    this.number_of_rows.update(data as number);
                }
                break;
            case 'IncreaseNumberOfRows':
                if (this.isBoardEditingAllowed() && this.number_of_rows.read() <= 16) {
                    this.number_of_rows.update((prev) => ++prev);
                }
                break;
            case 'DecreaseNumberOfRows':
                if (this.isBoardEditingAllowed() && this.number_of_rows.read() > 8) {
                    this.number_of_rows.update((prev) => --prev);
                }
                break;
            default:
                logDevError(`Unexpected action: ${String(action)}`);
                return;
        }
    }

    public get toastBarControlsRef(): RefObject<ToastBarControls> {
        return this.#toastBarControlsRef;
    }

    public get notificationsControlsRef(): RefObject<ToastBarControls> {
        return this.#notificationsControlsRef;
    }

    public get hotkeysModalControlsRef(): RefObject<ModalControls> {
        return this.#hotkeysModalControlsRef;
    }

    public get provablyFairModalControlsRef(): RefObject<ModalControls> {
        return this.#provablyFairModalControlsRef;
    }

    public showToast(toast: ToastProps): ToastControls | null {
        if (this.#toastBarControlsRef.current) {
            return this.#toastBarControlsRef.current.showToast({
                dismiss: { after: 2000 },
                ...toast,
            });
        }
        return null;
    }

    public showNotification(toast: ToastProps): ToastControls | null {
        if (this.#notificationsControlsRef.current) {
            return this.#notificationsControlsRef.current.showToast({
                dismiss: { after: 2500 },
                isDismissable: false,
                ...toast,
            });
        }
        return null;
    }

    public async verifySeeds(params: VerifySeedParams): Promise<FinalState> {
        return this.client.verifySeeds(params);
    }
}
