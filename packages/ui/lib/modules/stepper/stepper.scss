@use 'sass:math';
@use '../../styles/common/bem';
@use '../../styles/common/prop';
@use '../../styles/common/util';

$-size: 40px;
$-gap: 4px;

@mixin styles {
    @include bem.module('stepper') {
        display: flex;
        flex-direction: column;
        gap: util.to-rem($-gap);
        min-block-size: util.to-rem($-size);
        inline-size: auto;
        aspect-ratio: 1;
        background: prop.get('button-gradient');
        color: prop.get('color-zinc-300');
        transition: background 0.3s;

        @include prop.set(
            (
                icon-size: util.to-rem(16px),
                button-gradient-angle: 135deg,
                button-gradient: linear-gradient(
                        prop.get(button-gradient-angle),
                        #373737 0%,
                        #171717 100%
                    ),
            )
        );

        &:hover {
            @include prop.set(button-gradient-angle, 180deg);
        }

        &:has(:active) {
            @include prop.set(button-gradient-angle, 0deg);
        }

        &:has(:disabled) {
            background: prop.get('color-zinc-950');
            color: prop.get('color-zinc-300');
            pointer-events: none;
            opacity: 0.5;
        }

        &__button {
            border: none;
            display: flex;
            justify-content: center;
            align-items: center;
            block-size: calc((100% - util.to-rem($-gap)) / 2);
            inline-size: 100%;
            transition: color 0.3s;
            background: none;

            &:hover {
                color: prop.get('color-white');
            }

            > #{bem.block-selector(module, icon)} {
                position: relative;
                inset-block: auto util.to-rem(-$-gap);
            }

            &:last-child > #{bem.block-selector(module, icon)} {
                inset-block: util.to-rem(-$-gap) auto;
            }
        }

        &--horizontal {
            flex-direction: row;
            max-block-size: calc(prop.get(input-scale, 1) * util.to-rem($-size));
            aspect-ratio: 0;
        }

        &--horizontal &__button {
            inline-size: calc(prop.get(input-scale, 1) * util.to-rem(math.div($-size - $-gap, 2)));
            block-size: calc(prop.get(input-scale, 1) * util.to-rem($-size));

            > #{bem.block-selector(module, icon)} {
                inset-block: auto;
                inset-inline: auto calc(prop.get(input-scale, 1) * util.to-rem(-$-gap));
            }

            &:last-child > #{bem.block-selector(module, icon)} {
                inset-inline: calc(prop.get(input-scale, 1) * util.to-rem(-$-gap)) auto;
            }
        }

        &--split {
            background: none;
            transition: none;
            inline-size: auto;
            border: none !important;
            padding: calc(prop.get(input-scale, 1) * util.to-rem($-gap));

            &:hover,
            &:has(:active) {
                @include prop.set(button-gradient-angle, 135deg);
            }
        }

        &--split &__button {
            border-radius: calc(prop.get(input-scale, 1) * util.to-rem(8px));
            background: prop.get('button-gradient');
            color: prop.get('color-white');
            transition: background 0.3s;
            block-size: calc(prop.get(input-scale, 1) * util.to-rem($-size * 0.95 - $-gap * 1.5));
            inline-size: auto;
            aspect-ratio: 17 / 19;

            @include prop.set(
                (
                    icon-size: calc(prop.get(input-scale, 1) * 0.65 * util.to-rem(18px)),
                    button-gradient: linear-gradient(
                            prop.get(button-gradient-angle),
                            #373737 0%,
                            #171717 100%
                        ),
                )
            );

            &:hover {
                @include prop.set(button-gradient-angle, 180deg);
            }

            &:active {
                @include prop.set(button-gradient-angle, 0deg);
            }

            &:disabled {
                background: prop.get('color-zinc-950');
                color: prop.get('color-zinc-300');
                pointer-events: none;
                opacity: 0.5;
            }

            > #{bem.block-selector(module, icon)} {
                inset: auto;
                inset-inline-end: calc(20% - (prop.get(input-scale, 1) - 1) * 30%);
            }
            &:last-child > #{bem.block-selector(module, icon)} {
                inset: auto;
                inset-inline-start: calc(20% - (prop.get(input-scale, 1) - 1) * 30%);
            }
        }

        &--split:not(&--horizontal) &__button {
            inline-size: calc(prop.get(input-scale, 1) * util.to-rem($-size * 0.95));
            block-size: auto;
            aspect-ratio: 19 / 17;

            > #{bem.block-selector(module, icon)} {
                inset: auto;
                inset-block-end: 2%;
            }
            &:last-child > #{bem.block-selector(module, icon)} {
                inset: auto;
                inset-block-start: 2%;
            }
        }
    }
}
