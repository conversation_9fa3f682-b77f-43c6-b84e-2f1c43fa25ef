{"compilerOptions": {"target": "ESNext", "lib": ["ES2023"], "module": "ESNext", "moduleResolution": "<PERSON><PERSON><PERSON>", "moduleDetection": "force", "composite": true, "declaration": true, "declarationMap": true, "isolatedModules": true, "allowImportingTsExtensions": true, "noEmit": true, "esModuleInterop": true, "forceConsistentCasingInFileNames": true, "useDefineForClassFields": true, "strict": true, "noImplicitAny": true, "noUnusedLocals": true, "noUnusedParameters": true, "exactOptionalPropertyTypes": true, "noImplicitReturns": true, "noFallthroughCasesInSwitch": true, "noUncheckedIndexedAccess": true, "skipLibCheck": true, "baseUrl": "./src", "plugins": [{"name": "typescript-eslint-language-service"}], "types": ["vite/client"]}}