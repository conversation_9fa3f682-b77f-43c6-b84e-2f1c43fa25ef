import type { <PERSON>a, StoryObj } from '@storybook/html';
import { html } from '../../component';
import { namespaced } from '../../utilities/classes';
import { Icon, type IconProps } from './icon';
import iconNames from './icon-names';

export default {
    title: 'Modules/Icons',
} satisfies Meta;

const div = html('div');

export const Library: StoryObj = ({ size }: { size: string }) => {
    return div(
        {
            className: namespaced('l-box', 'l-grid', 'u-gap--xl'),
            cssProps: {
                'icon-size': { var: `size-${size}` },
            },
        },
        ...iconNames.map((name) =>
            div(
                { className: namespaced('l-cluster', 'u-gap--s') },
                Icon({ name, description: name }),
                name,
            ),
        ),
    ).render();
};
Library.argTypes = {
    size: {
        control: 'select',
        options: ['xs', 's', 'm', 'l', 'xl'],
    },
};
Library.args = {
    size: 'xl',
};

export const Currency: StoryObj = (props: IconProps) =>
    div(
        {
            className: namespaced('l-box', 'l-grid', 'u-gap--xl'),
        },
        Icon(props),
    ).render();
Currency.argTypes = {
    currency: {
        control: 'text',
    },
    description: {
        control: 'text',
    },
};
Currency.args = {
    currency: '$',
    description: 'US Dollars',
};
