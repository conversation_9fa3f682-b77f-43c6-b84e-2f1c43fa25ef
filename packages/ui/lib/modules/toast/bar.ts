import {
    defineComponent,
    forwardRef,
    html,
    type Component,
    type Props,
    type Ref,
} from '../../component';
import { mergeClasses, withNamespace } from '../../utilities/classes';
import { Toast, type ToastControls, type ToastProps } from './toast';

export interface ToastBarControls {
    /**
     * Create and show a new toast.
     */
    showToast(this: void, toast: ToastProps): ToastControls;
}

export interface ToastBarProps extends Props<HTMLDivElement> {
    /**
     * Callback to receive the toast bar controls.
     */
    controls(this: void, controls: ToastBarControls): void;

    readonly position?: 'fixed' | 'absolute';

    readonly align?: 'start' | 'center' | 'end';

    readonly animate?: 'default' | 'fade';
}

export const ToastBar = defineComponent(
    'Toast.Bar',
    ({ render }) =>
        ({
            controls,
            position = 'fixed',
            align = 'end',
            animate = 'default',
            ...props
        }: ToastBarProps): Component<HTMLDivElement> =>
            html('div')({
                ...props,
                className: mergeClasses(props.className, {
                    'm-toast-bar': true,
                    [`m-toast-bar--${align}`]: align !== 'end',
                }),
                ref: forwardRef(props.ref, (element: HTMLElement) => {
                    const slideIn = animate === 'default';
                    const toastBlockSize = `--${withNamespace('toast-block-size')}`;
                    const toastTransitionDuration = `--${withNamespace('toast-transition-duration')}`;

                    element.style.setProperty('position', position);

                    controls({
                        showToast(toast) {
                            const controlsRef: Ref<ToastControls> = { current: null };
                            const toastElement = render(
                                Toast({
                                    ...toast,
                                    controls: (toastControls) => {
                                        controlsRef.current = toastControls;
                                        if (toast.controls) {
                                            toast.controls(toastControls);
                                        }
                                    },
                                }),
                            );

                            toastElement.style.setProperty(toastTransitionDuration, '0s');

                            element.appendChild(toastElement);

                            const { offsetHeight } = toastElement;

                            if (slideIn) {
                                toastElement.style.setProperty(toastBlockSize, '0');
                            }

                            toastElement.style.setProperty('opacity', '0');

                            setTimeout(() => {
                                toastElement.style.removeProperty(toastTransitionDuration);

                                setTimeout(() => {
                                    if (slideIn) {
                                        toastElement.addEventListener(
                                            'transitionend',
                                            () => {
                                                toastElement.style.setProperty(
                                                    toastBlockSize,
                                                    '100%',
                                                );
                                            },
                                            { once: true },
                                        );
                                        toastElement.style.setProperty(
                                            toastBlockSize,
                                            `${offsetHeight}px`,
                                        );
                                    }
                                    toastElement.style.removeProperty('opacity');
                                }, 100);
                            }, 0);

                            return controlsRef.current!;
                        },
                    });
                }),
            }),
);
