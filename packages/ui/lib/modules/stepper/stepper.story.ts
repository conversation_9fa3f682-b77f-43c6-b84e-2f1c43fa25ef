import type { Meta, StoryObj } from '@storybook/html';
import { fn } from '@storybook/test';
import { Stepper, type StepperProps } from './stepper';

const meta: Meta = {
    title: 'Modules/Stepper',
};
export default meta;

export const Default: StoryObj = (props: StepperProps) => {
    return Stepper(props).render();
};
Default.argTypes = {
    incrementDescription: { control: 'text' },
    decrementDescription: { control: 'text' },
    horizontal: { control: 'boolean' },
    split: { control: false },
};
Default.args = {
    incrementDescription: 'Increment',
    decrementDescription: 'Decrement',
    horizontal: false,
    split: false,
    onIncrement: fn().mockName('onIncrement'),
    onDecrement: fn().mockName('onDecrement'),
};

export const Split: StoryObj = (props: StepperProps) => {
    return Stepper(props).render();
};
Split.argTypes = {
    incrementDescription: { control: 'text' },
    decrementDescription: { control: 'text' },
    horizontal: { control: 'boolean' },
    split: { control: false },
};
Split.args = {
    incrementDescription: 'Increment',
    decrementDescription: 'Decrement',
    horizontal: false,
    split: true,
    onIncrement: fn().mockName('onIncrement'),
    onDecrement: fn().mockName('onDecrement'),
};
