import type { GameActionName } from '@monkey-tilt/client';
import type { LimboState } from './state';

interface GameStateUpdate extends Partial<LimboState> {
    readonly next_actions: ReadonlyArray<GameActionName>;
}

declare module '@monkey-tilt/client' {
    export interface MessageTypes {
        readonly Autobet: {
            readonly request: {
                readonly currency: string;
                readonly bet_amount?: number;
                readonly multiplier?: number;
                readonly autobet_limit?: number;
                readonly on_win?: number;
                readonly on_win_reset?: boolean;
                readonly on_loss?: number;
                readonly on_loss_reset?: boolean;
                readonly stop_on_profit?: number;
                readonly stop_on_loss?: number;
            };
            readonly response: GameStateUpdate;
        };

        readonly Nextbet: {
            readonly request: void;
            readonly response: GameStateUpdate;
        };

        readonly CloseAutobet: {
            readonly request: void;
            readonly response: GameStateUpdate;
        };
    }

    export interface BetPayload {
        readonly currency: string;
        readonly bet_amount?: number;
        readonly multiplier?: number;
    }

    export interface GameState extends LimboState {}
}

export {};
