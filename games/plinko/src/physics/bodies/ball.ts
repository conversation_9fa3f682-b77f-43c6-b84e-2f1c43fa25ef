import { BALL_RADIUS, BOUNCE, FRICTION, GRAVITY } from '../constants';
import type { Board } from '../board';
import { DynamicBody, type CollisionInfo } from './body';

export class Ball extends DynamicBody {
    #radius: number;

    #position: {
        x: number;
        y: number;
    };

    #velocity: {
        x: number;
        y: number;
    };

    #bucketNumber: number | undefined;

    /**
     * Create a new ball.
     *
     * The initial velocity is set to 0, and the ball will fall under the influence of gravity.
     *
     * @param x - The initial horizontal position of the ball.
     * @param y - The initial vertical position of the ball.
     * @param radius - The radius of the ball. Defaults to BALL_RADIUS.
     */
    public constructor(x: number, y: number, radius = BALL_RADIUS) {
        super();
        this.#position = { x, y };
        this.#velocity = { x: 0, y: 0 };
        this.#radius = radius;
    }

    /**
     * Whether the ball is still "alive" (i.e. not in a bucket).
     */
    public get isAlive(): boolean {
        return this.#bucketNumber === undefined;
    }

    /**
     * The bucket number the ball fell into, if any.
     */
    public get bucketNumber(): number | undefined {
        return this.#bucketNumber;
    }

    /**
     * The ball's current horizontal position.
     */
    public get x(): number {
        return this.#position.x;
    }

    /**
     * The ball's current vertical position.
     */
    public get y(): number {
        return this.#position.y;
    }

    /**
     * The ball's radius.
     */
    public get radius(): number {
        return this.#radius;
    }

    /**
     * Update the ball's position and velocity based on the elapsed time.
     *
     * @param dt - Delta time in seconds.
     */
    public update(dt: number): void {
        this.#velocity.y += round(GRAVITY * dt);

        this.#position.x += round(this.#velocity.x * dt);
        this.#position.y += round(this.#velocity.y * dt);
    }

    protected handleCollision({ deltaX, deltaY, overlap, distance }: CollisionInfo): void {
        const normalX = round(deltaX / distance);
        const normalY = round(deltaY / distance);

        const offset = round(overlap + this.#radius * 0.25);

        this.#position.x += round(normalX * offset);
        this.#position.y += round(normalY * offset);

        const normalVelocity = round(
            (1 + BOUNCE) * (this.#velocity.x * normalX + this.#velocity.y * normalY),
        );

        this.#velocity.x -= round(normalVelocity * normalX);
        this.#velocity.y -= round(normalVelocity * normalY);

        const tangentialVelocity = round(this.#velocity.x * -normalY + this.#velocity.y * normalX);
        this.#velocity.x -= round(tangentialVelocity * -normalY * FRICTION);
        this.#velocity.y -= round(tangentialVelocity * normalX * FRICTION);
    }

    /**
     * Check if the ball is in any bucket.
     *
     * If the ball is in a bucket, the bucketNumber will be set and the ball will
     * not be considered "alive" anymore, and should be removed from simulation.
     *
     * @param board - The Board object to derive the bucket positions from.
     */
    public checkBuckets(board: Board): void {
        if (this.#bucketNumber !== undefined) {
            return;
        }

        if (this.#position.y + this.#radius * 0.75 > board.height) {
            const bucket = Math.floor(this.#position.x / board.cellSize + 0.5);

            if (bucket >= 1 && bucket < board.columns) {
                this.#position.y = board.height;
                this.#bucketNumber = bucket;
            }
        }
    }
}

const FACTOR = 10 ** 11; // Rounds to 11 decimal places
function round(x: number): number {
    return Math.round(x * FACTOR) / FACTOR;
}
