import {
    defineComponent,
    forwardRef,
    Frame,
    html,
    Label,
    namespaced,
    Toast,
    withNamespace,
    type Component,
    type Props,
    type RefObject,
    type ToastBarControls,
} from '@monkey-tilt/ui';
import type { BaccaratBetAmount } from '../../../game/state';
import { type BaccaratUI } from '../../baccarat';
import { Balance } from '../balance/balance';
import { Loader } from '../loader/loader';
import { Bet } from './bet';
import { Hand, type HandProps } from './hand';

export interface TableProps extends Props<HTMLDivElement> {
    /**
     * The Baccarat UI state manager.
     */
    readonly ui: BaccaratUI;
}

export const Table = defineComponent(
    'Table',
    ({ state }) =>
        ({ ui, ...props }: TableProps): Component<HTMLDivElement> => {
            const { memo, effect, signal } = state();

            const div = html('div');

            const deck: RefObject<HTMLDivElement> = { current: null };

            const [isLoading, setIsLoading] = signal(ui.gameState().isLoading);
            effect(() => {
                let timer: ReturnType<typeof setTimeout> | undefined;
                const newIsLoading = ui.gameState().isLoading;

                if (newIsLoading) {
                    timer = setTimeout(() => {
                        setIsLoading(true);
                    }, 1500);
                } else {
                    setIsLoading(false);
                }

                return () => {
                    if (timer) {
                        clearTimeout(timer);
                    }
                };
            });

            const bankerHand = memo(() => ui.gameState().banker_hand);
            const playerHand = memo(() => ui.gameState().player_hand);

            function animateHand(element: RefObject<HTMLDivElement>): Partial<HandProps> {
                const before = { x: 0, y: 0 };
                let firstRender = true;
                return {
                    onBeforeCardsAdded: () => {
                        if (element.current) {
                            const { x, y } = element.current.getBoundingClientRect();
                            before.x = x;
                            before.y = y;
                            if (firstRender) {
                                element.current.style.visibility = 'hidden';
                            }
                        }
                    },
                    onCardsAdded: ({ detail: cards }) => {
                        const { isTurboMode } = ui;
                        const duration = (ms: number) => (isTurboMode ? 0.1 : ms);

                        if (deck.current) {
                            const deckRect = deck.current.getBoundingClientRect();
                            for (let i = 0; i < cards.length; i++) {
                                const card = cards[i]!;
                                const rect = card.getBoundingClientRect();

                                const fromX = deckRect.x - rect.x;
                                const fromY = deckRect.y - rect.y - deckRect.height;

                                card.animate(
                                    [
                                        { transform: 'translate(0, 0)' },
                                        { transform: `translate(${fromX}px, ${fromY}px)` },
                                    ],
                                    {
                                        duration: duration(1),
                                        fill: 'forwards',
                                    },
                                );

                                card.animate(
                                    [
                                        { transform: `translate(${fromX}px, ${fromY}px)` },
                                        { transform: 'translate(0, 0)' },
                                    ],
                                    {
                                        delay: duration(i * 100),
                                        duration: duration(300),
                                        easing: 'ease-in-out',
                                        fill: 'forwards',
                                    },
                                );
                            }

                            if (firstRender && element.current) {
                                setTimeout(() => {
                                    element.current!.style.visibility = 'visible';
                                }, 100);
                            }
                        }

                        void ui.playSound('deal');

                        if (firstRender || !element.current) {
                            firstRender = false;
                            return;
                        }

                        const { x, y } = element.current.getBoundingClientRect();

                        element.current.animate(
                            [
                                { transform: `translate(${before.x - x}px, ${before.y - y}px)` },
                                { transform: 'translate(0, 0)' },
                            ],
                            {
                                duration: duration(150),
                                easing: 'ease-out',
                                fill: 'forwards',
                            },
                        );
                    },
                    onBeforeCardsRemoved: (event) => {
                        event.preventDefault();

                        const { isTurboMode } = ui;
                        const duration = (ms: number) => (isTurboMode ? 0.1 : ms);

                        for (let i = 0; i < event.detail.length; i++) {
                            const animation = event.detail[i]!.animate(
                                [
                                    { opacity: 1, transform: 'translate(0, 0)' },
                                    { opacity: 0, transform: 'translate(-50%, 30%)' },
                                ],
                                {
                                    delay: duration(i * 50),
                                    duration: duration(150),
                                    easing: 'ease-out',
                                    fill: 'forwards',
                                },
                            );
                            if (i === event.detail.length - 1) {
                                animation.onfinish = () => {
                                    event.detail.forEach((card) => card.remove());
                                    if (
                                        !element.current!.querySelector(
                                            `.${withNamespace('m-card')}`,
                                        )
                                    ) {
                                        firstRender = true;
                                    }
                                };
                            }
                        }
                    },
                };
            }

            const bankerHandElement: RefObject<HTMLDivElement> = { current: null };
            const playerHandElement: RefObject<HTMLDivElement> = { current: null };

            const handleBetClick = (bet: BaccaratBetAmount) => (event: MouseEvent) => {
                if (event && (event.button === 2 || event.altKey)) {
                    ui.triggerAction('ClearSingleBet', bet);
                } else {
                    ui.triggerAction('IncreaseSingleBet', bet);
                }
            };

            const displayValue = memo(() => ({ currency: ui.currency() }));
            const betOdds = memo(() => ui.gameState().bet_odds);

            return Frame(
                {
                    ...props,
                    ref: forwardRef(props.ref, (element: HTMLDivElement) => {
                        effect(() => {
                            element.classList.toggle(withNamespace('is-loading'), isLoading());
                        });
                    }),
                    className: { 'm-baccarat__table': true },
                },
                Loader({
                    isLoading,
                    text: [
                        { text: 'Loading, please wait...', delay: 1000 },
                        { text: 'This is taking longer than expected...', delay: 5000 },
                        { text: 'Please check your internet connection!', delay: 8000 },
                    ],
                    ref: (element: HTMLDivElement) => {
                        effect(() => {
                            element.classList.toggle(withNamespace('u-hide'), !isLoading());
                        });
                    },
                }),
                Balance({ ui }),
                div(
                    { className: namespaced('m-baccarat__hands') },
                    div(
                        { className: namespaced('m-baccarat__player') },
                        Hand({
                            ui,
                            name: 'Player',
                            hand: playerHand,
                            ref: playerHandElement,
                            ...animateHand(playerHandElement),
                        }),
                    ),
                    div(
                        { className: namespaced('m-baccarat__banker') },
                        Hand({
                            ui,
                            name: 'Banker',
                            hand: bankerHand,
                            ref: bankerHandElement,
                            ...animateHand(bankerHandElement),
                        }),
                    ),
                ),
                div(
                    { className: namespaced('l-stack', 'u-gap--xs', 'm-baccarat__ribbons') },
                    Label({ text: 'Banker Dragon 7 is a push', variant: 'ribbon-l' }),
                    Label({ text: 'Tie pays 8 to 1', variant: 'ribbon-r' }),
                ),
                div(
                    { className: namespaced('m-baccarat__bets') },
                    Bet({
                        ui,
                        className: namespaced('m-baccarat__bet--player'),
                        displayValue,
                        label: 'Player',
                        odds: `x${betOdds().player}`,
                        onClick: handleBetClick('player_bet_amount'),
                        value: ui.bets.player_bet_amount.read,
                    }),
                    Bet({
                        ui,
                        className: namespaced('m-baccarat__bet--banker'),
                        displayValue,
                        label: 'Banker',
                        odds: `x${betOdds().banker}`,
                        onClick: handleBetClick('banker_bet_amount'),
                        value: ui.bets.banker_bet_amount.read,
                    }),
                    Bet({
                        ui,
                        className: namespaced('m-baccarat__bet--tie'),
                        label: 'Tie',
                        odds: `x${betOdds().tie}`,
                        onClick: handleBetClick('tie_bet_amount'),
                        value: ui.bets.tie_bet_amount.read,
                    }),
                    Bet({
                        ui,
                        className: namespaced('m-baccarat__bet--side', 'u-text--center'),
                        label: [
                            'Panda\xA08',
                            html('span')({ className: { 'u-hide@m': true } }, ' Side\xA0Bet'),
                        ],
                        odds: `x${betOdds().panda_sidebet}`,
                        onClick: handleBetClick('panda_bet_amount'),
                        value: ui.bets.panda_bet_amount.read,
                        tooltip:
                            'Panda 8: This side bet wins if the ‘Player’ hand wins with 3 cards in hand that have a total value of exactly 8.',
                    }),
                    Bet({
                        ui,
                        className: namespaced('m-baccarat__bet--side', 'u-text--center'),
                        label: [
                            'Dragon\xA07',
                            html('span')({ className: { 'u-hide@m': true } }, ' Side\xA0Bet'),
                        ],
                        odds: `x${betOdds().dragon7_sidebet}`,
                        onClick: handleBetClick('dragon_7_bet_amount'),
                        value: ui.bets.dragon_7_bet_amount.read,
                        tooltip:
                            'Dragon 7: This side bet wins if the ‘Banker’ hand wins with 3 cards in hand that have a total value of 7.',
                    }),

                    div(
                        { className: { 'm-baccarat__bet-controls': true } },
                        Label({
                            text: 'Undo',
                            icon: 'undo',
                            onClick: () => ui.triggerAction('UndoBet'),
                        }),
                        Label({
                            text: 'Clear',
                            icon: 'clear',
                            onClick: () => ui.triggerAction('SetAllBets', 0),
                        }),
                    ),
                ),
                div(
                    {
                        className: namespaced('m-baccarat__deck'),
                        ref: forwardRef(deck, (element: HTMLDivElement) => {
                            const isTableEmpty = memo(
                                () => ui.gameState().player_hand.cards.length == 0,
                            );
                            effect(() => {
                                if (!isTableEmpty()) {
                                    element.classList.add(withNamespace('is-visible'));
                                }
                            });
                        }),
                    },
                    div(),
                    div(),
                    div(),
                ),
                Toast.Bar({
                    position: 'absolute',
                    controls: (toastControls: ToastBarControls) => {
                        ui.toastBarControlsRef.current = toastControls;
                    },
                }),
                Toast.Bar({
                    position: 'absolute',
                    animate: 'fade',
                    align: 'center',
                    css: {
                        bottom: '20%',
                    },
                    controls: (toastControls: ToastBarControls) => {
                        ui.notificationsControlsRef.current = toastControls;
                    },
                }),
            );
        },
);
