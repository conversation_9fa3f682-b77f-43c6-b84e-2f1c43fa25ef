@use 'sass:math';
@use '@monkey-tilt/ui/scss/common/bem';
@use '@monkey-tilt/ui/scss/common/prop';
@use '@monkey-tilt/ui/scss/common/respond';
@use '@monkey-tilt/ui/scss/common/util';
@use '@monkey-tilt/ui/scss/config/metrics';
@use '../card/card';

@mixin styles {
    @include bem.module('blackjack') {
        display: grid;
        grid-template-columns: metrics.fluid-size(308, 340) 1fr;
        grid-template-rows: 1fr util.to-rem(42px);
        gap: metrics.fluid-size(10, 12);
        padding: metrics.fluid-size(16, 0);
        background: prop.get(color-zinc-950);

        &-container {
            container: blackjack / inline-size;

            @include prop.set(measure, 100%);
        }

        &__sidebar {
            grid-column: 1;
            grid-row: 1 / span 2;

            @include bem.module('frame__section') {
                @include prop.set(frame-padding, util.to-rem(20px));
            }

            & > :last-child {
                @include prop.set(frame-padding, util.to-rem(12px));
            }
        }

        &__sidebar &__options,
        &__sidebar > &__bet {
            display: none;
        }

        &__actions {
            display: grid;
            gap: prop.get(size-xs);
            grid-template-columns: 1fr 1fr;

            @include respond.from(m) {
                > :nth-child(2n + 1):last-child {
                    grid-column: span 2;
                }
            }

            @include respond.until(m) {
                $u-hide: bem.block-selector(utility, 'hide\\@m');

                > :nth-child(2n + 1 of :not(#{$u-hide})):nth-last-child(1 of :not(#{$u-hide})) {
                    grid-column: span 2;
                }
            }
        }

        @container blackjack (inline-size < #{respond.breakpoint-value('m')}) {
            grid-template-columns: 1fr;
            grid-template-rows: 60vh auto;

            &__tabs {
                order: 2;

                &#{bem.block-selector(module, frame__section)}::before {
                    display: block;
                }
            }

            &__sidebar {
                grid-row: 2;

                & > :last-child {
                    order: 3;
                }
            }

            &__sidebar &__options,
            &__sidebar > &__bet {
                display: flex;
            }

            &__options-bar,
            &__controls &__bet-button {
                display: none;
            }

            &__hand {
                &:has(> :nth-child(4)) {
                    gap: 0;
                }

                @for $i from 1 through 5 {
                    &:has(> :nth-child(#{$i + 3})) {
                        @include card.overlap(0.8 - $i * 0.1);
                    }
                }
            }
        }

        &__table {
            position: relative;
            display: grid;
            grid-template-columns: 1fr;
            grid-template-rows: 1fr auto 1fr;
            gap: prop.get(size-m-xl);
            max-height: min-content;
            overflow: hidden;

            @include prop.set(frame-padding, 0 prop.get(size-m), $important: true);

            @include bem.module('toast-bar') {
                z-index: 100;
            }
        }

        &__table#{bem.namespaced-selector('is-loading')}
            > :not(#{bem.block-selector(module, loader)}) {
            opacity: 0;
        }

        &:has(&__hand:nth-child(2)) &__table {
            grid-template-rows: repeat(3, auto);
        }

        &__deck {
            position: absolute;
            inset: 0 prop.get(size-m-xl) auto auto;
            inline-size: calc(metrics.fluid-size(70, 120) * prop.get(card-scale, 1));
            aspect-ratio: 120 / 165;
            transform: translateY(-100%);
            transition: transform prop.get(blackjack-anim-duration, 0.3s) ease-in-out;

            & > * {
                position: absolute;
                inline-size: 100%;
                aspect-ratio: 120 / 165;
                inset-block-end: 50%;
                transition: inset-block-end prop.get(blackjack-anim-duration, 0.2) ease-in-out;

                border-radius: 5% / 3.5%;
                background-size: cover;
                background-color: #202020;
                background-image: radial-gradient(
                    50% 50% at 50% 50%,
                    rgba(255, 229, 0, 0.18) 0%,
                    rgba(255, 229, 0, 0) 100%
                );

                &::before {
                    content: '';
                    position: absolute;
                    inset: 0;
                    border-radius: inherit;
                    border: util.to-rem(1px) solid prop.get(color-mt-400);
                }

                &::after {
                    content: '';
                    position: absolute;
                    inset: auto 7% 5% auto;
                    border-radius: inherit;
                    // background: url(./modules/card/assets/decks/default.svg#mt) no-repeat;
                    background: url(./modules/card/assets/decks/default/mt.svg) no-repeat;
                    background-size: cover;
                    inline-size: 25%;
                    aspect-ratio: 1;
                }

                &:nth-child(2) {
                    transition-delay: prop.get(blackjack-anim-duration, 0.15s);
                }

                &:nth-child(3) {
                    transition-delay: prop.get(blackjack-anim-duration, 0.3s);
                }
            }

            &#{bem.namespaced-selector('is-visible')} {
                transform: translateY(-50%);

                > :first-child {
                    inset-block-end: 0;
                }

                > :nth-child(2) {
                    inset-block-end: 12%;
                }

                > :nth-child(3) {
                    inset-block-end: 24%;
                }
            }
        }

        &__ribbons {
            max-inline-size: fit-content;
            justify-self: center;
            justify-content: center;
        }

        $-hand-padding: prop.get(size-s);
        $-counter-size: calc(prop.get(size-2xs-xs) * 2 + prop.get(lh-1));

        &__hand {
            position: relative;
            display: flex;
            align-items: flex-end;
            justify-content: center;
            gap: prop.get(size-m);
            padding: calc($-hand-padding * 2 + $-counter-size) 0 $-hand-padding;
            margin: 0 auto;

            @include prop.set(outline-width-fluid, metrics.fluid-size(2, 5));

            @include bem.module(label) {
                position: absolute;
                inset: calc($-hand-padding / 2) 50% auto auto;
                transform: translate(50%, 0);

                &:only-child {
                    visibility: hidden;
                }
            }

            &:has(> :nth-child(6)) {
                gap: 0;
            }

            @for $i from 1 through 5 {
                &:has(> :nth-child(#{$i + 5})) {
                    @include card.overlap(0.8 - $i * 0.1);
                }
            }

            @each $state, $color in (bust: red-400, win: green-400, push: yellow-400) {
                &--is-#{$state} {
                    @include prop.set(
                        (
                            outline-width: prop.get(outline-width-fluid),
                            outline-color: prop.get(color-#{$color}),
                        )
                    );
                }
            }
        }

        &__hand#{bem.namespaced-selector('is-highlighted')} {
            @include prop.set(
                (
                    outline-width: prop.get(outline-width-fluid),
                    outline-color: prop.get(color-blue-500),
                )
            );
        }

        &__table:has(&__hand:nth-child(2)) #{bem.block-selector(module, card)} {
            @include prop.set(card-scale, 0.75);
        }

        &__dealer,
        &__player {
            display: flex;
            flex-wrap: wrap;
            gap: prop.get(size-2xs) prop.get(size-xl);
            justify-content: space-evenly;
        }

        &__dealer &__hand {
            margin-block-start: auto;
        }

        &__player &__hand {
            margin-block-end: auto;
            padding: $-hand-padding 0 calc($-hand-padding * 2 + $-counter-size);

            @include bem.module(label) {
                inset-block: auto calc($-hand-padding / 2);
                z-index: 1;
                outline: prop.get(outline-width, 0) solid prop.get(outline-color, transparent);
                transition: outline-width prop.get(blackjack-anim-duration, 0.3s) ease;
            }
        }

        &__player
            :is(
                #{bem.block-selector(module, card__face)},
                #{bem.block-selector(module, card__back)}
            ) {
            outline: prop.get(outline-width, 0) solid prop.get(outline-color, transparent);
            transition:
                transform prop.get(blackjack-anim-duration, 0.6s) ease,
                outline-width prop.get(blackjack-anim-duration, 0.3s) ease;
        }
    }
}
