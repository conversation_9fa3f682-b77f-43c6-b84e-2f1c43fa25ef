import {
    Button,
    defineComponent,
    forwardRef,
    html,
    Input,
    Label,
    Stack,
    Stepper,
    type Component,
    type IconName,
    type InputControls,
    type InputProps,
    type LabelControls,
    type Reactive,
    type RefObject,
    type Renderable,
    type Signal,
    type SignalReader,
} from '@monkey-tilt/ui';

export interface NumberInputProps extends Omit<InputProps, 'step' | 'value' | 'disabled'> {
    /**
     * The signal representing the current input value.
     */
    readonly value: Signal<string>;

    /**
     * The signal representing the disabled state of the input.
     */
    readonly disabled?: SignalReader<boolean>;

    /**
     * The preset amounts to be displayed as buttons below the input.
     */
    readonly presets?: number[];

    /**
     * The step value to be used when incrementing or decrementing the value. Defaults to 1.
     *
     * Set to 0 to disable the stepper.
     */
    readonly step?: number;

    /**
     * Whether to show the stepper buttons. Ignored if step == 0. Defaults to false.
     */
    readonly showStepper?: boolean | 'horizontal';

    /**
     * The number of fraction digits to be displayed. Defaults to 0.
     */
    readonly fractionDigits?: Reactive<number> | undefined;

    /**
     * The label to be displayed above the input.
     */
    readonly label?: string;

    /**
     * The optional label icon.
     */
    readonly icon?: IconName;

    /**
     * The signal producing the label tooltip's content.
     */
    readonly tooltip?: SignalReader<Renderable>;

    /**
     * The minimum and maximum bet amounts allowed. Defaults to {min: 0, max: 1_000_000}.
     */
    readonly limits?: SignalReader<{ min: number; max: number }>;

    /**
     * The optional default value to be used when the input is left empty after losing focus.
     */
    readonly defaultValue?: string;

    /**
     * The optional multiplier buttons.
     */
    readonly multipliers?: { label: string; factor: number }[];

    /**
     * Called when the value is multiplied by a factor.
     */
    readonly onMultiply?: (factor: number) => void;

    /**
     * Optional buttons rendered inside the input.
     * If both buttons and multipliers are provided, the buttons will be rendered first.
     */
    readonly buttons?: { label: string; onClick: () => void }[];

    /**
     * The conversion factor applied to step increments/decrements and preset values.
     */
    readonly conversionFactor?: SignalReader<number>;

    /**
     * Whether to allow the value to be set to Infinity. Defaults to false.
     */
    readonly allowInfinity?: boolean;
}

export const NumberInput = defineComponent(
    'NumberInput',
    ({ state }) =>
        ({
            value: valueSignal,
            disabled: disabledSignal = () => false,
            presets = [],
            showStepper = false,
            step = 1,
            fractionDigits = 2,
            label,
            icon,
            tooltip,
            className,
            limits = () => ({ min: 0.001, max: 1000 }),
            defaultValue,
            multipliers = [],
            buttons = [],
            onMultiply,
            allowInfinity = false,
            conversionFactor = () => 1,
            action,
            ref,
            ...props
        }: NumberInputProps): Component<HTMLDivElement> => {
            const { effect, memo, untracked, read } = state();

            const inputWrapper: RefObject<HTMLDivElement> = { current: null };
            const input: RefObject<InputControls> = { current: null };

            const precision =
                typeof fractionDigits === 'number'
                    ? () => fractionDigits
                    : memo(() => read(fractionDigits));

            const zero = memo(() => Math.max(0, limits().min).toFixed(precision()));
            const id = props.id ?? `bet-amount-${Math.random().toString(36).slice(2)}`;

            const mask =
                props.mask ??
                ((value: string, prevValue: string): string => {
                    return value == '' || /^\d*(\.\d*)?$/.test(value) ? value : prevValue;
                });

            const update = (
                value: string,
                delta?: { inc: number } | { factor: number },
                force = false,
            ): string => {
                if (allowInfinity && value === '∞') {
                    return value;
                }

                let num = Number.parseFloat(value);

                const { min, max } = limits();
                const fractionDigits = precision();

                if (!Number.isFinite(num)) {
                    if (!force) {
                        return value;
                    }
                    value =
                        num == Infinity && !allowInfinity ? max.toFixed(fractionDigits) : zero();
                } else {
                    if (delta) {
                        if ('inc' in delta) {
                            num += delta.inc;
                        } else {
                            num *= delta.factor;
                        }
                        const factor = Math.pow(10, fractionDigits);
                        num = Math.round(num * factor) / factor;
                    }
                    value = !force
                        ? String(num)
                        : Math.max(min, Math.min(max, num)).toFixed(fractionDigits);
                }

                valueSignal.update(value);

                return value;
            };

            const handleDisabledState = (element: HTMLElement): void => {
                effect(() => {
                    const isDisabled = disabledSignal();
                    if (element && 'disabled' in element) {
                        element.disabled = isDisabled;
                    }
                });
            };

            update(mask(valueSignal.read(), zero()));

            effect(() => {
                const value = valueSignal.read();
                untracked(() => {
                    if (input.current) {
                        input.current.update(value);
                    }
                });
            });

            effect(() => {
                const { max } = limits();
                untracked(() => {
                    if (Number.parseFloat(valueSignal.read()) > max) {
                        update(max.toFixed(precision()));
                    }
                });
            });

            const actionButtons = buttons.map(({ label, onClick }) =>
                Button({
                    variant: 'tertiary',
                    ref: handleDisabledState,
                    onClick,
                    label,
                }),
            );

            for (const { label, factor } of multipliers) {
                actionButtons.push(
                    Button({
                        variant: 'tertiary',
                        ref: handleDisabledState,
                        onClick: (e: MouseEvent) => {
                            e.stopPropagation();
                            if (onMultiply) {
                                onMultiply(factor);
                            } else {
                                update(valueSignal.read(), { factor }, true);
                            }
                        },
                        label,
                    }),
                );
            }

            return Stack(
                { gap: 's' },
                label != undefined &&
                    Label({
                        text: label,
                        icon,
                        htmlFor: id,
                        controls: ({ setTooltip }: LabelControls) => {
                            if (tooltip) {
                                effect(() => {
                                    setTooltip(tooltip());
                                });
                            }
                        },
                    }),
                Input(
                    {
                        ref: forwardRef(ref, (element: HTMLDivElement) => {
                            inputWrapper.current = element;
                        }),

                        id,
                        ...props,

                        value: valueSignal.read(),
                        inputMode: 'decimal',
                        mask,
                        onChange(event: InputEvent) {
                            if (event.target instanceof HTMLInputElement) {
                                update(event.target.value.replace(/^\.|\.$/g, ''));
                            }
                        },

                        onKeydown(event: KeyboardEvent) {
                            if (!(event.target instanceof HTMLInputElement)) {
                                return;
                            }

                            const { selectionStart, selectionEnd } = event.target;

                            switch (event.key) {
                                case 'ArrowUp':
                                    update(
                                        valueSignal.read(),
                                        { inc: step * conversionFactor() },
                                        true,
                                    );
                                    break;
                                case 'ArrowDown':
                                    update(
                                        valueSignal.read(),
                                        { inc: -step * conversionFactor() },
                                        true,
                                    );
                                    break;
                                default:
                                    return;
                            }

                            event.preventDefault();
                            event.target.setSelectionRange(selectionStart, selectionEnd);
                        },

                        onFocus(event: FocusEvent) {
                            if (event.target instanceof HTMLInputElement) {
                                event.target.select();
                            }
                        },

                        onBlur(event: FocusEvent) {
                            if (
                                event.target instanceof HTMLInputElement &&
                                event.target.value.trim() === '' &&
                                input.current
                            ) {
                                input.current.update(
                                    update(defaultValue ?? zero(), undefined, true),
                                );
                            } else {
                                update(valueSignal.read(), undefined, true);
                            }
                        },

                        action:
                            action ??
                            (step !== 0 && showStepper
                                ? Stepper({
                                      horizontal: showStepper === 'horizontal',
                                      incrementRef: handleDisabledState,
                                      decrementRef: handleDisabledState,
                                      onIncrement(e: MouseEvent) {
                                          e.stopPropagation();
                                          update(
                                              valueSignal.read(),
                                              { inc: step * conversionFactor() },
                                              true,
                                          );
                                      },
                                      onDecrement(e: MouseEvent) {
                                          e.stopPropagation();
                                          update(
                                              valueSignal.read(),
                                              { inc: -step * conversionFactor() },
                                              true,
                                          );
                                      },
                                  })
                                : undefined),

                        inputRef: forwardRef(props.inputRef, handleDisabledState),
                        controls: (controls: InputControls) => {
                            input.current = controls;
                        },
                    },
                    ...actionButtons,
                ),
                presets.length > 0 &&
                    html('div')(
                        {
                            className: { 'l-cluster': true },
                            cssProps: { 'cluster-gap': { var: 'size-2xs' } },
                        },
                        ...presets.map((value) => {
                            return Button({
                                variant: 'secondary',
                                ref: handleDisabledState,
                                onClick: (e: MouseEvent) => {
                                    e.stopPropagation();
                                    update(
                                        (value * conversionFactor()).toFixed(precision()),
                                        undefined,
                                        true,
                                    );
                                },
                                label: memo(() =>
                                    (value * conversionFactor()).toFixed(precision()),
                                ),
                            });
                        }),
                    ),
            );
        },
);
