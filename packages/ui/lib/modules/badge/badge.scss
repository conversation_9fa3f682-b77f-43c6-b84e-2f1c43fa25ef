@use '../../styles/common/bem';
@use '../../styles/common/prop';
@use '../../styles/common/respond';
@use '../../styles/common/type-style' as *;
@use '../../styles/common/util';
@use '../../styles/config/metrics';

$padding-desktop: util.to-rem(12px);
$padding-mobile: util.to-rem(10px);
$radius: util.to-rem(12px);

@mixin styles {
    @include bem.module(badge) {
        display: inline-flex;
        align-items: center;
        justify-content: center;

        padding: $padding-desktop;
        border-radius: $radius;
        background: prop.get(color-zinc-700);

        @include type-style(
            c1,
            $with: (font-weight: semi-bold, font-size: metrics.fluid-size(14, 24), line-height: 1)
        );
        color: prop.get(color-white);

        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;

        max-inline-size: fit-content;

        &--is-clickable {
            cursor: pointer;
        }

        &--pill {
            border-radius: util.to-rem(9999px);
            min-inline-size: util.to-rem(100px);
            color: prop.get(color-black);
        }

        &--success {
            background-color: prop.get(color-green-450);
            color: prop.get(color-black);
        }

        &--fail {
            background-color: prop.get(color-zinc-700);
        }

        @include respond.until(m) {
            padding: $padding-mobile;
        }

        &-ticker {
            display: flex;
            flex-direction: row;
            flex-wrap: nowrap;
            overflow: hidden;
            justify-content: flex-end;
            gap: prop.get(size-xs-s);
            pointer-events: none;
            mask-image: linear-gradient(to right, transparent 0%, #000 10%);

            &__item {
                overflow: hidden;
                pointer-events: all;
                max-inline-size: min-content;
                flex-basis: 100%;
                flex-shrink: 0;
                opacity: 1;
                transition:
                    flex-basis 1s ease-in,
                    opacity 0.5s ease-in-out;

                &--enter,
                &--leave {
                    flex-basis: 0;
                    opacity: 0;
                }
            }

            &--vertical {
                mask-image: none;

                #{bem.namespaced-selector(m-badge-ticker__item)} {
                    overflow: hidden;
                    display: block;
                    max-block-size: 56px;
                    opacity: 1;
                    transition:
                        max-height 0.4s ease-in-out,
                        opacity 0.4s ease-in-out;

                    &--leave {
                        display: none;
                    }

                    &--enter,
                    &--leave {
                        max-block-size: 0;
                        opacity: 0;
                    }
                }

                @include respond.from(s) {
                    flex-direction: column-reverse;
                }
            }
        }
    }
}
