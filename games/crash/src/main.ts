import type { GameContext } from '@monkey-tilt/client';
import { createRoot, withNamespace } from '@monkey-tilt/ui';
import { createCrashClient, type CrashClientOptions } from './game/crash';
import { Game, CrashUI } from './ui';
import { MockTransport } from './game/mock';
import { MockAPIClient } from './game/mock-api';

export interface CrashRunOptions extends Omit<CrashClientOptions, 'root'> {
    readonly container: HTMLElement | string;
    readonly useMockData?: boolean;
}

const assetsUrl = new URL(
    globalThis && globalThis.document.currentScript instanceof HTMLScriptElement
        ? globalThis.document.currentScript.src
        : location.href,
);

if (globalThis && globalThis.document.currentScript instanceof HTMLScriptElement) {
    const link = document.createElement('link');
    link.rel = 'stylesheet';
    link.href = new URL('./crash.css', assetsUrl).href;
    document.head.appendChild(link);
}

export async function run({ container, useMockData = false, ...options }: CrashRunOptions): Promise<GameContext> {
    // Add global error handlers for debugging
    window.addEventListener('unhandledrejection', (event) => {
        console.error('🚨 UNHANDLED PROMISE REJECTION:', event.reason);
        console.error('🚨 Promise:', event.promise);
    });

    window.addEventListener('error', (event) => {
        // Ignore common ResizeObserver warnings
        if (event.message && event.message.includes('ResizeObserver loop completed with undelivered')) {
            return;
        }
        console.error('🚨 GLOBAL ERROR:', event.error);
        console.error('🚨 Message:', event.message);
        console.error('🚨 Source:', event.filename, 'Line:', event.lineno);
    });
    const element: HTMLElement | null =
        typeof container === 'string' ? document.querySelector(container) : container;

    if (!element) {
        throw new Error('Invalid container provided');
    }

    const handleResize = () => {
        element.classList.add(
            ...['s-app', 'l-cover', 'l-cover--stretch', 'm-crash-container'].map(withNamespace),
        );
        element.style.setProperty(`--${withNamespace('min-block-size')}`, '100%');
    };

    handleResize();

    const root = createRoot(element, () => {
        element.innerHTML = '';
    });

    let clientOptions = { ...options, root };

    // Use mock implementations if requested
    if (useMockData) {
        console.log('🎭 Using mock data for crash game');
        
        try {
            const mockTransport = new MockTransport();
            const mockApiClient = new MockAPIClient(options.gatewayUrl);
            mockApiClient.gameId = options.gameId;
            mockApiClient.gameSessionId = options.gameSessionId;
            
            clientOptions = {
                ...clientOptions,
                transport: mockTransport,
                apiClient: mockApiClient,
            };
            console.log('✅ Mock implementations created successfully');
        } catch (error) {
            console.error('🚨 Error creating mock implementations:', error);
            throw error;
        }
    }

    let client;
    try {
        console.log('🎯 Creating crash client with options:', clientOptions);
        client = await createCrashClient(clientOptions as CrashClientOptions);
        console.log('✅ Crash client created successfully');
    } catch (error) {
        console.error('🚨 Error creating crash client:', error);
        throw error;
    }

    const ui = new CrashUI({
        client,
        assetsUrl,
        root,
    });

    root.render(Game({ ui, className: { 'l-cover__principal': true } }));

    return {
        unmount: () => {
            ui.dispose();
        },
        onAction: (callback) => {
            ui.hostActionDispatcher = callback;
        },
        notify: (notification) => {
            if (notification.type === 'CONTAINER_RESIZED') {
                handleResize();
            }
            ui.handleHostNotification(notification);
        },
    };
} 