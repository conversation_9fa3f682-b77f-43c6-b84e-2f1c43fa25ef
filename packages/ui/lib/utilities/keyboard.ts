export interface KeyMapping<T extends string = string, U = unknown> {
    readonly action: T;
    readonly data?: U | undefined;
    readonly key: string;
    readonly description?: string | undefined;
    readonly canRepeat?: boolean | undefined;
    readonly hidden?: boolean | undefined;
}

export class KeyboardControllerConfiguration<T extends string = string> {
    readonly scope?: Document | HTMLElement;
    readonly focusTarget?: HTMLElement | globalThis.Window;
    readonly keyMap?: Map<string, Omit<KeyMapping<T>, 'key'>>;
}

export interface RegisterKeyOptions {
    readonly canRepeat?: boolean | undefined;
}

export interface KeyboardController<T extends string = string, U = unknown> {
    isDisabled: boolean;
    readonly mappings: IterableIterator<KeyMapping<T>>;
    onAction(handler: (action: T, data: U | undefined) => void): void;
    registerKey(action: T, key: string, description?: string, options?: RegisterKeyOptions): void;
    dispose(): void;
}

export class NullKeyboardController<T extends string = string, U = unknown>
    implements KeyboardController<T, U>
{
    public isDisabled = true;

    public dispose(): void {
        // Do nothing
    }

    public onAction(_: (action: T, data: U) => void): void {
        // Do nothing
    }

    public registerKey(
        _action: string,
        _key: string,
        _description?: string,
        _options?: RegisterKeyOptions,
    ): void {
        // Do nothing
    }

    public get mappings(): IterableIterator<KeyMapping<T>> {
        return new Map().values();
    }
}

export class DOMKeyboardController<T extends string = string, U = unknown>
    implements KeyboardController<T, U>
{
    public isDisabled = false;

    #isInFocus = true;
    #keyMap: Map<string, KeyMapping<T>>;
    #actionHandlers = new Set<(action: T, data: U | undefined) => void>();
    #abortController = new AbortController();

    public constructor({
        scope = globalThis.document,
        focusTarget = globalThis.window,
        keyMap = new Map(),
    }: KeyboardControllerConfiguration<T> = {}) {
        this.#keyMap = new Map(
            Array.from(keyMap.entries(), ([key, mapping]) => {
                return [normalizeKey(key), { description: mapping.action, ...mapping, key }];
            }),
        );

        (scope as HTMLElement).addEventListener('keydown', this.#handleKeyDown, {
            signal: this.#abortController.signal,
        });

        focusTarget.addEventListener(
            'focus',
            () => {
                this.#isInFocus = true;
            },
            { signal: this.#abortController.signal },
        );

        focusTarget.addEventListener(
            'blur',
            () => {
                this.#isInFocus = false;
            },
            { signal: this.#abortController.signal },
        );
    }

    public dispose(): void {
        this.isDisabled = true;
        this.#actionHandlers.clear();
        this.#keyMap.clear();
        this.#abortController.abort();
    }

    public onAction(handler: (action: T, data: U | undefined) => void): void {
        this.#actionHandlers.add(handler);
    }

    public registerKey(
        action: T,
        key: string,
        description?: string,
        { canRepeat = false }: RegisterKeyOptions = {},
    ): void {
        this.#keyMap.set(normalizeKey(key), { action, key, description, canRepeat });
    }

    public get mappings(): IterableIterator<KeyMapping<T>> {
        return this.#keyMap.values();
    }

    #handleKeyDown = (event: KeyboardEvent): void => {
        if (
            event.defaultPrevented ||
            this.isDisabled ||
            !this.#isInFocus ||
            isEditable(event.target)
        ) {
            return;
        }

        let key = normalizeKey(event.key);

        const modifiers = {
            metaKey: 'meta',
            ctrlKey: 'ctrl',
            altKey: 'alt',
            shiftKey: 'shift',
        } as const;

        for (const [modifierKey, modifier] of Object.entries(modifiers) as [
            keyof typeof modifiers,
            string,
        ][]) {
            if (event[modifierKey]) {
                key = `${modifier}+${key}`;
            }
        }

        if (this.#keyMap.has(key)) {
            event.preventDefault();
            const mapping = this.#keyMap.get(key);
            if (mapping != null && (mapping.canRepeat || !event.repeat)) {
                for (const handler of this.#actionHandlers) {
                    handler(mapping.action, mapping.data as U);
                }
            }
        }
    };
}

function isEditable<T extends EventTarget>(element: T | null): boolean {
    return (
        element != null &&
        (element instanceof HTMLInputElement ||
            element instanceof HTMLTextAreaElement ||
            (element instanceof HTMLElement && element.isContentEditable))
    );
}

const remapping = new Map<string, string>([
    [' ', 'space'],
    ['os', 'meta'],
    ['spacebar', 'space'],
    ['left', 'arrowleft'],
    ['up', 'arrowup'],
    ['right', 'arrowright'],
    ['down', 'arrowdown'],
    ['del', 'delete'],
    ['esc', 'escape'],
]);

function normalizeKey(key: string): string {
    key = key.toLowerCase();
    return remapping.get(key) ?? key;
}
