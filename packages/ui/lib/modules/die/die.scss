@use '../../styles/common/bem';
@use '../../styles/common/prop';
@use '../../styles/common/util';
@use '../../styles/common/type-style' as *;
@use '../../styles/config/metrics';

$-neutral-color: prop.get(color-white);
$-win-color: prop.get(color-green-400);
$-loss-color: prop.get(color-red-500);
$-transition: 0.2s ease-in-out;

@mixin styles {
    @include bem.module(die) {
        position: relative;
        width: metrics.fluid-size(56, 82);
        aspect-ratio: 136 / 142;
        filter: drop-shadow(0 0 util.to-rem(6px) prop.get(die-border-color, transparent));
        transition: filter $-transition;
        user-select: none;

        &__shape,
        &__shape::after {
            position: absolute;
            inset: 0;
            mask: 100% 100% no-repeat;
            mask-image: url(../modules/die/assets/die.svg);
            background: prop.get(die-border-color, $-neutral-color);
            transition: background $-transition;
        }

        &__shape::after {
            content: '';
            inset: metrics.fluid-size(1.5, 3);
            background: prop.get(die-background, prop.get(color-zinc-900));
        }

        &--win {
            @include prop.set(
                (
                    die-border-color: $-win-color,
                    die-color: $-win-color,
                )
            );
        }

        &--loss {
            @include prop.set(
                (
                    die-border-color: $-loss-color,
                    die-color: $-loss-color,
                )
            );
        }

        &__number {
            position: absolute;
            z-index: 1;
            inset-block-start: 50%;
            inset-inline-start: 50%;
            transform: translate(-50%, -50%);
            color: prop.get(die-color, $-neutral-color);
            transition: color $-transition;

            @include type-style(
                multiplier,
                $with: (font-size: metrics.fluid-size(16, 26), line-height: 1)
            );
        }
    }
}
