import { formatCurrency } from '@monkey-tilt/client';
import {
    Chip,
    defineComponent,
    html,
    Icon,
    Label,
    mergeClasses,
    withNamespace,
    type Component,
    type Props,
    type Reactive,
    type Renderable,
} from '@monkey-tilt/ui';
import type { BaccaratUI } from '../../baccarat';
import { breakIntoChips, chipText, ChipValues } from '../../types/chips';

export interface BetProps extends Props<HTMLDivElement> {
    /**
     * The Baccarat UI state manager.
     */
    readonly ui: BaccaratUI;

    /**
     * The current bet value.
     */
    readonly value: Reactive<number>;

    /**
     * Whether to display the bet value (and which currency to use).
     */
    readonly displayValue?: Reactive<{ readonly currency: string } | undefined>;

    /**
     * The bet label (name).
     */
    readonly label: Renderable;

    /**
     * The winning odds of the bet.
     */
    readonly odds: string;

    /**
     * The optional tooltip to display (on desktop only).
     */
    readonly tooltip?: Renderable;
}

export const Bet = defineComponent(
    'Bet',
    ({ state, render }) =>
        ({
            ui,
            value,
            label,
            odds,
            displayValue,
            tooltip,
            ...props
        }: BetProps): Component<HTMLDivElement> => {
            const { onPropChange } = state();

            return html('div')(
                {
                    ...props,
                    className: mergeClasses(props.className, { 'm-baccarat__bet': true }),
                },
                tooltip && Icon({ name: 'info', tooltip }),
                html('div')(
                    { className: { 'm-baccarat__bet-text': true } },
                    Label({
                        variant: 'subtle',
                        ref: (element: HTMLSpanElement) => {
                            const hiddenClassName = withNamespace('u-hide');
                            onPropChange(value, displayValue, (value, displayValue) => {
                                if (!displayValue) {
                                    element.classList.add(hiddenClassName);
                                } else {
                                    element.classList.remove(hiddenClassName);
                                    element.innerText =
                                        value == 0
                                            ? ''
                                            : formatCurrency(value, displayValue.currency);
                                }
                            });
                        },
                    }),
                    Label({ text: label, variant: 'medium' }),
                    Label({ text: odds, variant: 'subtle' }),
                ),
                html('div')({
                    className: { 'm-baccarat__bet-stacks': true },
                    ref: (element: HTMLDivElement) => {
                        onPropChange(value, ui.usdRate, (value, usdRate) => {
                            const stacks = breakIntoChips(value / usdRate);

                            element.innerHTML = '';

                            const order = ChipValues.slice().sort(
                                (a, b) => (stacks[b] ?? 0) - (stacks[a] ?? 0),
                            );

                            let zIndex = 1;

                            for (const value of order) {
                                if (!stacks[value]) {
                                    continue;
                                }
                                element.appendChild(
                                    render(
                                        Chip.Stack({
                                            value,
                                            text: chipText(value),
                                            count: stacks[value],
                                            css: { 'z-index': `${zIndex++}` },
                                        }),
                                    ),
                                );
                            }
                        });
                    },
                }),
            );
        },
);
