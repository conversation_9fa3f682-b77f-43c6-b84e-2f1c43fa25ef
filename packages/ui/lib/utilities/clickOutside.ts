const handlers = new Map<HTMLElement, (event: MouseEvent) => void>();
let didAttachListener = false;

export function onClickOutside(
    element: HTMLElement,
    callback: (event: MouseEvent) => void,
): () => void {
    if (!didAttachListener) {
        didAttachListener = true;
        document.addEventListener(
            'click',
            (event: MouseEvent) => {
                for (const [element, handler] of handlers) {
                    if (!element.isConnected) {
                        handlers.delete(element);
                        continue;
                    }
                    if (!element.contains(event.target as Node)) {
                        handler(event);
                    }
                }
            },
            { capture: true },
        );
    }

    handlers.set(element, callback);

    return () => {
        handlers.delete(element);
    };
}
