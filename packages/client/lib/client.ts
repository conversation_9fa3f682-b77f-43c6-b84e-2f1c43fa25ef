import type { <PERSON>, Signal, SignalReader, SignalUpdater, StoreSnapshot } from '@monkey-tilt/ui';
import { logDevError, structuralEqual } from '@monkey-tilt/utils';
import { APIClient, type SeedData, type VerifySeedParams } from './api';
import { GameError } from './error';
import { IntentManager, isIntent, type Intent } from './intents';
import type {
    ActionLike,
    AnyState,
    BetPayload,
    ErrorMessage,
    GameActionName,
    GameState,
    Message,
    MessageName,
    MessageType,
    RequestData,
    RequestType,
    ResponseData,
    SessionActionName,
    State,
    StateLike,
    EventsOutOfSync,
} from './messages';
import { isAuthenticate, isGameState, isState } from './messages';
import type { ReadyState, Transport } from './transports';
import { RetryError, runWithRetry, uuid } from './utils';
import { MessageQueue } from './utils/messageQueue';

/**
 * Configuration for a game client.
 */
export interface GameClientConfiguration {
    /**
     * The UI root.
     */
    readonly root: Root;

    /**
     * The game ID.
     */
    readonly gameId: string;

    /**
     * The ID of the game session.
     */
    readonly gameSessionId: string;

    /**
     * The currency code associated with the game session.
     */
    readonly currency: string;

    /**
     * The URL of the game server gateway.
     */
    readonly gatewayUrl: string | URL;

    /**
     * The API client to use for making API requests.
     *
     * If not provided, a new API client will be created using the `gatewayUrl`.
     */
    readonly apiClient?: APIClient;

    /**
     * The transport to use for sending and receiving messages.
     */
    readonly transport: Transport;

    /**
     * Whether to require authentication for GameEvent actions. Defaults to `true`.
     */
    readonly requireAuthentication?: boolean;

    /**
     * Whether to allow concurrent actions. Defaults to `false`.
     */
    readonly allowConcurrentActions?: boolean;

    /**
     * The timeout in milliseconds for action responses. Defaults to `undefined`.
     *
     * If set and greater than 0, every action will reject with an error if the
     * response does not resolve within the specified time.
     */
    readonly actionTimeout?: number;

    /**
     * The number of times to retry sending an action if it fails. Defaults to `0`.
     */
    readonly autoRetryAttempts?:
        | ((type: MessageType, action: MessageName, data: RequestData<MessageName>) => number)
        | number;

    /**
     * The delay in milliseconds between retry attempts. Defaults to `50`.
     */
    readonly autoRetryDelay?: number;

    /**
     * An array or function that tells us what to sync.
     */
    readonly syncableActions?: ReadonlyArray<string> | ((action: string) => boolean);
}

/**
 * A subscription to a specific action response.
 * @internal
 */
interface Subscription<Action extends MessageName = MessageName> {
    /**
     * The resolve function for the returned Promise.
     */
    readonly resolve: (value: ResponseData<Action>) => void;

    /**
     * The reject function for the returned Promise.
     */
    readonly reject: (reason: Error) => void;
}

interface GameClientEventHandlerTypes {
    readyStateChanged: (readyState: ReadyState) => void;
    error: (event: ErrorEvent) => void;
    beforeSend: (action: ActionLike, context: { readonly abortSignal: AbortSignal }) => void;
    afterHandle: (state: State | GameError) => void;
    roundEnd: () => void;
    idle: () => void;
    intentAdded: (intent: Intent) => void;
    intentRemoved: (intent: Intent) => void;
    beforeGameSessionSwitch: (data: { currency: string }) => void;
    afterGameSessionSwitch: (data: { currency: string }) => void;
}

export type GameClientEvent = keyof GameClientEventHandlerTypes;

export interface EventHandlerOptions {
    readonly abortSignal?: AbortSignal;
}

export interface SessionState {
    readonly id: string;
    readonly currency: string;
    readonly usdRate: number;
}

/**
 * A base client for interacting with a game server.
 */
export abstract class GameClient<T extends GameState> extends IntentManager {
    public readonly gameId: string;

    protected readonly gatewayUrl: URL;
    protected readonly api: APIClient;
    protected readonly root: Root;

    readonly #eventHandlers = new Map<
        GameClientEvent,
        Set<GameClientEventHandlerTypes[GameClientEvent]>
    >();

    readonly #transport: Transport;
    readonly #subscriptions: Map<string, Subscription[]> = new Map();
    readonly #history: (Message | ErrorMessage)[] = [];
    readonly #requireAuthentication: boolean;
    readonly #areConcurrentActionsAllowed: boolean;
    readonly #actionTimeout: number;
    readonly #timeoutTimers: Map<string, ReturnType<typeof setTimeout>[]> = new Map();

    readonly #autoRetryAttempts: (
        type: MessageType,
        action: MessageName,
        data: RequestData<MessageName>,
    ) => number;
    readonly #autoRetryDelay: number;

    #eventOrder: number | undefined;
    #isClosed = false;
    #readyState: ReadyState = 'closed';
    #isAuthenticated = false;
    #pendingActions: ActionLike[] = [];
    #session: Signal<SessionState>;
    #sentActionIds: Set<string> = new Set();

    #isActionSyncable: (action: string) => boolean;
    #messageQueue: MessageQueue<{ message: StateLike | ErrorMessage; sync: boolean }>;

    protected isIdle = false;

    /**
     * Create a new game client.
     *
     * @param configuration - The game client configuration.
     */
    public constructor({
        root,
        gameId,
        gameSessionId,
        currency,
        gatewayUrl,
        transport,
        apiClient,
        requireAuthentication = true,
        allowConcurrentActions = false,
        actionTimeout = 0,
        autoRetryAttempts = 0,
        autoRetryDelay = 50,
        syncableActions = () => true,
    }: GameClientConfiguration) {
        super();

        this.root = root;
        this.gameId = gameId;
        this.gatewayUrl = new URL(gatewayUrl);

        this.#isActionSyncable =
            typeof syncableActions === 'function'
                ? syncableActions
                : Array.isArray(syncableActions)
                  ? (action) => syncableActions.includes(action)
                  : () => true;

        this.#messageQueue = new MessageQueue<{ message: StateLike | ErrorMessage; sync: boolean }>(
            ({ message, sync }) => {
                this.#handleMessage(message, sync);
            },
            1200,
        );

        if (apiClient) {
            this.api = apiClient;
        } else {
            this.api = new APIClient(this.gatewayUrl);
            this.api.gameId = gameId;
            this.api.gameSessionId = gameSessionId;
        }

        const { signal, memo, effect } = root.store;

        this.#session = signal<SessionState>({
            id: gameSessionId,
            currency,
            usdRate: 1,
        });

        const currencyCode = memo(() => this.#session.read().currency);
        effect(() => {
            const currency = currencyCode();

            let updateTimer: ReturnType<typeof setTimeout> | null = null;
            let shouldUpdate = true;

            const updateRate = () => {
                updateTimer = null;

                this.api.getUSDRate(currency).then(({ rate, expiresOn }) => {
                    if (!shouldUpdate) {
                        return;
                    }

                    this.#session.update((session) => ({ ...session, usdRate: rate }));

                    if (Number.isFinite(expiresOn)) {
                        let updateAfter = expiresOn - Date.now();
                        if (updateAfter <= 0) {
                            // update after 5min if expiresOn is in the past
                            updateAfter = 1000 * 60 * 5;
                        }
                        updateTimer = setTimeout(updateRate, updateAfter);
                    }
                });
            };

            updateRate();

            return () => {
                shouldUpdate = false;
                if (updateTimer) {
                    clearTimeout(updateTimer);
                }
            };
        });

        this.#transport = transport;
        this.#requireAuthentication = requireAuthentication;
        this.#areConcurrentActionsAllowed = allowConcurrentActions;
        this.#actionTimeout = actionTimeout;
        this.#autoRetryDelay = autoRetryDelay;
        this.#autoRetryAttempts =
            typeof autoRetryAttempts === 'number' ? () => autoRetryAttempts : autoRetryAttempts;

        this.resetAllowedActions();

        transport.onMessage(this.#processMessage);
        transport.onReadyStateChanged(this.#handleReadyStateChanged);

        this.#handleReadyStateChanged();
    }

    public get session(): SignalReader<SessionState> {
        return this.#session.read;
    }

    /**
     * The game client state reader.
     */
    public abstract get state(): SignalReader<T>;

    /**
     * Updates the game client state.
     */
    protected abstract updateState(
        ...args: Parameters<SignalUpdater<T>>
    ): ReturnType<SignalUpdater<T>>;

    /**
     * Whether the client is ready to send and receive messages (i.e., whether it is connected).
     */
    public get isReady(): boolean {
        return this.#readyState === 'ready';
    }

    /**
     * The current ready state of the client.
     */
    public get readyState(): ReadyState {
        return this.#readyState;
    }

    /**
     * Whether the client is authenticated.
     *
     * GameEvent actions can only be sent when authenticated.
     */
    public get isAuthenticated(): boolean {
        return this.#isAuthenticated;
    }

    /**
     * The message history.
     */
    public get history(): ReadonlyArray<Message | ErrorMessage> {
        return this.#history;
    }

    /**
     * The list of actions that can start a round.
     */
    protected get roundStartActions(): GameActionName[] {
        return ['Bet'];
    }

    /**
     * Add a handler for the ready state change.
     *
     * @param handler - The ready state change handler to add.
     * @param options - Optional parameters including an abort signal for removing the handler.
     * @returns A function to remove the handler.
     */
    public on(
        event: 'readyStateChanged',
        handler: (readyState: ReadyState) => void,
        options?: EventHandlerOptions,
    ): () => void;

    /**
     * Add a handler to be invoked before an action is sent. The handler can abort the action.
     *
     * @param handler - The handler that will be called before an action is sent.
     * @param options - Optional parameters including an abort signal for removing the handler.
     * @returns A function to remove the handler.
     */
    public on(
        event: 'beforeSend',
        handler: (action: ActionLike, context: { readonly abortSignal: AbortSignal }) => void,
        options?: EventHandlerOptions,
    ): () => void;

    /**
     * Add a handler to be invoked after an action response is handled.
     *
     * @param handler - The handler to be invoked after an action response is handled.
     * @param options - Optional parameters including an abort signal for removing the handler.
     * @returns A function to remove the handler.
     */
    public on(
        event: 'afterHandle',
        handler: (state: State | GameError) => void,
        options?: EventHandlerOptions,
    ): () => void;

    /**
     * Add an error handler.
     *
     * @param handler - The error handler to add.
     * @param options - Optional parameters including an abort signal for removing the handler.
     * @returns A function to remove the handler.
     */
    public on(
        event: 'error',
        handler: (event: ErrorEvent) => void,
        options?: EventHandlerOptions,
    ): () => void;

    /**
     * Add a handler to be invoked when a round ends.
     *
     * This doesn't necessarily matches the "round end" in the UI, as the UI might still be
     * performing animations and/or delayed state updates.
     *
     * @param handler - The handler to be invoked when a round ends.
     * @param options - Optional parameters including an abort signal for removing the handler.
     * @returns A function to remove the handler.
     */
    public on(event: 'roundEnd', handler: () => void, options?: EventHandlerOptions): () => void;

    /**
     * Add a handler to be invoked when the client is idle - that is, when:
     *  - client is authenticated,
     *  - no round is currently in progress, and
     *  - the UI is not performing any animations or delayed state updates.
     *
     * @param handler - The handler to be invoked when the client is idle.
     * @param options - Optional parameters including an abort signal for removing the handler.
     * @returns A function to remove the handler.
     */
    public on(event: 'idle', handler: () => void, options?: EventHandlerOptions): () => void;

    /**
     * Add a handler to be invoked when an intent is added.
     *
     * @param handler - The handler to be invoked when an intent is added.
     * @param options - Optional parameters including an abort signal for removing the handler.
     * @returns A function to remove the handler.
     */
    public on(
        event: 'intentAdded',
        handler: (intent: Intent) => void,
        options?: EventHandlerOptions,
    ): () => void;

    /**
     * Add a handler to be invoked when an intent is removed.
     *
     * @param handler - The handler to be invoked when an intent is removed.
     * @param options - Optional parameters including an abort signal for removing the handler.
     * @returns A function to remove the handler.
     */
    public on(
        event: 'intentRemoved',
        handler: (intent: Intent) => void,
        options?: EventHandlerOptions,
    ): () => void;

    /**
     * Add a handler to be invoked when the game session is about to be switched,
     * due to currency change.
     *
     * @param handler - The handler to be invoked when the game session is about to be switched.
     * @param options - Optional parameters including an abort signal for removing the handler.
     * @returns A function to remove the handler.
     */
    public on(
        event: 'beforeGameSessionSwitch',
        handler: (data: { currency: string }) => void,
        options?: EventHandlerOptions,
    ): () => void;

    /**
     * Add a handler to be invoked when the game session is switched, due to currency change.
     *
     * @param handler - The handler to be invoked when the game session is switched.
     * @param options - Optional parameters including an abort signal for removing the handler.
     * @returns A function to remove the handler.
     */
    public on(
        event: 'afterGameSessionSwitch',
        handler: (data: { currency: string }) => void,
        options?: EventHandlerOptions,
    ): () => void;

    public on<T extends GameClientEvent>(
        event: T,
        handler: GameClientEventHandlerTypes[T],
        { abortSignal }: EventHandlerOptions = {},
    ): () => void {
        let handlers = this.#eventHandlers.get(event);
        if (!handlers) {
            handlers = new Set();
            this.#eventHandlers.set(event, handlers);
        }

        const remove = () => {
            const handlers = this.#eventHandlers.get(event);
            if (handlers) {
                handlers.delete(handler);
            }
        };

        if (abortSignal) {
            abortSignal.addEventListener('abort', remove, { once: true });
        }

        handlers.add(handler);

        return remove;
    }

    protected fireEvent<T extends GameClientEvent>(
        event: T,
        ...args: Parameters<GameClientEventHandlerTypes[T]>
    ): void {
        const handlers = this.#eventHandlers.get(event);
        if (handlers) {
            for (const handler of handlers) {
                (handler as (...args: unknown[]) => void)(...args);
            }
        }
    }

    protected fireEventGenerator<T extends GameClientEvent>(
        event: T,
        ...args: Parameters<GameClientEventHandlerTypes[T]>
    ): Generator<ReturnType<GameClientEventHandlerTypes[T]>> {
        const handlers = this.#eventHandlers.get(event);
        return (function* () {
            if (handlers) {
                for (const handler of handlers) {
                    yield (
                        handler as (
                            ...args: unknown[]
                        ) => ReturnType<GameClientEventHandlerTypes[T]>
                    )(...args);
                }
            }
        })();
    }

    protected override onIntentAdded<T extends Intent>(intent: T): void {
        this.fireEvent('intentAdded', intent);

        if (isIntent(intent, 'SwitchGameSession')) {
            void this.api.getUSDRate(intent.currency); // prefetch conversion rate
            this.#trySwitchGameSession();
        }
    }

    protected override onIntentRemoved<T extends Intent>(intent: T): void {
        this.fireEvent('intentRemoved', intent);
    }

    public async getSeeds(): Promise<SeedData> {
        return this.api.getSeeds();
    }

    async setClientSeed(client_seed: string): Promise<void> {
        return this.api.setClientSeed(client_seed);
    }

    protected abstract validateSeedVerificationData(data: unknown): boolean;

    protected async _validateSeeds(params: VerifySeedParams): Promise<unknown> {
        const { data } = await this.api.verifySeeds(params);
        if (!this.validateSeedVerificationData(data)) {
            throw new Error('Failed to verify seeds');
        }
        return data;
    }

    /**
     * Send an authentication request.
     *
     * @param data - The authentication data.
     * @returns A promise that resolves with the authentication action response.
     */
    public async authenticate(
        shouldRetry?: (error: Error) => boolean,
    ): Promise<ResponseData<'auth'>> {
        return this.sendAndAwait('Authenticate', 'auth', {}, shouldRetry);
    }

    /**
     * Send a "Bet" action to start the game.
     */
    public abstract bet(payload: BetPayload): Promise<void>;

    public async reload(shouldRetry?: (error: Error) => boolean): Promise<GameState> {
        if (this.#isAuthenticated) {
            return this.sendAndAwait('GameState', 'GameState', shouldRetry);
        }
        return this.authenticate(shouldRetry).then(() =>
            this.sendAndAwait('GameState', 'GameState', shouldRetry),
        );
    }

    /**
     * The set of allowed actions in the current game state.
     *
     * Sending an action that is not in this set will throw an error.
     */
    protected allowedActions: Set<MessageName> = new Set();

    /**
     * Check if an action is allowed in the current game state.
     *
     * By default, this method checks if the action is in the `allowedActions` set.
     * Subclasses can override this method to implement custom action permission logic.
     *
     * @param action - The action to check.
     * @returns `true` if the action is allowed, otherwise `false`.
     */
    protected isActionAllowed<A extends ActionLike>(action: A): boolean {
        return this.can(action.action as MessageName);
    }

    /**
     * Check if an action is allowed in the current game state.
     *
     * @param action - The name of the action to check.
     * @returns `true` if the action is allowed, otherwise `false`.
     */
    public can<A extends MessageName>(action: A): boolean {
        return this.#requireAuthentication && !this.#isAuthenticated
            ? action === 'auth'
            : action === 'GameState' || this.allowedActions.has(action);
    }

    /**
     * Send a GameEvent action which does not require a payload.
     *
     * If the client is not authenticated, an error will be thrown.
     *
     * @param action - The action to send.
     * @returns The action ID.
     */
    protected sendGameEvent<A extends GameActionName>(
        action: RequestData<A> extends void ? A : never,
    ): string;

    /**
     * Send a GameEvent action with a payload.
     *
     * If the client is not authenticated, an error will be thrown.
     *
     * @param action - The action to send.
     * @param data - The action payload.
     * @returns The action ID.
     */
    protected sendGameEvent<A extends GameActionName>(
        action: RequestData<A> extends void ? never : A,
        data: RequestData<A>,
    ): string;

    protected sendGameEvent<A extends GameActionName>(action: A, data?: RequestData<A>): string {
        const request = this.#prepareGameEvent(action, data);
        if (request) {
            void this.#send(request);
            return request.action_id;
        }
        return '';
    }

    /**
     * Prepares a GameEvent action.
     *
     * If the client is not authenticated, an error will be raised.
     *
     * @param action - The action to send.
     * @param data - The action payload.
     * @returns The action object to send.
     */
    #prepareGameEvent<A extends GameActionName>(
        action: A,
        data?: RequestData<A>,
        isRetry = false,
    ): ActionLike | null {
        if (this.#requireAuthentication && !this.#isAuthenticated) {
            if (!isRetry) {
                this.#reportError(new GameError('Not authenticated', 'AUTH_FAILED', ''));
            }
            return null;
        }

        return this.#prepare(
            {
                type: 'GameEvent',
                action,
                game_id: this.gameId,
                data: data ?? {},
                eventOrder: ++this.#eventOrder!,
            },
            isRetry,
        );
    }

    #reportError(error: Error): Error {
        const event = new ErrorEvent('error', { error });
        this.fireEvent('error', event);
        if (!event.defaultPrevented) {
            throw error;
        }
        return error;
    }

    /**
     * Send a non-GameEvent action which does not require a payload.
     *
     * @param type - The type of the action to send.
     * @param action - The action to send.
     * @returns The action ID.
     */
    protected sendSessionAction<A extends SessionActionName>(
        type: RequestType<A>,
        action: RequestData<A> extends void ? A : never,
    ): string;

    /**
     * Send a non-GameEvent action with a payload.
     *
     * @param type - The type of the action to send.
     * @param action - The action to send.
     * @param data - The action payload.
     * @returns The action ID.
     */
    protected sendSessionAction<A extends SessionActionName>(
        type: RequestType<A>,
        action: RequestData<A> extends void ? never : A,
        data: RequestData<A>,
    ): string;

    protected sendSessionAction<A extends SessionActionName>(
        type: RequestType<A>,
        action: A,
        data?: RequestData<A>,
    ): string {
        const request = this.#prepareSessionAction(type, action, data);
        if (request) {
            void this.#send(request);
            return request.action_id;
        }
        return '';
    }

    /**
     * Prepares a non-GameEvent action with a payload.
     *
     * @param type - The type of the action to send.
     * @param action - The action to send.
     * @param data - The action payload.
     * @returns The action object to send.
     */
    #prepareSessionAction<A extends SessionActionName>(
        type: RequestType<A>,
        action: A,
        data?: RequestData<A>,
        isRetry = false,
    ): ActionLike | null {
        return this.#prepare(
            {
                type,
                action,
                game_id: this.gameId,
                data: (data ?? {}) as ActionLike['data'],
                ...(action == 'GameState' && this.#eventOrder !== undefined
                    ? { eventOrder: this.#eventOrder }
                    : {}),
            },
            isRetry,
        );
    }

    /**
     * Prepare a GameEvent action without payload to be sent and await the response.
     *
     * @param action - The action to send.
     * @returns A promise that resolves with the action response.
     */
    protected prepareSendAndAwait<A extends MessageName>(
        action: GameActionName,
        shouldRetry?: (error: Error) => boolean,
    ): () => Promise<ResponseData<A>> {
        return this.#prepareSendAndAwait('GameEvent', action as MessageName, {}, shouldRetry);
    }
    /**
     * Send a GameEvent action and await the response.
     *
     * @param action - The action to send.
     * @returns A promise that resolves with the action response.
     */
    protected sendAndAwait<A extends MessageName>(
        action: RequestData<A> extends void ? (A extends GameActionName ? A : never) : never,
        shouldRetry?: (error: Error) => boolean,
    ): Promise<ResponseData<A>>;

    /**
     * Send a GameEvent action with a payload and await the response.
     *
     * @param action - The action to send.
     * @param data - The action payload.
     * @returns A promise that resolves with the action response.
     */
    protected sendAndAwait<A extends MessageName>(
        action: RequestData<A> extends void ? never : A extends GameActionName ? A : never,
        data: A extends GameActionName ? RequestData<A> : never,
        shouldRetry?: (error: Error) => boolean,
    ): Promise<ResponseData<A>>;

    /**
     * Send a non-GameEvent action and await the response.
     *
     * @param type - The type of the action to send.
     * @param action - The action to send.
     * @returns A promise that resolves with the action response.
     */
    protected sendAndAwait<A extends MessageName>(
        type: A extends SessionActionName ? RequestType<A> : never,
        action: RequestData<A> extends void ? (A extends SessionActionName ? A : never) : never,
        shouldRetry?: (error: Error) => boolean,
    ): Promise<ResponseData<A>>;

    /**
     * Send a non-GameEvent action with a payload and await the response.
     *
     * @param type - The type of the action to send.
     * @param action - The action to send.
     * @param data - The action payload.
     * @returns A promise that resolves with the action response.
     */
    protected sendAndAwait<A extends MessageName>(
        type: A extends SessionActionName ? RequestType<A> : never,
        action: RequestData<A> extends void ? never : A extends SessionActionName ? A : never,
        data: A extends SessionActionName ? RequestData<A> : never,
        shouldRetry?: (error: Error) => boolean,
    ): Promise<ResponseData<A>>;

    protected sendAndAwait<A extends MessageName>(
        type: RequestType<A> | A,
        action?: A | RequestData<A> | ((error: Error) => boolean),
        data?: RequestData<A> | ((error: Error) => boolean),
        retry?: (error: Error) => boolean,
    ): Promise<ResponseData<A>> {
        let actionType: MessageType;
        let actionName: MessageName;
        let actionData = {} as RequestData<MessageName>;
        let shouldRetry = retry;

        if (typeof action === 'string') {
            actionType = type as RequestType<MessageName>;
            actionName = action as MessageName;
            if (typeof data === 'function') {
                shouldRetry = data as (error: Error) => boolean;
            } else {
                actionData = data as RequestData<MessageName>;
            }
        } else {
            actionType = 'GameEvent';
            actionName = type as GameActionName;
            if (typeof action === 'function') {
                shouldRetry = action as (error: Error) => boolean;
            } else {
                actionData = action as RequestData<GameActionName>;
                shouldRetry = data as (error: Error) => boolean;
            }
        }

        return this.#sendAndAwait(actionType, actionName, actionData, shouldRetry);
    }

    #prepareSendAndAwait(
        actionType: MessageType,
        actionName: MessageName,
        actionData: RequestData<MessageName>,
        shouldRetry?: (error: Error) => boolean,
        isRetry = false,
    ): () => Promise<ResponseData<MessageName>> {
        const action = this.#prepareAction(actionType, actionName, actionData, isRetry);

        if (!action) {
            return () =>
                Promise.reject(
                    new GameError('Action could not be sent', 'REQUEST_FAILED', '', {
                        type: actionType,
                        action: actionName,
                        data: actionData as Record<string, unknown>,
                        action_id: '',
                        game_id: undefined,
                        round_id: undefined,
                    }),
                );
        }

        return () => {
            if (action.eventOrder && action.eventOrder <= (this.#eventOrder ?? 0)) {
                Promise.reject(
                    new GameError('Action is outdated', 'OUTDATED_ACTION', '', {
                        type: actionType,
                        action: actionName,
                        data: actionData as Record<string, unknown>,
                        action_id: '',
                        game_id: undefined,
                        round_id: undefined,
                    }),
                );
            }

            return this.#sendPreparedAction(
                action,
                actionType,
                actionName,
                actionData,
                shouldRetry,
                isRetry,
            );
        };
    }

    #sendAndAwait(
        actionType: MessageType,
        actionName: MessageName,
        actionData: RequestData<MessageName>,
        shouldRetry?: (error: Error) => boolean,
        isRetry = false,
    ): Promise<ResponseData<MessageName>> {
        const action = this.#prepareAction(actionType, actionName, actionData, isRetry);

        if (!action) {
            return Promise.reject(
                new GameError('Action could not be sent', 'REQUEST_FAILED', '', {
                    type: actionType,
                    action: actionName,
                    data: actionData as Record<string, unknown>,
                    action_id: '',
                    game_id: undefined,
                    round_id: undefined,
                }),
            );
        }

        return this.#sendPreparedAction(
            action,
            actionType,
            actionName,
            actionData,
            shouldRetry,
            isRetry,
        );
    }

    #prepareAction(
        actionType: MessageType,
        actionName: MessageName,
        actionData: RequestData<MessageName>,
        isRetry: boolean,
    ): ActionLike | null {
        return actionType !== 'GameEvent'
            ? this.#prepareSessionAction(
                  actionType as RequestType<SessionActionName>,
                  actionName as SessionActionName,
                  actionData,
                  isRetry,
              )
            : this.#prepareGameEvent(
                  actionName as GameActionName,
                  actionData as RequestData<GameActionName>,
                  isRetry,
              );
    }

    #sendPreparedAction(
        action: ActionLike,
        actionType: MessageType,
        actionName: MessageName,
        actionData: RequestData<MessageName>,
        shouldRetry?: (error: Error) => boolean,
        isRetry = false,
    ): Promise<ResponseData<MessageName>> {
        return runWithRetry(
            {
                maxAttempts: this.#autoRetryAttempts(actionType, actionName, actionData),
                delay: this.#autoRetryDelay,
                shouldRetry: (error) =>
                    error instanceof Error
                        ? (shouldRetry?.(error) ??
                          (!(error instanceof GameError) || error.code === 'TIMEOUT'))
                        : true,
            },
            () => this.#send(action).then(() => this.response(action.action_id)),
        ).catch((error) => {
            let promise: Promise<ResponseData<MessageName>> | null = null;

            if (error instanceof RetryError && !error.retryAborted) {
                if (!this.#isClosed && !isRetry && actionType !== 'GameState') {
                    promise = this.sendAndAwait('GameState', 'GameState')
                        .catch(() => {
                            throw this.#reportError(error);
                        })
                        .then(() =>
                            this.#sendAndAwait(
                                actionType,
                                actionName,
                                actionData,
                                shouldRetry,
                                true,
                            ),
                        );
                } else {
                    throw this.#reportError(error);
                }
            }

            if (!promise) {
                throw this.#reportError(
                    error instanceof Error ? error : new Error('Unknown error'),
                );
            }

            return promise;
        });
    }

    #findPendingDuplicate({
        action_id: _,
        ...message
    }: Partial<ActionLike>): ActionLike | undefined {
        return this.#pendingActions.find(({ action_id: _, ...pending }) =>
            structuralEqual(pending, message),
        );
    }

    #prepare(message: Omit<ActionLike, 'action_id'>, isRetry = false): ActionLike | null {
        if (!this.isReady) {
            if (!isRetry) {
                this.#reportError(new Error('Not ready'));
            }
            return null;
        }

        if (!this.#areConcurrentActionsAllowed && this.#pendingActions.length > 0) {
            if (!isRetry) {
                const pending = this.#findPendingDuplicate(message);
                if (pending) {
                    return pending;
                }
                this.#reportError(new Error('An action is already in progress'));
            }
            return null;
        }

        // TODO: validate message object
        const action: ActionLike = {
            ...message,
            action_id: uuid(),
        };

        if (!this.isActionAllowed(action)) {
            if (!isRetry) {
                this.#reportError(
                    new Error(`Action '${action.action}' is not allowed in the current state`),
                );
            }

            return null;
        }

        return action;
    }

    #send(action: ActionLike): Promise<boolean> {
        if (this.#findPendingDuplicate(action) !== undefined) {
            return Promise.resolve(true);
        }

        const { signal: abortSignal } = new AbortController();
        for (const _ of this.fireEventGenerator('beforeSend', action, { abortSignal })) {
            if (abortSignal.aborted) {
                return Promise.resolve(false);
            }
        }

        this.#pendingActions.push(action);
        this.#sentActionIds.add(action.action_id);

        return Promise.resolve().then(() => {
            this.isIdle = false;

            if (!this.#transport.canSend(action) || !this.#transport.send(action)) {
                const index = this.#pendingActions.lastIndexOf(action);
                if (index >= 0) {
                    this.#pendingActions.splice(index, 1);
                }

                this.#sentActionIds.delete(action.action_id);

                this.signalIdle();

                throw new GameError(
                    `Action '${action.action}' could not be sent`,
                    'REQUEST_FAILED',
                    action.action_id,
                    action,
                );
            }

            if (this.#actionTimeout > 0) {
                this.#addActionTimeout(action.action_id, this.#actionTimeout);
            }

            return true;
        });
    }

    /**
     * Wait for a response to a specific action id.
     *
     * If `options.timeout` is provided, the promise will reject if the action
     * does not resolve within the specified time in milliseconds.
     *
     * @param action_id - The id of action response to wait for.
     * @param options - The subscription options.
     * @returns A promise that resolves with the action response.
     */
    public async response<Action extends MessageName>(
        action_id: string,
        {
            timeout,
        }: {
            /**
             * The timeout in milliseconds.
             */
            readonly timeout?: number;
        } = {},
    ): Promise<ResponseData<Action>> {
        let awaiting = this.#subscriptions.get(action_id);
        if (!awaiting) {
            awaiting = [];
            this.#subscriptions.set(action_id, awaiting);
        }

        if (timeout && timeout > 0) {
            this.#addActionTimeout(action_id, timeout);
        }

        return new Promise((resolve, reject) => {
            awaiting.push({ resolve, reject });
        });
    }

    #addActionTimeout(action_id: string, timeout: number): void {
        let timeouts = this.#timeoutTimers.get(action_id);
        if (!timeouts) {
            timeouts = [];
            this.#timeoutTimers.set(action_id, timeouts);
        }
        timeouts.push(
            setTimeout(() => {
                this.#handleMessage({
                    type: 'Error',
                    message: `Action '${action_id}' timed out after ${timeout}ms`,
                    code: 'TIMEOUT',
                    action_id,
                });
            }, timeout),
        );
    }

    protected resetAllowedActions(): void {
        this.allowedActions.clear();
        if (this.isAuthenticated || !this.#requireAuthentication) {
            for (const action of this.roundStartActions) {
                this.allowedActions.add(action);
            }
        } else {
            this.allowedActions.add('auth');
        }
    }

    #clearPendingActions(): void {
        this.#pendingActions.length = 0;
        this.#history.length = 0;
    }

    public reset(): void {
        this.resetAllowedActions();
    }

    public close(): void {
        this.#isClosed = true;
        this.#readyState = 'closed';

        this.reset();

        this.#subscriptions.clear();
        this.#clearPendingActions();

        for (const timeouts of this.#timeoutTimers.values()) {
            for (const timeout of timeouts) {
                clearTimeout(timeout);
            }
        }

        this.#timeoutTimers.clear();

        this.#eventHandlers.clear();

        this.#transport.close();
    }

    protected signalRoundEnd(): void {
        void Promise.resolve().then(() => {
            this.fireEvent('roundEnd');
        });
    }

    /**
     * Called by the GameUI to signal that it is idle.
     */
    public signalIdle(): void {
        this.isIdle = false;

        if (this.#requireAuthentication && !this.#isAuthenticated) {
            return;
        }

        const { next_actions } = this.state();
        if (
            next_actions.length === 0 ||
            this.roundStartActions.some((action) => next_actions.includes(action))
        ) {
            this.isIdle = true;

            if (!this.#trySwitchGameSession()) {
                this.fireEvent('idle');
            }
        }
    }

    #newSessionState: null | {
        snapshot: StoreSnapshot;
        currency: string;
        usdRate: number;
    } = null;

    signalSessionStart(): void {
        if (!this.#newSessionState) {
            return;
        }
        const { store } = this.root;
        const { snapshot, currency, usdRate } = this.#newSessionState;

        this.#newSessionState = null;
        this.#sentActionIds.clear();

        this.fireEvent('afterGameSessionSwitch', { currency });
        this.signalRoundEnd();

        store.restoreSnapshot(snapshot);
        store.resume();

        void Promise.resolve().then(() => {
            this.#session.update((session) => ({ ...session, currency, usdRate }));
        });
    }

    #trySwitchGameSession(): boolean {
        if (!this.isIdle) {
            return false;
        }

        const intent = this.getIntent('SwitchGameSession');
        if (!intent) {
            return false;
        }

        const { currency, gameSessionId } = intent;

        this.api.getUSDRate(currency).then(({ rate: usdRate }) => {
            this.api.gameSessionId = gameSessionId;

            this.removeIntent('SwitchGameSession');
            this.fireEvent('beforeGameSessionSwitch', { currency });

            const { store } = this.root;

            store.pause();

            this.#session.update((session) => ({ ...session, id: gameSessionId }));

            void Promise.resolve().then(() => {
                this.#newSessionState = {
                    snapshot: store.createSnapshot(),
                    currency,
                    usdRate,
                };

                this.#transport
                    .switchGameSession(gameSessionId)
                    .then(() => {
                        this.#eventOrder = undefined;
                    })
                    .catch((error) => {
                        logDevError('Failed to switch game session', error);
                        this.#reportError(
                            new GameError(
                                'Failed to switch game session',
                                'GAME_SESSION_SWITCH_FAILED',
                                '',
                            ),
                        );
                    });
            });
        });

        return true;
    }

    /**
     * Handle an incoming message.
     *
     * Subclasses should override this method to handle incoming messages and
     * update the client state and the allowed actions set.
     *
     * @param _state - The incoming message.
     */
    protected handleUpdate(_state: AnyState): void {}

    #handleOutOfSyncMessages = (data: EventsOutOfSync) => {
        data.filter(({ event_order }) => event_order > (this.#eventOrder ?? 0))
            .sort(({ event_order: a }, { event_order: b }) => a - b)
            .forEach(({ payload }) => {
                this.#eventOrder = payload.eventOrder;
                if (payload.type == 'GameUpdate' && this.#isActionSyncable(payload.action)) {
                    this.#messageQueue.push({ message: payload, sync: true });
                }
            });
    };

    #processMessage = (message: StateLike | ErrorMessage) => {
        if (this.#messageQueue.isEmpty) {
            void this.#handleMessage(message);
        } else {
            this.#messageQueue.push({ message, sync: false });
        }
    };

    #handleMessage = (message: StateLike | ErrorMessage, sync: boolean = false) => {
        let action: ActionLike | undefined;

        if (!message.action_id && message.type === 'GameState') {
            (message as { action_id: string | undefined }).action_id = this.#pendingActions.find(
                (a) => a.type === 'GameState',
            )?.action_id;
        }

        if (message.action_id) {
            if (!sync && !this.#sentActionIds.has(message.action_id)) {
                void this.reload().catch(logDevError);
                return;
            }

            const index = this.#pendingActions.findIndex((a) => a.action_id === message.action_id);
            if (index >= 0) {
                action = this.#pendingActions[index];
                this.#pendingActions.splice(index, 1);
            }
        }

        const awaiting = this.#subscriptions.get(message.action_id) ?? [];
        this.#subscriptions.delete(message.action_id);

        for (const timeout of this.#timeoutTimers.get(message.action_id) ?? []) {
            clearTimeout(timeout);
        }

        const lastMessage = this.#history.at(-1);
        if (message.action_id && message.action_id === lastMessage?.action_id) {
            return;
        }

        if (message.type === 'Error') {
            const error = message as ErrorMessage;
            const gameError = new GameError(error.message, error.code, error.action_id, action);

            const isDeauthenticated = error.code === 'CLIENT_NOT_AUTHENTICATED';

            if (error.code === 'EVENT_ORDER_OUT_OF_SYNC') {
                this.fireEvent('afterHandle', gameError);
                return this.#handleOutOfSyncMessages(error.eventsOutOfSync!);
            }

            if (error.code === 'GAME_ROUND_NOT_FOUND') {
                this.#eventOrder = message.eventOrder;
            }

            if (isDeauthenticated) {
                this.#isAuthenticated = false;
                this.resetAllowedActions();
            }

            for (const { reject } of awaiting) {
                reject(gameError);
            }

            if (isDeauthenticated) {
                this.authenticate().catch(logDevError);
            }

            this.fireEvent('afterHandle', gameError);
            return;
        }

        const state = message as State; // TODO: validate message object

        this.#history.push(state);

        if (isAuthenticate(state)) {
            this.#isAuthenticated = state.data.authenticated;
            this.allowedActions.clear();
            if (this.#isAuthenticated) {
                this.reload()
                    .catch(() => {
                        // ignore
                    })
                    .then(() => {
                        this.signalIdle();
                    });
            } else {
                this.allowedActions.add('auth');
                this.#reportError(
                    new GameError('Authentication failed', 'AUTH_FAILED', state.action_id),
                );
            }
        } else if (
            this.roundStartActions.some((action) => isState(state, action)) ||
            isGameState(state)
        ) {
            this.#isAuthenticated = true;
        }

        if (!sync && message.eventOrder) {
            this.#eventOrder = message.eventOrder;
        }
        this.handleUpdate(state as AnyState);

        for (const { resolve } of awaiting) {
            resolve(state.data);
        }

        this.fireEvent('afterHandle', state);
    };

    #handleReadyStateChanged = () => {
        if (this.#isClosed) {
            return;
        }

        const previousReadyState = this.#readyState;
        this.#readyState = this.#transport.readyState;

        if (this.#readyState === previousReadyState) {
            return;
        }

        if (this.#readyState === 'closed') {
            this.#isAuthenticated = false;
            this.resetAllowedActions();
        }

        this.fireEvent('readyStateChanged', this.#readyState);
    };
}
