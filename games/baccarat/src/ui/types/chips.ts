import { writable } from '@monkey-tilt/client';
import { ChipValues as AllChipValues } from '@monkey-tilt/ui';

export const ChipValues = AllChipValues.filter((value) => value !== '10M' && value !== '100M');
export type ChipValue = (typeof ChipValues)[number];

export type ChipStacks = { readonly [key in ChipValue]?: number };

export function emptyStack(): ChipStacks {
    return {};
}

export function addChip(stacks: ChipStacks, value: ChipValue): ChipStacks {
    return { ...stacks, [value]: (stacks[value] ?? 0) + 1 };
}

const multipliers: Record<ChipValue, number> = {
    '1': 1,
    '10': 10,
    '100': 25,
    '1K': 50,
    '10K': 100,
    '100K': 250,
    '1M': 1000,
};

const denominations: [denomination: ChipValue, value: number][] = ChipValues.map(
    (value) => [value, multipliers[value]] as [ChipValue, number],
).sort(([, a], [, b]) => b - a);

const UNIT_VALUE = 1;

export const MIN_CHIP_VALUE = chipValue(denominations.at(-1)![0]);

export function clampChipSum(sum: number): number {
    return Math.max(0, sum - (sum % MIN_CHIP_VALUE));
}

export function breakIntoChips(amount: number, base = UNIT_VALUE): ChipStacks {
    const stacks = writable({} as ChipStacks);

    let remaining = Math.round(amount / base);

    for (const [denomination, value] of denominations) {
        if (remaining >= value) {
            const count = Math.floor(remaining / value);
            stacks[denomination] = count;
            remaining -= count * value;
        }
    }

    return stacks;
}

export function chipValue(value: ChipValue, base = UNIT_VALUE): number {
    return multipliers[value] * base;
}

export function chipText(denomination: ChipValue): string {
    const value = chipValue(denomination);
    return value >= 1000 ? `${value / 1000}K` : value.toString();
}

export function sumChips(stacks: ChipStacks, base = UNIT_VALUE): number {
    const sum = (Object.entries(stacks) as [ChipValue, number][]).reduce(
        (sum, [value, count]) => sum + multipliers[value] * count,
        0,
    );
    return base * sum;
}
