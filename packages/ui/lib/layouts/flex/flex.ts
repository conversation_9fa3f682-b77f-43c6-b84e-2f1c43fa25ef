import { defineComponent, html, type Component, type Props } from '../../component';
import { mergeClasses, type CSSPropValue } from '../../utilities';
import type { Size } from '../types';

export interface FlexProps extends Props<HTMLDivElement> {
    readonly gap?: Size | undefined;
    readonly threshold?: CSSPropValue | undefined;
    readonly reverse?: boolean | undefined;
    readonly wrap?: boolean | 'text-only' | undefined;
    readonly justify?:
        | undefined
        | 'start'
        | 'end'
        | 'center'
        | 'space-between'
        | 'space-around'
        | 'space-evenly'
        | 'stretch';
}

export const Flex = defineComponent(
    'Flex',
    () =>
        ({
            gap,
            threshold,
            reverse = false,
            wrap = 'text-only',
            justify,
            ...props
        }: FlexProps): Component<HTMLDivElement> =>
            html('div')({
                ...props,
                css: {
                    ...(props.css ?? {}),
                    ...(justify ? { 'justify-content': justify } : {}),
                },
                cssProps: {
                    threshold,
                    'flex-gap': gap && { var: `size-${gap}` },
                    ...(props.cssProps ?? {}),
                },
                className: mergeClasses(props.className, {
                    'l-flex': true,
                    'l-flex--reverse': reverse,
                    'l-flex--wrap': wrap === true,
                    'l-flex--nowrap': wrap === false,
                }),
            }),
);
