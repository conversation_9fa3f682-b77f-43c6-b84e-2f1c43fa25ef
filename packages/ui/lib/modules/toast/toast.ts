import {
    defineComponent,
    forwardRef,
    html,
    render,
    type Component,
    type Props,
    type RefObject,
    type Renderable,
} from '../../component';
import { mergeClasses, withNamespace } from '../../utilities/classes';
import { Icon } from '../icon/icon';
import { ToastBar } from './bar';

export type { ToastBarControls, ToastBarProps } from './bar';

export interface ToastControls {
    /**
     * Set the content of the toast.
     *
     * @param content - The new content of the toast.
     * @param label - The optional label to render below content.
     */
    setContent(this: void, content: Renderable, label?: Renderable): void;

    /**
     * Dismiss the toast.
     */
    dismiss(this: void, options?: { instant?: boolean }): void;
}

export interface ToastProps extends Omit<Props<HTMLDivElement>, 'title'> {
    readonly content: Renderable;
    readonly label?: Renderable;
    readonly variant?: 'default' | 'error' | 'win' | 'lose' | 'tie';
    readonly isImportant?: boolean;
    readonly isDismissable?: boolean;
    readonly dismiss?: 'click' | { after: number } | undefined;
    readonly onDismiss?: () => void;
    readonly controls?: (controls: ToastControls) => void;
}

const toast = defineComponent(
    'Toast',
    ({ root }) =>
        ({
            content,
            label,
            variant = 'default',
            isImportant = typeof content === 'string' && /error|fail/i.test(content),
            isDismissable = variant !== 'win' && variant !== 'lose' && variant !== 'tie',
            dismiss: dismissStrategy,
            onDismiss,
            controls,
            ...props
        }: ToastProps): Component<HTMLDivElement> => {
            const div = html('div');

            const ref: RefObject<HTMLDivElement> = { current: null };
            const contentRef: RefObject<HTMLDivElement> = { current: null };

            function dismiss(this: void, { instant = false } = {}): void {
                if (ref.current) {
                    if (instant) {
                        ref.current.remove();
                        if (onDismiss) {
                            onDismiss();
                        }
                    } else {
                        ref.current.addEventListener(
                            'transitionend',
                            () => {
                                ref.current!.remove();
                                if (onDismiss) {
                                    onDismiss();
                                }
                            },
                            { once: true },
                        );

                        ref.current.classList.add(withNamespace('is-dismissed'));
                    }
                }
            }

            if (controls) {
                controls({
                    setContent(content: Renderable, label?: Renderable) {
                        if (contentRef.current) {
                            let node: Node | null;

                            if (label) {
                                node = render(
                                    div(
                                        { className: { 'm-toast__text': true } },
                                        content,
                                        div({ className: { 'm-toast__label': true } }, label),
                                    ),
                                    root,
                                );
                            } else {
                                node = render(content, root);
                            }

                            if (node) {
                                contentRef.current.replaceChildren(node);
                            } else {
                                contentRef.current.innerHTML = '';
                            }
                        }
                    },
                    dismiss,
                });
            }

            return div(
                {
                    ...props,
                    role: 'alert',
                    ariaAtomic: 'true',
                    ariaLive: isImportant ? 'assertive' : 'polite',
                    ref: forwardRef(props.ref, ref, (element: HTMLDivElement) => {
                        if (dismissStrategy === 'click' || props.onClick) {
                            element.style.setProperty('cursor', 'pointer');
                        }
                        if (typeof dismissStrategy === 'object') {
                            setTimeout(dismiss, dismissStrategy.after);
                        }
                    }),
                    className: mergeClasses(props.className, {
                        'm-toast': true,
                        [`m-toast--${variant}`]: variant !== 'default',
                        'm-toast--status':
                            variant === 'win' || variant === 'lose' || variant === 'tie',
                    }),
                    onClick:
                        dismissStrategy === 'click'
                            ? (e) => {
                                  if (props.onClick) {
                                      props.onClick(e);
                                  }
                                  dismiss();
                              }
                            : props.onClick,
                },
                div(
                    { ref: contentRef, className: { 'm-toast__content': true } },
                    variant === 'error' && Icon({ name: 'error' }),
                    label
                        ? div(
                              { className: { 'm-toast__text': true } },
                              content,
                              div({ className: { 'm-toast__label': true } }, label),
                          )
                        : content,
                ),
                isDismissable &&
                    html('button')(
                        {
                            className: { 'm-toast__dismiss': true },
                            ariaLabel: 'Dismiss',
                            onClick: () => dismiss(),
                        },
                        Icon({ name: 'x' }),
                    ),
            );
        },
);

export const Toast = Object.assign(toast, { Bar: ToastBar });
