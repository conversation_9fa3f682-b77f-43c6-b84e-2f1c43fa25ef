import fs from 'node:fs';
import path from 'node:path';
import { optimize } from 'svgo';

const inputDir = process.argv[2];

if (!inputDir) {
    console.error('Usage: node svg-spite.js <input directory>');
    process.exit(1);
}

const outputFilename = path.join(path.dirname(inputDir), `${path.basename(inputDir)}.svg`);
const sprites = [];

let totalWidth = 0;

for (const file of fs.readdirSync(inputDir, { withFileTypes: true })) {
    if (!file.isFile() || !file.name.toLowerCase().endsWith('.svg')) {
        continue;
    }

    const svg = fs.readFileSync(path.join(inputDir, file.name), 'utf8');
    const viewBox = svg.match(/viewBox="([^"]*)"/)[1].split(' ');

    const width = Number.parseFloat(viewBox[2]);
    const height = Number.parseFloat(viewBox[3]);

    totalWidth += width;

    sprites.push({
        id: path.basename(file.name, path.extname(file.name)),
        width,
        height,
        content: svg.match(/<svg[^>]*>([\s\S]*?)<\/svg>/)[1],
    });
}

sprites.sort((a, b) => a.height - b.height);

const maxWidth = sprites[Math.floor(sprites.length * 0.65)].width * 4;

let sheetWidth = 0;
let sheetHeight = 0;
let y = 0;
let rowHeight = 0;
let rowWidth = 0;
let currentRow = [];
const sheet = [];

for (const sprite of sprites) {
    if (
        rowWidth + sprite.width > maxWidth ||
        (currentRow.length > 0 && sprite.height / currentRow[0].height > 5)
    ) {
        sheet.push(currentRow);
        sheetWidth = Math.max(sheetWidth, rowWidth);

        y += rowHeight;
        rowHeight = 0;
        rowWidth = 0;
        currentRow = [];
    }

    sprite.x = rowWidth;
    sprite.y = y;

    rowWidth += sprite.width;
    rowHeight = Math.max(rowHeight, sprite.height);

    currentRow.push(sprite);
}

sheet.push(currentRow);
sheetWidth = Math.max(sheetWidth, rowWidth);
sheetHeight = y + rowHeight;

let svgContent =
    '<?xml version="1.0" encoding="utf-8"?>' +
    `<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 ${sheetWidth} ${sheetHeight}">`;

for (const row of sheet) {
    for (const sprite of row) {
        const viewBox = `${sprite.x} ${sprite.y} ${sprite.width} ${sprite.height}`;
        svgContent +=
            `<view id="${sprite.id}" viewBox="${viewBox}"/>` +
            `<g transform="translate(${sprite.x}, ${sprite.y})">${sprite.content}</g>`;
    }
}

svgContent += '</svg>';

fs.writeFileSync(
    outputFilename,
    optimize(svgContent, {
        path: outputFilename,
        multipass: true,
        plugins: [
            {
                name: 'preset-default',
                params: {
                    overrides: {
                        removeViewBox: false,
                        cleanupIds: false,
                    },
                },
            },
        ],
    }).data,
    'utf-8',
);
