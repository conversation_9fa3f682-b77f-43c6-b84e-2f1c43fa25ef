import { roundHalfEven } from './number';

const knownCurrencies = new Set(
    Intl.supportedValuesOf('currency').map((code) => code.toUpperCase()),
);

const formatterCache = new Map<string, { prefix: string; formatter: Intl.NumberFormat }>();

/**
 * Formats a number as a currency string, using the specified locale and options.
 *
 * @param value The number to format.
 * @param currencyCode The currency code to use for formatting.
 * @param options Additional options for formatting.
 * @returns The formatted currency string.
 */
export function formatCurrency(
    value: number,
    currencyCode: string,
    {
        locale = 'en-US',
        factor = 1,
        currencyDisplay = 'symbol',
        skipCache = currencyDisplay === 'none',
        useGrouping = true,
        roundingMode = 'halfEven',
        truncate = false,
        ...options
    }: Omit<Intl.NumberFormatOptions, 'currencyDisplay'> & {
        currencyDisplay?: Intl.NumberFormatOptionsCurrencyDisplay | 'none';
        locale?: string;
        factor?: number;
        skipCache?: boolean;
        truncate?: boolean | 'auto' | 'zero';
    } = {},
): string {
    const currency = currencyCode.toUpperCase();
    let { formatter, prefix = '' } = formatterCache.get(currency) ?? {};

    if (skipCache || !formatter) {
        const styleOptions: Intl.NumberFormatOptions = {};

        if (currencyDisplay !== 'none' && knownCurrencies.has(currency)) {
            Object.assign(styleOptions, {
                style: 'currency',
                currency,
                currencyDisplay,
            });
        } else {
            const minimumFractionDigits = precisionForCurrency(currency);
            Object.assign(styleOptions, {
                style: 'decimal',
                minimumFractionDigits,
                maximumFractionDigits: minimumFractionDigits,
            });
            prefix = currencyDisplay !== 'none' ? currency + ' ' : '';
        }

        formatter = new Intl.NumberFormat([locale, 'en'], {
            ...options,
            ...styleOptions,
            roundingMode,
            useGrouping,
        });

        if (!skipCache) {
            formatterCache.set(currency, { formatter, prefix });
        }
    }

    const amount = value * factor;
    const formatted = prefix + formatter.format(amount);

    if (truncate === true || (truncate === 'zero' && amount === 0)) {
        return formatted.replace(/\.\d+$/, '');
    }

    if (truncate === 'auto') {
        return formatted.replace(/\.0+$/, '');
    }

    return formatted;
}

// only contains currencies whose precision is not 2:
const currencyPrecision: Record<string, number> = Object.assign(Object.create(null), {
    AVAX: 6,
    BNB: 6,
    BTC: 8,
    ETH: 8,
    LTC: 8,
    SOL: 6,
    USDC: 8,
    XRP: 8,
    // fiat currencies:
    BHD: 3,
    BIF: 0,
    CLF: 4,
    CLP: 0,
    GNF: 0,
    IQD: 3,
    ISK: 0,
    JOD: 3,
    JPY: 0,
    KMF: 0,
    KRW: 0,
    KWD: 3,
    LYD: 3,
    OMR: 3,
    PYG: 0,
    TND: 3,
    UGX: 0,
    UYI: 0,
    UYW: 4,
    VND: 0,
    VUV: 0,
    XAF: 0,
    XOF: 0,
    XPF: 0,
});

/**
 * Retrieves the precision for a given currency code.
 *
 * @param currency The currency code (e.g., 'USD', 'EUR').
 * @returns The number of decimal places for the currency.
 */
export function precisionForCurrency(currency: string): number {
    return currencyPrecision[currency.toUpperCase()] ?? 2;
}

/**
 * Rounds a currency value, using halfEven rounding mode, to the appropriate
 * precision based on the currency code.
 *
 * @param value - The value to round.
 * @param currencyCode - The currency code (e.g., 'USD', 'EUR').
 */
export function roundCurrency(value: number, currencyCode: string): number {
    return roundHalfEven(value, precisionForCurrency(currencyCode));
}

/**
 * Formats a number as a currency string, rounding it to the appropriate precision
 * based on the currency code. No currency symbol/code or digit grouping is added.
 *
 * @param value - The value to format.
 * @param currencyCode - The currency code (e.g., 'USD', 'EUR').
 */
export function currencyToFixedString(value: number, currencyCode: string): string {
    const precision = precisionForCurrency(currencyCode);
    return roundHalfEven(value, precision).toFixed(precision);
}

const symbolCache = new Map<string, string>();

/**
 * Retrieves the currency symbol for a given currency code. If the symbol is not
 * found, the currency code is returned instead.
 *
 * @param currencyCode - The currency code (e.g., 'USD', 'EUR', BTC).
 * @returns The currency symbol as a string.
 */
export function getCurrencySymbol(currencyCode: string, locale = 'en-US'): string {
    const currency = currencyCode.toUpperCase();

    if (symbolCache.has(currency)) {
        return symbolCache.get(currency)!;
    }

    if (!knownCurrencies.has(currency)) {
        symbolCache.set(currency, currency);
        return currency;
    }

    const symbol =
        new Intl.NumberFormat([locale, 'en'], {
            style: 'currency',
            currency,
            currencyDisplay: 'symbol',
        })
            .formatToParts(1)
            .find(({ type }) => {
                return type === 'currency';
            })?.value ?? currency;

    symbolCache.set(currency, symbol);

    return symbol;
}
