import { defineComponent, html, type Props } from '../../component';
import type { Reactive } from '../../state';
import { mergeClasses, withNamespace } from '../../utilities/classes';

export interface StatsItem {
    /**
     * The label to display next to the stat value.
     */
    label: string;

    /**
     * The reactive value of the stat.
     */
    value: Reactive<string | number>;

    /**
     * Optional formatter to transform the value before rendering.
     */
    format?: (val: string | number) => string;
}

export interface StatsProps extends Props<HTMLDivElement> {
    stats: StatsItem[];
}

export const Stats = defineComponent(
    'Stats',
    ({ state }) =>
        ({ stats, className, ...props }: StatsProps): HTMLElement => {
            const { onPropChange } = state();

            const renderStatItem = (
                label: string,
                signal: Reactive<string | number>,
                format?: (val: string | number) => string,
            ) =>
                html('div')({
                    className: withNamespace('m-stats__item'),
                    children: [
                        html('span')({
                            className: withNamespace('m-stats__label'),
                            textContent: label,
                        }),
                        html('span')({
                            className: withNamespace('m-stats__value'),
                            ref: (el) => {
                                const resolve = (v: string | number) =>
                                    format ? format(v) : String(v);

                                const initial =
                                    typeof signal === 'function'
                                        ? signal()
                                        : typeof signal === 'object' && 'read' in signal
                                          ? signal.read()
                                          : signal;

                                el.textContent = resolve(initial);

                                onPropChange(signal, (val) => {
                                    el.textContent = resolve(val);
                                });
                            },
                        }),
                    ],
                });

            return html('div')({
                ...props,
                className: mergeClasses(withNamespace('m-stats'), className),
                role: 'presentation',
                children: [
                    ...stats.map(({ label, value, format }) =>
                        renderStatItem(label, value, format),
                    ),
                ],
            }).render();
        },
);
