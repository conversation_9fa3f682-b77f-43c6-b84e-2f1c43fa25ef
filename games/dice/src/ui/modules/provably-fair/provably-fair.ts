import {
    <PERSON>ton,
    Center,
    defineComponent,
    Die,
    Frame,
    Label,
    Modal,
    Stack,
    Tabs,
    withNamespace,
    type Component,
    type ModalControls,
    type ModalProps,
    type TabControls,
} from '@monkey-tilt/ui';
import type { FinalState } from '../../../game/state';
import type { DiceUI } from '../../dice';
import { TextInput } from '../text-input/text-input';

export interface ProvablyFairProps extends ModalProps {
    /**
     * The Dice UI state manager.
     */
    readonly ui: DiceUI;
}

export const ProvablyFair = defineComponent(
    'ProvablyFair',
    ({ state }) =>
        ({ ui, ...props }: ProvablyFairProps): Component<HTMLDialogElement> => {
            const { signal, effect } = state();

            const [activeTab, setActiveTab] = signal<'seeds' | 'verify'>('seeds');

            const [rotateSeedError, setRotateSeedError] = signal('');

            const verifyClientSeed = signal('');
            const verifyServerSeed = signal('');
            const verifyNonce = signal('');
            const [verifyError, setVerifyError] = signal('');

            const [finalState, setFinalState] = signal<FinalState | null>(null);

            effect(() => {
                const client_seed = verifyClientSeed.read();
                const server_seed = verifyServerSeed.read();
                const nonce = Number(verifyNonce.read());

                if (!Number.isFinite(nonce)) {
                    setVerifyError('Nonce must be a number');
                    return;
                }

                setVerifyError('');

                if (client_seed && server_seed && nonce) {
                    ui.verifySeeds({ client_seed, server_seed, nonce })
                        .then(setFinalState)
                        .catch((error) => {
                            setVerifyError(
                                error instanceof Error ? error.message : 'Failed to verify seeds',
                            );
                        });
                }
            });

            return Modal(
                {
                    title: 'Provably Fair',
                    controls: (controls: ModalControls) => {
                        ui.provablyFairModalControlsRef.current = controls;
                    },
                    ...props,
                },
                Tabs({
                    value: activeTab(),
                    variant: 'subtle',
                    tabs: [
                        { label: 'Seeds', value: 'seeds' },
                        { label: 'Verify', value: 'verify' },
                    ],
                    onChange(value: 'seeds' | 'verify') {
                        setActiveTab(value);
                    },
                    controls({ selectTab }: TabControls) {
                        effect(() => {
                            selectTab(activeTab());
                        });
                    },
                }),
                Stack(
                    {
                        gap: 'l',
                        ref(element: HTMLDivElement) {
                            effect(() => {
                                element.classList.toggle(
                                    withNamespace('u-hide'),
                                    activeTab() !== 'seeds',
                                );
                            });
                        },
                    },
                    TextInput({
                        label: 'Active Client Seed',
                        value: ui.seeds.activeClientSeed,
                        readOnly: true,
                        withCopy: true,
                    }),
                    TextInput({
                        label: 'Active Server Seed (hashed)',
                        value: ui.seeds.activeServerSeed,
                        readOnly: true,
                        withCopy: true,
                    }),
                    Label({ variant: 'medium' }, 'Rotate Seed Pair'),
                    TextInput({
                        label: 'New Client Seed*',
                        value: ui.seeds.newClientSeed,
                        error: rotateSeedError,
                        onChange() {
                            setRotateSeedError('');
                        },
                        button: Button(
                            {
                                variant: 'accent',
                                onClick(e: MouseEvent) {
                                    if (!(e.target instanceof HTMLButtonElement)) {
                                        return;
                                    }
                                    const button = e.target;
                                    button.disabled = true;

                                    setRotateSeedError('');

                                    ui.rotateSeedPair()
                                        .catch((error) => {
                                            setRotateSeedError(
                                                error instanceof Error
                                                    ? error.message
                                                    : 'Failed to change client seed',
                                            );
                                        })
                                        .finally(() => {
                                            button.disabled = false;
                                        });
                                },
                            },
                            'Change',
                        ),
                    }),
                    TextInput({
                        label: 'New Server Seed',
                        value: ui.seeds.newServerSeed,
                        readOnly: true,
                        withCopy: true,
                    }),
                ),
                Stack(
                    {
                        gap: 'l',
                        ref(element: HTMLDivElement) {
                            effect(() => {
                                element.classList.toggle(
                                    withNamespace('u-hide'),
                                    activeTab() === 'seeds',
                                );
                            });
                        },
                    },
                    Frame({
                        variant: 'dashed',
                        css: { 'min-block-size': '35vh' },
                        cssProps: { 'card-size': '3rem' },
                        ref: (element: HTMLDivElement) => {
                            effect(() => {
                                element.innerHTML = '';

                                const state = finalState();

                                if (!state) {
                                    return;
                                }

                                Center(
                                    { gap: 'l' },
                                    Die({
                                        number: state.random_dice_roll,
                                    }),
                                ).renderTo(element);
                            });
                        },
                    }),
                    TextInput({
                        label: 'Client Seed',
                        value: verifyClientSeed,
                        placeholder: 'Enter Client Seed',
                    }),
                    TextInput({
                        label: 'Server Seed',
                        value: verifyServerSeed,
                        placeholder: 'Enter Server Seed',
                    }),
                    TextInput({
                        label: 'Nonce',
                        value: verifyNonce,
                        error: verifyError,
                        placeholder: 'Enter Nonce',
                        inputMode: 'numeric',
                    }),
                ),
            );
        },
);
