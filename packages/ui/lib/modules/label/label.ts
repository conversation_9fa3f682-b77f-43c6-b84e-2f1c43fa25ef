import {
    create,
    defineComponent,
    forwardRef,
    html,
    render as renderContent,
    type Props,
    type RefObject,
    type Renderable,
} from '../../component';
import type { Reactive } from '../../state';
import { mergeClasses, withNamespace } from '../../utilities/classes';
import { Icon, type IconName } from '../icon/icon';
import { Tooltip, type TooltipControls } from '../tooltip/tooltip';
import { LabelGroup, type LabelGroupProps } from './group';

export type { LabelGroupProps };

export interface LabelControls {
    setText(this: void, text: Renderable): void;
    setTooltip(this: void, tooltip: Renderable): void;
    ref(this: void): HTMLElement | null;
}

export interface LabelProps extends Props<HTMLLabelElement & HTMLSpanElement> {
    /**
     * The label text.
     *
     * Ignored if `children` is provided.
     */
    readonly text?: Reactive<Renderable>;

    /**
     * The optional help text, renders as an icon with a tooltip.
     */
    readonly tooltip?: Reactive<Renderable>;

    /**
     * The optional icon. If provided, the icon will be rendered before the text.
     */
    readonly icon?: IconName | undefined;

    /**
     * The optional icon description, used for accessibility.
     */
    readonly iconDescription?: string;

    /**
     * The label variant.
     */
    readonly variant?:
        | 'default'
        | 'light'
        | 'medium'
        | 'big'
        | 'box'
        | 'small-box'
        | 'subtle'
        | 'caption'
        | 'ribbon-l'
        | 'ribbon-r'
        | 'error'
        | 'bubble'
        | 'highlight';

    /**
     * Callback to receive the label controls.
     */
    controls?: (controls: LabelControls) => void;
}

const label = defineComponent(
    'Label',
    ({ root, render, state }) =>
        ({
            text,
            tooltip,
            icon,
            htmlFor,
            iconDescription = '',
            variant = 'default',
            children = [],
            controls,
            ...props
        }: LabelProps): HTMLLabelElement | HTMLSpanElement => {
            const { onPropChange } = state();

            const ref: RefObject = { current: null };
            const tooltipIconRef: RefObject = { current: null };
            const tooltipControlsRef: RefObject<TooltipControls> = { current: null };

            const hideClassName = withNamespace('u-hide');

            const setTooltip = (tooltip: Renderable) => {
                if (tooltipIconRef.current) {
                    tooltipIconRef.current.classList.toggle(hideClassName, !tooltip);
                }
                if (!tooltipControlsRef.current) {
                    if (!tooltip) {
                        return;
                    }
                    render(
                        Tooltip({
                            target: tooltipIconRef,
                            controls(controls) {
                                tooltipControlsRef.current = controls;
                            },
                        }),
                    );
                }

                tooltipControlsRef.current!.setContent(tooltip);
            };

            const setText = (text: Renderable) => {
                if (ref.current) {
                    ref.current.innerHTML = '';
                    const content = renderContent(text, root);
                    if (content) {
                        ref.current.appendChild(content);
                    }
                }
            };

            return create(
                htmlFor ? 'label' : 'span',
                {
                    props: {
                        ...props,
                        ref: forwardRef(props.ref, (element: HTMLElement) => {
                            if (controls) {
                                controls({
                                    setText,
                                    setTooltip,
                                    ref: () => element,
                                });
                            }
                        }),
                        htmlFor,
                        className: mergeClasses(props.className, {
                            'm-label': true,
                            'm-label--action': props.onClick != null,
                            [`m-label--${variant}`]: variant !== 'default',
                        }),
                    },
                },
                icon && Icon({ name: icon, description: iconDescription }),
                ...(children.length > 0
                    ? children
                    : [
                          (text || controls) &&
                              html('span')({
                                  ref: forwardRef(ref, () => {
                                      onPropChange(text, setText);
                                  }),
                              }),
                      ]),
                Icon({
                    name: 'ask',
                    ref: forwardRef(tooltipIconRef, () => {
                        onPropChange(tooltip, setTooltip);
                    }),
                    className: {
                        'm-label__tooltip': true,
                        'u-hide': true,
                    },
                }),
            );
        },
);

export const Label: typeof label & {
    readonly Group: typeof LabelGroup;
} = Object.assign(label, {
    Group: LabelGroup,
});
