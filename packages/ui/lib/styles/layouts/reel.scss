@use '../common/bem';
@use '../common/prop';

///
/// The Reel Layout primitive can be used to layout a series of elements in the inline flow direction.
/// When the elements overflow the container, the Reel Layout will provide a scrollbar to allow the user
/// to scroll through the elements.
///
///
///                              Reel Layout
///     /----------------------------------------------------------\
///     | +------------+ # +------------+ # +------------+ # +-----|
///     | |            | # |            | # |            | # |     |
///     | |            | # |            | # |            | # |     |
///     | |            | # |            | # |            | # |     |
///     | |            | # |            | # |            | # |     |
///     | +------------+ # +------------+ # +------------+ # +-----|
///     \----------------------------------------------------------/
///
///
/// Configuration:
///     $-color-scrollbar-thumb      The color of the scrollbar thumb.
///     $-color-scrollbar-track      The color of the scrollbar track.
///
/// Variables:
///     --color-scrollbar-thumb     The color of the scrollbar thumb. Set to $-color-scrollbar-thumb by default.
///     --color-scrollbar-track     The color of the scrollbar track. Set to $-color-scrollbar-track by default.
///     --gap                       The gap between the elements. Defaults to var(--size-s).
///     --reel-gap                  The reel-specific gap that overrides the --gap. Unset by default.
///
/// Usage:
///    <div class="l-reel" role="list">
///      <img src="..." role="listitem" />
///      <img src="..." role="listitem" />
///      <img src="..." role="listitem" />
///      <img src="..." role="listitem" />
///      <img src="..." role="listitem" />
///      <img src="..." role="listitem" />
///   </div>
///
/// Block:
///     .l-reel                      The reel layout container.
///
/// Elements:
///     <none>
///
/// Modifiers:
///     .is-overflowing              Set via JavaScript when scrollWidth > clientWidth.
///                                  Adds the space to the container to accommodate the scrollbar.
///                                  This is progressive enhancement.
///

$-color-scrollbar-thumb: prop.get(color-zinc-350);
$-color-scrollbar-track: prop.get(color-zinc-700);

@mixin styles {
    @include bem.layout('reel') {
        display: flex;
        block-size: auto;
        max-inline-size: 100%;
        overflow-x: auto;
        overflow-y: hidden;
        scrollbar-color: prop.get(color-scrollbar-thumb) prop.get(color-scrollbar-track);

        @include prop.set(
            (
                color-scrollbar-thumb: $-color-scrollbar-thumb,
                color-scrollbar-track: $-color-scrollbar-track,
            )
        );

        > * {
            flex: 0 0 auto;
        }

        > :is(img, picture, video) {
            block-size: 100%;
            flex-basis: auto;
            width: auto;
        }

        > * + * {
            margin-inline-start: prop.get(reel-gap, prop.get(gap, prop.get(size-s)));
        }

        &.is-overflowing {
            padding-block-end: prop.get(reel-gap, prop.get(gap, prop.get(size-s)));
        }

        &::-webkit-scrollbar {
            block-size: 1rem;

            &-track {
                background-color: prop.get(color-scrollbar-track);
            }

            &-thumb {
                background-color: prop.get(color-scrollbar-thumb);
                background-image: linear-gradient(
                    prop.get(color-scrollbar-thumb) 0,
                    prop.get(color-scrollbar-thumb) 0.25rem,
                    prop.get(color-scrollbar-track) 0.25rem,
                    prop.get(color-scrollbar-track) 0.75rem,
                    prop.get(color-scrollbar-thumb) 0.75rem
                );
            }
        }
    }
}
