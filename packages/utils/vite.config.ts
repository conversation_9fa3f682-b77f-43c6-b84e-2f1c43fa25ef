/// <reference types="vitest" />

import { resolve } from 'node:path';
import { defineConfig } from 'vite';
import dts from 'vite-plugin-dts';

export default defineConfig(() => ({
    plugins: [dts({ rollupTypes: true })],
    build: {
        target: 'esnext',
        sourcemap: true,
        lib: {
            entry: resolve(__dirname, 'lib/index.ts'),
            name: 'MT_utils',
            fileName: (format) => `utils.${format}.js`,
        },
    },
    test: {
        reporters: process.env.GITHUB_ACTIONS ? ['dot', 'github-actions'] : ['default'],
    },
}));
