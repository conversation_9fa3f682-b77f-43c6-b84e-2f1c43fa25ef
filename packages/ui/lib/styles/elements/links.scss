@use '../common/prop';
@use '../common/type-style' as *;

@mixin styles {
    a {
        @include type-style(('link', 'body'));

        color: prop.get(link-color, prop.get(color-mt-400));

        &:hover,
        &:focus,
        &:active {
            color: prop.get(link-hover-color, prop.get(color-mt-950));
            text-decoration: underline;
        }

        &[disabled] {
            color: prop.get(link-disabled-color, prop.get(color-zinc-400));
        }
    }

    nav a {
        text-decoration: none;
    }
}
