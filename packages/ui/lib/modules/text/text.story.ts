import type { Meta, StoryObj } from '@storybook/html';
import { html } from '../../component';
import { Text, TypeStyles, type TextProps } from './text';

const meta: Meta = {
    title: 'Modules/Text',
};
export default meta;

export const Default: StoryObj = () => {
    return html('table')(
        null,
        html('tr')(null, html('th')(null, 'Type Style')),
        ...TypeStyles.filter((style) => style !== 'multiplier').map((style) =>
            html('tr')(null, html('td')(null, Text({ style }, style))),
        ),
    ).render();
};
Default.storyName = 'Type Styles';

export const Suffix: StoryObj = ({ content, ...props }: TextProps & { content: string }) => {
    return html('div')({ className: { 'u-text--center': true } }, Text(props, content)).render();
};
Suffix.args = {
    style: 'multiplier',
    suffix: 'x',
    content: '2.00',
};
Suffix.argTypes = {
    content: { control: 'text' },
    suffix: { control: 'text' },
    style: { control: 'select', options: TypeStyles },
};
