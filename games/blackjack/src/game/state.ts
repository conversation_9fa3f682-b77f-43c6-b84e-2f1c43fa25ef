import type {
    BetAmountLimits,
    GameA<PERSON><PERSON>ame,
    GameError,
    ReadyState,
    RequestData,
} from '@monkey-tilt/client';

interface SuitMap {
    Hearts: 0;
    Diamonds: 1;
    Clubs: 2;
    Spades: 3;
}
export type Suit = SuitMap[keyof SuitMap];

interface BaseCardData {
    /**
     * Unique identifier for the card, FE-only
     */
    readonly id: string;

    /**
     * Unique identifier for the hand that the card was split from, FE-only
     */
    readonly splitFrom?: string | undefined;
}

export interface CardInfo {
    readonly value: 1 | 2 | 3 | 4 | 5 | 6 | 7 | 8 | 9 | 10;
    readonly suit: Suit;
    readonly index: 0 | 1 | 2 | 3 | 4 | 5 | 6 | 7 | 8 | 9 | 10 | 11 | 12;
}

export interface FacedownCard extends BaseCardData {
    readonly isFacedown: true;
}

export interface FaceupCard extends BaseCardData, CardInfo {
    readonly isFacedown: false;
}

export type Card = FacedownCard | FaceupCard;

export interface Hand {
    readonly cards: Card[];
    readonly status: 'bust' | 'win' | 'push' | 'playing';

    /**
     * Unique identifier for the hand, FE-only
     */
    readonly id: string;
}

export interface PlayerHand extends Hand {
    readonly splitFrom?: string | undefined;
    readonly bet_amount: string;
    readonly payout?: string;
}

export type HandOwner = 'Dealer' | 'Player';

export interface BlackjackBetAmountLimits extends BetAmountLimits<'perfect_pairs' | 'poker'> {}

export interface BlackjackState {
    readonly balance: number;
    readonly bet_limits: BlackjackBetAmountLimits;

    readonly error: GameError | null;
    readonly isLoading: boolean;
    readonly readyState: ReadyState;
    readonly isAuthenticated: boolean;

    readonly round_id: string;

    readonly dealer_hand: Hand;

    readonly player_hands: PlayerHand[];

    readonly next_actions: ReadonlyArray<GameActionName>;
    readonly hand_owner: HandOwner;
    readonly hand_index: number;

    readonly bet_type?: 'standard' | 'sidebet';
    readonly bet_amount: string;

    readonly perfect_pairs_amount?: string;
    readonly perfect_pairs?:
        | undefined
        | {
              readonly payout: string;
              readonly status: 'perfect_pair' | 'coloured_pair' | 'mixed_pair' | 'sidebet_lost';
          };

    readonly poker_bet_amount?: string;
    readonly poker?:
        | undefined
        | {
              readonly payout: string;
              readonly status:
                  | 'suited_trips'
                  | 'straight_flush'
                  | 'three_of_a_kind'
                  | 'straight'
                  | 'flush'
                  | 'sidebet_lost';
          };

    readonly insurance?:
        | undefined
        | {
              readonly payout: string;
              readonly status: 'insurance_won' | 'insurance_lost';
          };

    readonly round_closed?: boolean;
    readonly total_payout?: string;

    readonly action?: GameActionName;
    readonly requestData?: RequestData<GameActionName>;
    readonly actionInProgress?:
        | undefined
        | {
              readonly action: GameActionName;
              readonly requestData: RequestData<GameActionName>;
          };
}
