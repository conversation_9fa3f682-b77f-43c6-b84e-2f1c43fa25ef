import { defineComponent, type Component, type SignalReader } from '@monkey-tilt/ui';
import { NumberInput, type NumberInputProps } from './number-input';

export interface PercentageInputProps extends NumberInputProps {
    /**
     * The number of fraction digits to be displayed. Defaults to 0.
     */
    readonly fractionDigits?: number;
    /**
     * The minimum and maximum amounts allowed. Defaults to {min: 0, max: 1_000}.
     */
    readonly limits?: SignalReader<{ min: number; max: number }>;
}

export const PercentageInput = defineComponent(
    'PercentageInput',
    () =>
        ({
            fractionDigits = 0,
            suffix = '%',
            multipliers = [],
            step = 1,
            showStepper = false,
            limits = () => ({ min: 0, max: 1_000 }),
            ...props
        }: PercentageInputProps): Component<HTMLDivElement> => {
            return NumberInput({
                fractionDigits,
                limits,
                suffix,
                multipliers,
                step,
                showStepper,
                ...props,
            });
        },
);
