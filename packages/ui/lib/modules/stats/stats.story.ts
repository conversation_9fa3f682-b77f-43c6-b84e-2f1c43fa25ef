import type { Meta, StoryObj } from '@storybook/html';
import { Stats, type StatsProps } from './stats';

const meta: Meta = {
    title: 'Modules/Stats',
    globals: {
        backgrounds: { value: 'dark' },
    },
};
export default meta;

export const Default: StoryObj<StatsProps> = {
    render: (props) => Stats(props).render(),
    args: {
        stats: [
            { label: 'Odds', value: '1.20x' },
            { label: 'Profit', value: '$523.00' },
            { label: 'Times Landed', value: '1,000' },
        ],
    },
};

export const FourStats: StoryObj<StatsProps> = {
    render: (props) => Stats(props).render(),
    args: {
        stats: [
            { label: 'Odds', value: '1.20x' },
            { label: 'Profit', value: '$523.00' },
            { label: 'Times Landed', value: '1,000' },
            { label: 'Accuracy', value: '98.7%' },
        ],
    },
};

export const Formatted: StoryObj<StatsProps> = {
    render: (props) => Stats(props).render(),
    args: {
        stats: [
            {
                label: 'Profit',
                value: 523,
                format: (v) => `$${v}`,
            },
            {
                label: 'Odds',
                value: 1.25,
                format: (v) => `${v}x`,
            },
        ],
    },
};