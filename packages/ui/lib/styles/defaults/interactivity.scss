@mixin styles {
    :where(:root) {
        // Use the default cursor in all browsers
        cursor: default;

        // Opt-in to the smooth scrolling behavior in all browsers
        scroll-behavior: smooth;
    }

    :where(details > summary:first-of-type) {
        // Add the correct display in Safari
        display: list-item;
    }

    :where(
        label[for],
        a[href],
        button,
        [type='button' i],
        [type='submit' i],
        [type='reset' i],
        [role='button' i],
        [tabindex]:not([tabindex='-1'])
    ) {
        // Make the cursor on interactive elements consistent in all browsers
        cursor: pointer;
    }
}
