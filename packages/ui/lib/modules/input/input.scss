@use '../../styles/common/bem';
@use '../../styles/common/prop';
@use '../../styles/common/util';
@use '../../styles/common/respond';

$-block-size: 40px;
$-border-size: 1px;
$-gap: $-block-size * 0.1;
$-radius: $-block-size * 0.25;
$-padding: $-gap * 3;

@mixin styles {
    @include bem.module('input') {
        position: relative;
        display: flex;
        align-items: stretch;
        flex-direction: row;
        gap: calc(prop.get(input-gap-scale, prop.get(input-scale, 1)) * util.to-rem($-gap));
        block-size: calc(
            prop.get(input-size-scale, prop.get(input-scale, 1)) * util.to-rem($-block-size)
        );
        box-sizing: content-box;
        inline-size: 100%;
        background: prop.get('color-zinc-900');
        border: calc(
                prop.get(input-border-scale, prop.get(input-scale, 1)) * util.to-rem($-border-size)
            )
            solid prop.get('color-zinc-800');
        border-radius: calc(
            prop.get(input-radius-scale, prop.get(input-scale, 1)) * util.to-rem($-radius)
        );
        color: prop.get(input-color, prop.get('color-zinc-300'));
        max-inline-size: prop.get(measure);
        transition: box-shadow 0.15s;
        font-size: calc(prop.get(input-font-scale, prop.get(input-scale, 1)) * 1rem);

        @include prop.set(
            (
                icon-size: calc(
                        prop.get(
                                input-icon-scale,
                                prop.get(input-font-scale, prop.get(input-scale, 1))
                            ) *
                            prop.get(size-l)
                    ),
            )
        );

        * {
            box-sizing: border-box;
        }

        &:focus,
        &:active,
        &:focus-within {
            box-shadow: 0 0 util.to-rem($-border-size) prop.get('color-mt-400');
        }

        &:focus-within {
            @include prop.set(input-color, prop.get('color-white'));
        }

        &:has(:disabled) {
            background: prop.get('color-zinc-800');
            border-color: prop.get('color-zinc-700');
            pointer-events: none;

            @include prop.set(input-color, prop.get('color-zinc-450'));
        }

        &__input,
        &__sizer {
            border: none;
            font-size: inherit;
            line-height: inherit;
            padding: 0
                calc(
                    prop.get(input-padding-scale, prop.get(input-scale, 1)) * util.to-rem($-padding)
                );
            margin: 0;
            min-inline-size: 2ch;
        }

        &__affix {
            padding-inline-start: calc(
                prop.get(input-padding-scale, prop.get(input-scale, 1)) * util.to-rem($-padding)
            );
            max-inline-size: min-content;
        }

        &__input + &__affix {
            margin-inline-start: auto;
            padding-inline-start: 0;
            padding-inline-end: calc(
                prop.get(input-padding-scale, prop.get(input-scale, 1)) * util.to-rem($-padding)
            );

            &:has(#{bem.namespaced-selector(m-button)}) {
                padding-inline-end: 0;

                @include bem.module('button') {
                    transform: translate(14%);
                    @include prop.set(icon-size, prop.get(size-2xl), $important: true);

                    @include respond.until(900px) {
                        transform: translate(18%) scale(0.9);
                        @include prop.set(icon-size, prop.get(size-l), $important: true);
                    }
                }
            }
        }

        &:has(&__affix + &__sizer) :is(&__input, &__sizer) {
            padding-inline-start: 0;
        }

        &:has(&__input + &__affix) :is(&__input, &__sizer) {
            padding-inline-end: 0;
        }

        &__sizer {
            visibility: hidden;
            overflow: hidden;
            white-space: nowrap;
            text-overflow: clip;
            pointer-events: none;
        }

        &__input,
        &__affix {
            display: flex;
            align-items: center;
            outline: none;
            background: none;
            color: inherit;
            flex: 1 1 auto;
            cursor: text;
        }

        &__input {
            position: absolute;
            inset: 0 auto 0 prop.get(input-position, 0);
            inline-size: 100%;
            max-inline-size: prop.get(input-size, 100%);

            &:focus-visible {
                outline: none;
                box-shadow: none;
            }
        }

        & > &__action {
            border-width: 0;
            border-radius: 0
                calc(prop.get(input-radius-scale, prop.get(input-scale, 1)) * util.to-rem($-radius))
                calc(prop.get(input-radius-scale, prop.get(input-scale, 1)) * util.to-rem($-radius))
                0;
            border-inline-start: calc(
                    prop.get(input-border-scale, prop.get(input-scale, 1)) *
                        util.to-rem($-border-size)
                )
                solid prop.get('color-zinc-800');
            block-size: auto;

            &#{bem.namespaced-selector(m-button)} {
                background: prop.get('button-gradient');

                @include prop.set(
                    (
                        button-gradient-angle: 135deg,
                        button-gradient: linear-gradient(
                                prop.get(button-gradient-angle),
                                #373737 0%,
                                #171717 100%
                            ),
                    )
                );

                &:hover {
                    @include prop.set(button-gradient-angle, 180deg);
                }

                &:has(:active) {
                    @include prop.set(button-gradient-angle, 0deg);
                }
            }
        }

        &__addons {
            display: flex;
            flex-direction: row;
            gap: calc(prop.get(input-gap-scale, prop.get(input-scale, 1)) * util.to-rem($-gap));
            block-size: calc(
                prop.get(input-size-scale, prop.get(input-scale, 1)) *
                    util.to-rem($-block-size) - prop.get(
                        input-gap-scale,
                        prop.get(input-scale, 1)
                    ) *
                    util.to-rem($-gap) * 2
            );
            align-self: center;

            &:last-child {
                margin-inline-end: calc(
                    prop.get(input-gap-scale, prop.get(input-scale, 1)) * util.to-rem($-gap)
                );
            }

            &-percentage {
                align-self: center;
                padding-inline-end: calc(
                    prop.get(input-padding-scale, prop.get(input-scale, 1)) * util.to-rem($-padding)
                );
            }
        }

        &--medium {
            @include prop.set(
                (
                    input-scale: 1.25,
                    input-font-scale: 1.15,
                )
            );
        }
    }
}
