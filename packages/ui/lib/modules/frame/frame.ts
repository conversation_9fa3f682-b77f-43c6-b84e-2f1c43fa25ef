import { create, defineComponent, type Props } from '../../component';
import type { Size } from '../../layouts';
import { FrameSection } from './section';

export interface FrameProps extends Props<HTMLDivElement> {
    readonly variant?: 'default' | 'small' | 'medium' | 'dashed';
    readonly gap?: Size | undefined;
}

const frame = defineComponent(
    'Frame',
    () =>
        ({ variant = 'default', gap, ...props }: FrameProps): HTMLDivElement =>
            create('div', {
                props: {
                    ...props,
                    className: {
                        'l-cover': true,
                        'l-box': true,
                        'l-stack': true,
                        'm-frame': true,
                        [`m-frame--${variant}`]: variant !== 'default',
                    },
                    cssProps: {
                        'stack-gap': gap && { var: `size-${gap}` },
                        ...(props.cssProps ?? {}),
                    },
                },
            }),
);

export const Frame: typeof frame & {
    readonly Section: typeof FrameSection;
} = Object.assign(frame, {
    Section: FrameSection,
});
