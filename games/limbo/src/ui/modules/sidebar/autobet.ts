import {
    currencyToFixedString,
    precisionForCurrency,
    NumberInput,
    PercentageInput,
} from '@monkey-tilt/client';
import {
    Button,
    defineComponent,
    Flex,
    Frame,
    Icon,
    Label,
    Radio,
    Stack,
    type Component,
    type Props,
    type RefObject,
    type SignalReader,
} from '@monkey-tilt/ui';
import type { AutobetState } from '../../../game/state';
import { type LimboUI } from '../../limbo';

export interface AutobetProps extends Props<HTMLDivElement> {
    /**
     * The Limbo UI state manager.
     */
    readonly ui: LimboUI;

    readonly actionState: SignalReader<{
        readonly next_actions: ReadonlyArray<string>;
        readonly round_closed: boolean;
        readonly canPlay: boolean;
    }>;
}

export const Autobet = defineComponent(
    'Autobet',
    ({ state }) =>
        ({ ui, actionState, ...props }: AutobetProps): Component<HTMLDivElement> => {
            const { effect, memo } = state();

            const precision = memo(() => precisionForCurrency(ui.currency()));

            const autobetState = memo(() => {
                const state = ui.gameState();
                const currency = ui.currency();
                return {
                    autobet_limit: (state.autobet_limit ?? 0).toFixed(0),
                    on_win_reset: state.on_win_reset !== false,
                    on_win: Number.parseFloat(state.on_win ?? '0').toFixed(0),
                    on_loss_reset: state.on_loss_reset !== false,
                    on_loss: Number.parseFloat(state.on_loss ?? '0').toFixed(2),
                    stop_on_profit: currencyToFixedString(
                        Number.parseFloat(state.stop_on_profit ?? '0'),
                        currency,
                    ),
                    stop_on_loss: currencyToFixedString(
                        Number.parseFloat(state.stop_on_loss ?? '0'),
                        currency,
                    ),
                    autobet_cumulative_payout: currencyToFixedString(
                        Number.parseFloat(state.autobet_cumulative_payout ?? '0'),
                        currency,
                    ),
                } satisfies {
                    [K in Exclude<
                        keyof AutobetState,
                        'autobet_count'
                    >]: AutobetState[K] extends boolean ? boolean : string;
                };
            });

            effect(() => {
                const state = autobetState();
                for (const key of Object.keys(state) as (keyof typeof state)[]) {
                    ui.autobet[key].update(state[key] as string & boolean);
                }
            });

            const onWinRef: RefObject<HTMLInputElement> = { current: null };
            const onWinPercentageDisabled = memo(
                () => ui.isBettingLocked() || ui.autobet.on_win_reset.read(),
            );

            const onLossRef: RefObject<HTMLInputElement> = { current: null };
            const onLossPercentageDisabled = memo(
                () => ui.isBettingLocked() || ui.autobet.on_loss_reset.read(),
            );

            const handleDisabledState = (element: HTMLButtonElement) => {
                effect(() => {
                    element.disabled = ui.isBettingLocked();
                });
            };

            return Stack(
                { gap: 'xl', ...props },
                NumberInput({
                    label: 'Number of Bets',
                    value: ui.autobet.autobet_limit,
                    allowInfinity: true,
                    disabled: ui.isBettingLocked,
                    showStepper: true,
                    step: 1,
                    fractionDigits: 0,
                    buttons: [
                        {
                            label: '10',
                            onClick: () => ui.autobet.autobet_limit.update('10'),
                        },
                        {
                            label: '100',
                            onClick: () => ui.autobet.autobet_limit.update('100'),
                        },
                    ],
                    action: Button(
                        {
                            ref: handleDisabledState,
                            onClick: (e: MouseEvent) => {
                                e.stopImmediatePropagation();
                                ui.autobet.autobet_limit.update('0');
                            },
                        },
                        Icon({
                            name: 'infinity',
                            description: 'Do not limit number of bets',
                        }),
                    ),
                }),
                Frame(
                    {
                        variant: 'small',
                        cssProps: {
                            'frame-padding': { var: 'size-m' },
                            'stack-gap': { var: 'size-m' },
                        },
                    },
                    Flex(
                        { wrap: false, justify: 'space-between' },
                        Label({ variant: 'medium', text: 'On win' }),
                        Radio.Group({
                            wrap: false,
                            disabled: ui.isBettingLocked,
                            value: ui.autobet.on_win_reset.read,
                            items: [
                                { value: true, label: 'Reset' },
                                { value: false, label: 'Increase by' },
                            ],
                            onChange: (value: boolean) => {
                                ui.autobet.on_win_reset.update(value);
                                if (!value) {
                                    onWinRef.current?.focus();
                                }
                            },
                        }),
                    ),
                    PercentageInput({
                        inputRef: onWinRef,
                        value: ui.autobet.on_win,
                        disabled: onWinPercentageDisabled,
                    }),
                ),
                Frame(
                    {
                        variant: 'small',
                        cssProps: {
                            'frame-padding': { var: 'size-m' },
                            'stack-gap': { var: 'size-m' },
                        },
                    },
                    Flex(
                        { wrap: false, justify: 'space-between' },
                        Label({ variant: 'medium', text: 'On Loss' }),
                        Radio.Group({
                            wrap: false,
                            disabled: ui.isBettingLocked,
                            value: ui.autobet.on_loss_reset.read,
                            items: [
                                { value: true, label: 'Reset' },
                                { value: false, label: 'Increase by' },
                            ],
                            onChange: (value: boolean) => {
                                ui.autobet.on_loss_reset.update(value);
                                if (!value) {
                                    onLossRef.current?.focus();
                                }
                            },
                        }),
                    ),
                    PercentageInput({
                        inputRef: onLossRef,
                        value: ui.autobet.on_loss,
                        disabled: onLossPercentageDisabled,
                    }),
                ),
                NumberInput({
                    label: 'Stop on Profit',
                    value: ui.autobet.stop_on_profit,
                    disabled: ui.isBettingLocked,
                    step: 0.1,
                    suffix: ui.currency,
                    conversionFactor: ui.usdRate,
                    fractionDigits: precision(),
                }),
                NumberInput({
                    label: 'Stop on Loss',
                    value: ui.autobet.stop_on_loss,
                    disabled: ui.isBettingLocked,
                    step: 0.1,
                    suffix: ui.currency,
                    conversionFactor: ui.usdRate,
                    fractionDigits: precision(),
                }),
            );
        },
);
