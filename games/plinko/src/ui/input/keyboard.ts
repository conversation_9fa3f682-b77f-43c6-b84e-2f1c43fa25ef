import { D<PERSON><PERSON><PERSON>boardController, KeyboardController } from '@monkey-tilt/ui';
import type { UIAction } from '../actions';

export const keyboardShortcuts: KeyboardController<UIAction> = new DOMKeyboardController<UIAction>({
    keyMap: new Map([
        ['Space', { action: 'BetOrStop', description: 'Make a bet' }],
        ['W', { action: 'MultiplyBetAmount', data: 2, description: 'Double bet amount' }],
        ['S', { action: 'MultiplyBetAmount', data: 0.5, description: 'Halve bet amount' }],
        ['Q', { action: 'SetBetAmount', data: 0, description: 'Zero bet amount' }],
        ['S', { action: 'IncreaseNumberOfRows', description: 'Increase number of rows' }],
        ['A', { action: 'DecreaseNumberOfRows', description: 'Decrease number of rows' }],
        ['X', { action: 'SetRisk', data: 0, description: 'Select Low risk' }],
        ['C', { action: 'SetRisk', data: 1, description: 'Select Medium risk' }],
        ['V', { action: 'SetRisk', data: 2, description: 'Select High risk' }],
    ]),
});
