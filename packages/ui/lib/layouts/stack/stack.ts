import { defineComponent, html, type Component, type Props } from '../../component';
import { mergeClasses } from '../../utilities';
import type { Size } from '../types';

export interface StackProps extends Props<HTMLDivElement> {
    readonly gap?: Size;
    readonly split?: 1 | 2 | 3 | 4 | 5 | 6;
    readonly splitLast?: 1 | 2 | 3 | 4 | 5 | 6;
    readonly center?: boolean;
    readonly scroll?: boolean;
}

export const Stack = defineComponent(
    'Stack',
    () =>
        ({
            gap,
            split,
            splitLast,
            center = false,
            scroll = false,
            ...props
        }: StackProps): Component<HTMLDivElement> =>
            html('div')({
                ...props,
                cssProps: {
                    'stack-gap': gap && { var: `size-${gap}` },
                    ...(props.cssProps ?? {}),
                },
                className: mergeClasses(props.className, {
                    'l-stack': true,
                    'l-stack--scroll': scroll,
                    'l-stack--center': center,
                    [`l-stack--split-${split}`]: split !== undefined,
                    [`l-stack--split-last-${splitLast}`]: splitLast !== undefined,
                }),
            }),
);
