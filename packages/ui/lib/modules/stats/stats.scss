@use '../../styles/common/bem';
@use '../../styles/common/prop';
@use '../../styles/common/util';
@use '../../styles/common/respond';
@use '../../styles/config/metrics';
@use '../../styles/common/type-style' as *;

$-border-radius-large: util.to-rem(20px);
$-border-radius-small: util.to-rem(12px);

$-component-gap-desktop: util.to-rem(16px);
$-component-gap-mobile: util.to-rem(10px);

$-item-gap-desktop: util.to-rem(8px);
$-item-gap-mobile: util.to-rem(4px);

$-background-gradient: 
    radial-gradient(
        ellipse at bottom left,
        hsla(54.11, 95.73%, 45.88%, 0.3) 0%,
        transparent 40%
    ),
    radial-gradient(
        ellipse at top left,
        hsla(54.11, 95.73%, 45.88%, 0.3) 0%,
        transparent 40%
    ),
    radial-gradient(
        ellipse at bottom right,
        hsl(21.71deg 77.78% 26.47% / 54%) 0%,
        transparent 60%
    ),
    radial-gradient(
        ellipse at top right,
        hsl(21.71deg 77.78% 26.47% / 51%) 0%,
        transparent 70%
    ),
    radial-gradient(ellipse at center, #09090b 0%, transparent 70%);

$-border: 1px solid rgba(255, 255, 255, 0.12);
$-box-shadow:
    0 0 0 1px rgba(255, 255, 255, 0.08),
    inset 0 0 0 1px rgba(255, 255, 255, 0.04);

$-padding-desktop: util.to-rem(16px);
$-padding-mobile: util.to-rem(12px);

@mixin styles {
    @include bem.module('stats') {
        display: flex;
        align-items: center;
        justify-content: space-between;
        flex-wrap: wrap;
        gap: $-component-gap-desktop;
        padding: $-padding-desktop;
        position: relative;
        border-radius: $-border-radius-large;
        background: prop.get(color-zinc-950);
        background-image: $-background-gradient;
        border: $-border;
        box-shadow: $-box-shadow;

        &::after {
            content: '';
            position: absolute;
            inset: 0;
            display: block;
            background: rgba(0, 0, 0, 0.7);
            z-index: 1;
            pointer-events: none;
            border-radius: inherit;
        }

        @include respond.until(m) {
            gap: $-component-gap-mobile;
            padding: $-padding-mobile;
        }

        &__item {
            display: flex;
            flex-direction: row;
            justify-content: space-between;
            align-items: center;
            flex: 1;
            gap: $-item-gap-desktop;
            min-inline-size: fit-content;
            background: rgba(255, 255, 255, 0.08);
            border-radius: $-border-radius-small;
            padding: $-padding-desktop;
            z-index: 2;

            @include respond.until(m) {
                gap: $-item-gap-mobile;
                padding: $-padding-mobile;
            }
        }

        &__label,
        &__value {
            font-size: metrics.fluid-size(8, 14);
            white-space: nowrap;
            word-break: keep-all;
            overflow-wrap: normal;
        }

        &__label {
            font-weight: 500;
            color: prop.get(color-white);
        }

        &__value {
            font-weight: 700;
            color: prop.get(color-yellow-400);
        }

        &__arrow {
            position: absolute;
            bottom: -12px;
            inline-size: 0;
            block-size: 0;
            border-left: util.to-rem(10px) solid transparent;
            border-right: util.to-rem(10px) solid transparent;
            border-top: util.to-rem(12px) solid prop.get(color-zinc-700);
        }
    }
}