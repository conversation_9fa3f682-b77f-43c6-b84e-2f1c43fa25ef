import { createRoot, type Renderable, type Store } from '../lib';

export interface Context {
    readonly store: Store;
    onStoryUnmount(this: void, callback: () => void): void;
}

export function render(story: (context: Context) => Renderable) {
    const rootElement = document.createElement('div');
    rootElement.style.minHeight = '100%';

    void Promise.resolve().then(() => {
        const root = createRoot(rootElement);
        const context: Context = {
            store: root.store,
            onStoryUnmount: root.onUnmount,
        };

        root.render(story(context));
    });

    return rootElement;
}
