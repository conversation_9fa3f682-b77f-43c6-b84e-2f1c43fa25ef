import {
    type APIC<PERSON>,
    type BalanceData,
    type BetAmountLimits,
    type SeedData,
    type VerifySeedParams,
    type SeedVerificationData,
} from '@monkey-tilt/client';

export class MockAPIClient implements Partial<APIClient> {
    gameId: string | undefined;
    #gameSessionId: string | undefined;
    #mockBalance = 1000.0;

    constructor(gatewayUrl: string | URL) {
        // Mock constructor - gatewayUrl not used
        console.log('Mock API Client initialized with gateway:', gatewayUrl);
    }

    get gameSessionId(): string {
        if (!this.#gameSessionId) {
            throw new Error('Game session ID not set');
        }
        return this.#gameSessionId;
    }

    set gameSessionId(value: string) {
        this.#gameSessionId = value;
    }

    getBalance(): Promise<BalanceData> {
        return Promise.resolve({
            balance: this.#mockBalance,
            currency: 'USD',
        });
    }

    getUSDRate(currency: string): Promise<{ rate: number; expiresOn: number }> {
        // Mock exchange rates
        const rates: Record<string, number> = {
            'USD': 1.0,
            'EUR': 0.85,
            'GBP': 0.73,
            'JPY': 110.0,
            'BTC': 0.000024,
            'ETH': 0.00035,
        };

        return Promise.resolve({
            rate: rates[currency.toUpperCase()] || 1.0,
            expiresOn: Date.now() + 3600000, // 1 hour from now
        });
    }

    getBetLimits(): Promise<BetAmountLimits> {
        return Promise.resolve({
            bet: {
                min: 0.01,
                max: 1000,
                max_payout: 50000,
            },
        });
    }

    getSeeds(): Promise<SeedData> {
        return Promise.resolve({
            client_seed: 'mock_client_seed_' + Math.random().toString(36).slice(2),
            server_seed_hashed: 'mock_server_hash_' + Math.random().toString(36).slice(2),
            next_server_seed_hashed: 'mock_next_hash_' + Math.random().toString(36).slice(2),
        });
    }

    verifySeeds(params: VerifySeedParams): Promise<SeedVerificationData> {
        // Mock seed verification - in real implementation this would verify cryptographic proofs
        return Promise.resolve({
            type: 'SeedVerification',
            action: 'verify',
            data: {
                random_multiplier: (1.01 + Math.random() * 10).toFixed(2),
                previous_bet_outcome: Math.random() > 0.5 ? 'win' : 'loss',
                verified: true,
                server_seed: 'mock_revealed_server_seed',
                nonce: params.nonce || 0,
            },
        });
    }

    setClientSeed(client_seed: string): Promise<void> {
        console.log('Mock: Setting client seed to:', client_seed);
        return Promise.resolve();
    }

    // Update mock balance (for testing purposes)
    updateBalance(newBalance: number): void {
        this.#mockBalance = newBalance;
    }

    // Get current mock balance
    getCurrentBalance(): number {
        return this.#mockBalance;
    }

    getConfigCustomProps<T>(spec: T): Promise<any> {
        // Mock configuration properties
        return Promise.resolve({
            min_multiplier: '1.01',
            max_multiplier: '500.00',
        } as any);
    }

    // Add any other missing methods that might be called
    getMultipliers?(): Promise<any> {
        return Promise.resolve({
            min: 1.01,
            max: 500.00,
        });
    }

    getExchangeRates?(): Promise<any> {
        return Promise.resolve({
            USD: 1.0,
            EUR: 0.85,
            GBP: 0.73,
        });
    }
} 