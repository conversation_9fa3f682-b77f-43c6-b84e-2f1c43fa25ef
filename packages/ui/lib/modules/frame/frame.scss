@use '../../styles/common/bem';
@use '../../styles/common/prop';
@use '../../styles/common/util';

$-default-padding: util.to-rem(32px);
$-default-border-size: util.to-rem(1px);
$-small-padding: prop.get(size-2xs);
$-medium-padding: util.to-rem(12px);

@mixin styles {
    @include bem.module('frame') {
        background: prop.get(frame-fill, linear-gradient(180deg, #1d1d1f 0%, #111113 100%));
        border: prop.get(
            frame-border,
            prop.get(frame-border-size, $-default-border-size) prop.get(frame-border-style, solid)
                prop.get(frame-border-color, prop.get(color-zinc-800))
        );
        border-radius: prop.get(frame-border-radius, util.to-rem(24px));
        isolation: isolate;
        min-block-size: min-content;

        @include prop.set(
            (
                min-block-size: 100%,
                gap: 0,
                box-gap: prop.get(frame-padding, 0),
            )
        );

        $-ns: bem.block-selector(module, 'frame');

        &:not(:has(#{$-ns}__section)) {
            @include prop.set(frame-padding, 0 $-default-padding);
        }

        :has(#{$-ns}__section) {
            @include prop.set(frame-padding, 0);
        }

        &--small {
            @include prop.set(
                (
                    frame-fill: prop.get(color-zinc-900),
                    frame-border-radius: util.to-rem(12px),
                    frame-border-color: prop.get(color-zinc-800),
                    frame-padding: $-small-padding,
                )
            );

            &:not(:has(#{$-ns}__section)) {
                @include prop.set(frame-padding, $-small-padding);
            }
        }

        & &--small {
            @include prop.set(frame-fill, rgb(from prop.get(color-zinc-950) r g b / 0.25));
        }

        &--medium,
        &--dashed {
            @include prop.set(
                (
                    frame-fill: prop.get(color-zinc-900),
                    frame-border-radius: util.to-rem(8px),
                    frame-padding: $-medium-padding,
                )
            );

            &:not(:has(#{$-ns}__section)) {
                @include prop.set(frame-padding, $-medium-padding);
            }
        }

        &--dashed {
            @include prop.set(frame-border-style, dashed);
        }

        &__section {
            position: relative;

            @include prop.set(
                (
                    box-gap: prop.get(frame-padding),
                    frame-padding: $-default-padding,
                )
            );

            &::before {
                content: '';
                display: block;
                position: absolute;
                inset: 0 0 auto 0;
                block-size: prop.get(frame-border-size, $-default-border-size);
                background: prop.get(frame-border-color, prop.get(color-zinc-800));
            }

            &:first-child::before {
                display: none;
            }

            &--subtle {
                @include prop.set(
                    frame-border-color,
                    linear-gradient(
                        90deg,
                        transparent 0%,
                        rgba(255, 255, 255, 0.24) 49.9%,
                        transparent 99.79%
                    )
                );
            }
        }
    }
}
