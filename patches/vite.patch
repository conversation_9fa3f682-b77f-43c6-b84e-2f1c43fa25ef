diff --git a/dist/node/chunks/dep-DBxKXgDP.js b/dist/node/chunks/dep-DBxKXgDP.js
index 2cd82a2f105df2ac39d49cc8c8f787325d882f81..87b4ebf1ea4de54bac127b4e9bd6ed027c1d90b1 100644
--- a/dist/node/chunks/dep-DBxKXgDP.js
+++ b/dist/node/chunks/dep-DBxKXgDP.js
@@ -10601,7 +10601,6 @@ function shouldInline(environment, file, id, content, buildPluginContext, forceI
   if (noInlineRE.test(id)) return false;
   if (inlineRE$3.test(id)) return true;
   if (buildPluginContext) {
-    if (environment.config.build.lib) return true;
     if (buildPluginContext.getModuleInfo(id)?.isEntry) return false;
   }
   if (forceInline !== void 0) return forceInline;
