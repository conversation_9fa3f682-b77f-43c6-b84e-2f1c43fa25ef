import { CELL_SIZE, PEG_RADIUS } from '../../../physics/constants';

const hasOffscreenCanvas = false; //'OffscreenCanvas' in globalThis;

export interface createImageSourceOptions {
    readonly width: number;
    readonly height: number;
    readonly scale?: number | undefined;
}

export function createImageSource(
    { width, height, scale = window.devicePixelRatio }: createImageSourceOptions,
    paint: (context: Omit<CanvasRenderingContext2D, 'canvas'>) => void,
): CanvasImageSource {
    const canvas = hasOffscreenCanvas
        ? new OffscreenCanvas(width, height)
        : document.createElement('canvas');

    const context = canvas.getContext('2d') as Omit<CanvasRenderingContext2D, 'canvas'>;

    if (context === null) {
        throw new Error('Failed to get 2D context');
    }

    canvas.width = width * scale;
    canvas.height = height * scale;

    context.scale(scale, scale);

    paint(context);

    return canvas instanceof HTMLCanvasElement ? canvas : canvas.transferToImageBitmap();
}

const pegLatticeCache = new Map<`${number}@${number}`, CanvasImageSource>();

export function getPegLatticeImage(
    rows: number,
    scale = window.devicePixelRatio,
): CanvasImageSource {
    const key = `${rows}@${scale}` as const;
    let image = pegLatticeCache.get(key);

    if (!image) {
        image = createImageSource(
            { width: (rows + 2) * CELL_SIZE, height: (rows + 1) * CELL_SIZE, scale },
            (context) => {
                context.fillStyle = '#e4e4e7';

                for (let y = 0; y < rows; y++) {
                    for (let i = 0; i < y + 3; i++) {
                        const x = i + (rows - y - 1) / 2;

                        context.beginPath();
                        context.arc(
                            (x + 0.5) * CELL_SIZE,
                            (y + 0.5) * CELL_SIZE,
                            PEG_RADIUS,
                            0,
                            Math.PI * 2,
                        );
                        context.fill();
                        context.closePath();
                    }
                }
            },
        );

        pegLatticeCache.set(key, image);
    }

    return image;
}

const imageCache = new Map<string, CanvasImageSource>();

export function loadImage(
    url: string | URL,
    width?: number,
    height = width,
): Promise<CanvasImageSource> {
    const cacheKey = `${url.toString()}-${width ?? 0}-${height ?? 0}`;

    const cachedImage = imageCache.get(cacheKey);
    if (cachedImage) {
        return Promise.resolve(cachedImage);
    }

    return new Promise((resolve, reject) => {
        const image = new Image(width, height);
        image.src = url.toString();

        image.onload = () => {
            imageCache.set(cacheKey, image);
            resolve(image);
        };
        image.onerror = (error) => reject(error);
    });
}
