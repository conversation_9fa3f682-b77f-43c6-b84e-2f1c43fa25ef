<!doctype html>
<html lang="en">

<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Monkey Tilt Crash</title>
    <style>
        html,
        body {
            margin: 0;
            height: 100%;
        }
    </style>
</head>

<body>
    <div id="app"></div>
    <script type="module">
        import '@monkey-tilt/ui/style.scss';
        import { WebSocketTransport } from '@monkey-tilt/client';
        import { run } from './src/main.ts';

        let context;

        globalThis.runGame = () => {
            if (context) {
                console.log('Unmounting previous game');
                context.unmount();
            }

            const urlParams = new URL(window.location.href).searchParams;
            const useMockData = urlParams.get('mock') === 'true';

            run({
                container: '#app',
                gameId: 'crash-007',
                gatewayUrl: `${window.location.origin}/-/og-dev/v1`,
                currency: urlParams.get('currency') ?? 'USD',
                gameSessionId: urlParams.get('game_session') ?? '899933d6-125a-4011-8126-bbacb756efad',
                useMockData,
            }).then((gameContext) => {
                console.log('Game mounted', gameContext);
                context = gameContext;
            });
        };

        runGame();
    </script>
</body>

</html> 