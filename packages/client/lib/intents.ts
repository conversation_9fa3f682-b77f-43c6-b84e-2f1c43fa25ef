export interface Intent<T extends string = string> {
    readonly type: T;
}

export interface SwitchGameSessionIntent extends Intent<'SwitchGameSession'> {
    readonly gameSessionId: string;
    readonly currency: string;
}

export interface CloseAutobetIntent extends Intent<'CloseAutobet'> {}

export interface IntentTypeMap {
    SwitchGameSession: SwitchGameSessionIntent;
    CloseAutobet: CloseAutobetIntent;
}

export type IntentType = IntentTypeMap[keyof IntentTypeMap]['type'];

export function isIntent<T extends IntentType>(
    intent: Intent,
    type: T,
): intent is IntentTypeMap[T] {
    return intent.type === type;
}

export class IntentManager {
    #intents: Map<string, Intent[]> = new Map();

    public constructor() {}

    protected onIntentAdded<T extends Intent>(_intent: T): void {}
    protected onIntentRemoved<T extends Intent>(_intent: T): void {}

    /**
     * @deprecated Use `queueIntent`/`setIntent` instead.
     */
    addIntent<T extends Intent>(intent: T): void {
        return this.setIntent(intent);
    }

    setIntent<T extends Intent>(intent: T): void {
        this.#intents.set(intent.type, [intent]);
        this.onIntentAdded(intent);
    }

    queueIntent<T extends Intent>(intent: T): void {
        let intents = this.#intents.get(intent.type);
        if (!intents) {
            intents = [];
            this.#intents.set(intent.type, intents);
        }
        intents.push(intent);
        this.onIntentAdded(intent);
    }

    dequeueIntent<T extends IntentType>(intentType: T): IntentTypeMap[T] | undefined {
        const intents = this.#intents.get(intentType);
        if (!intents) {
            return undefined;
        }
        if (intents.length === 0) {
            this.#intents.delete(intentType);
            return undefined;
        }
        const intent = intents.shift() as IntentTypeMap[T];
        if (intents.length == 0) {
            this.#intents.delete(intentType);
        }
        this.onIntentRemoved(intent);
        return intent;
    }

    hasIntent<T extends IntentType>(intentType: T): boolean {
        const intents = this.#intents.get(intentType);
        return intents ? intents.length > 0 : false;
    }

    removeIntent<T extends IntentType>(intentType: T): void {
        const intents = this.#intents.get(intentType);

        if (intents) {
            this.#intents.delete(intentType);

            for (const intent of intents) {
                this.onIntentRemoved(intent);
            }
        }
    }

    getIntent<T extends IntentType>(intentType: T): IntentTypeMap[T] | undefined {
        const intents = this.#intents.get(intentType);
        if (!intents || intents.length === 0) {
            return undefined;
        }
        return intents[0] as IntentTypeMap[T];
    }

    clearIntents(): void {
        this.#intents.clear();
    }
}
