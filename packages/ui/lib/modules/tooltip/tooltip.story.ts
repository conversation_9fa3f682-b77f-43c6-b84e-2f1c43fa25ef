import type { <PERSON>a, StoryObj } from '@storybook/html';
import { html } from '../../component';
import { Icon } from '../icon/icon';
import { Tooltip, type TooltipProps } from './tooltip';

const meta: Meta = {
    title: 'Modules/Tooltip',
};
export default meta;

export const Default: StoryObj = (props: TooltipProps) => {
    return html('div')(
        null,
        Icon({ name: 'info', id: 'tooltip-trigger' }),
        Tooltip(
            {
                ...props,
                target: '#tooltip-trigger',
            },
            'Lorem ipsum dolor sit amet',
            html('br')(),
            'consectetur adipiscing elit.',
        ),
    ).render();
};
Default.argTypes = {
    showOn: { control: 'select', options: ['hover', 'click'] },
};
Default.args = {
    showOn: 'hover',
};
