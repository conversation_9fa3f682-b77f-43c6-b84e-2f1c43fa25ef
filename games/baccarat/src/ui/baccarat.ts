import {
    currencyToFixedString,
    GameUI,
    pick,
    timer,
    type RequestData,
    type VerifySeedParams,
    type Writable,
} from '@monkey-tilt/client';
import {
    type ModalControls,
    type RefObject,
    type Root,
    type Signal,
    type SignalReader,
    type ToastBarControls,
    type ToastControls,
    type ToastProps,
} from '@monkey-tilt/ui';
import { logDevError } from '@monkey-tilt/utils';
import { EmptySidebetState, type BaccaratClient } from '../game/baccarat';
import {
    BaccaratSidebets,
    type AutobetState,
    type BaccaratBetAmount,
    type BaccaratBetAmountLimits,
    type BaccaratSidebet,
    type BaccaratState,
    type FinalState,
} from '../game/state';
import type { UIAction } from './actions';
import { keyboardShortcuts } from './input/keyboard';
import { chipValue, clampChipSum, type ChipValue } from './types/chips';

import effects from './assets/effects.json';
import effectsMP3 from './assets/effects.mp3';
import effectsOGG from './assets/effects.ogg';
import effectsWEBM from './assets/effects.webm';

export type SoundEffect = keyof typeof effects;

type Config = {
    readonly soundEffects: boolean;
    readonly hotkeys: boolean;
    readonly turboMode: boolean;
};

export interface BaccaratUIOptions {
    /**
     * The base URL used to load assets from.
     */
    readonly assetsUrl: URL;

    /**
     * The Baccarat GameClient instance.
     */
    readonly client: BaccaratClient;

    /**
     * The state root.
     */
    readonly root: Root;
}

export interface GameState extends BaccaratState {}

export const SidebetToBetAmount: Record<BaccaratSidebet, BaccaratBetAmount> = {
    dragon7_sidebet: 'dragon_7_bet_amount',
    panda_sidebet: 'panda_bet_amount',
};

export const SidebetAmountToName: Partial<Record<BaccaratBetAmount, string>> = {
    dragon_7_bet_amount: 'Dragon 7',
    panda_bet_amount: 'Panda 8',
};

export const MainBetAmounts = [
    'player_bet_amount',
    'banker_bet_amount',
    'tie_bet_amount',
] as const satisfies BaccaratBetAmount[];

export const SidebetAmounts = [
    'dragon_7_bet_amount',
    'panda_bet_amount',
] as const satisfies BaccaratBetAmount[];

export const AllBetAmounts = (MainBetAmounts as BaccaratBetAmount[]).concat(SidebetAmounts);

export const BetAmountToBetLimit: Record<BaccaratBetAmount, keyof BaccaratBetAmountLimits> = {
    player_bet_amount: 'bet',
    banker_bet_amount: 'bet',
    tie_bet_amount: 'bet',
    dragon_7_bet_amount: 'dragon_7',
    panda_bet_amount: 'panda',
};

export class BaccaratUI extends GameUI<GameState, BaccaratClient, UIAction, Config, SoundEffect> {
    #toastBarControlsRef: RefObject<ToastBarControls> = { current: null };
    #notificationsControlsRef: RefObject<ToastBarControls> = { current: null };
    #hotkeysModalControlsRef: RefObject<ModalControls> = { current: null };
    #helpModalControlsRef: RefObject<ModalControls> = { current: null };
    #provablyFairModalControlsRef: RefObject<ModalControls> = { current: null };

    #pendingIntents: {
        CloseAutobet: Signal<boolean>;
    };

    #undoStack: (() => void)[] = [];

    public readonly activeTab: Signal<'manual' | 'auto'>;

    public readonly currency: SignalReader<string>;
    public readonly usdRate: SignalReader<number>;

    public readonly selectedChip: Signal<ChipValue | null>;
    public readonly bets: Record<BaccaratBetAmount, Signal<number>>;
    public readonly totalBetAmount: Signal<string>;

    public readonly autobet: {
        [K in Exclude<keyof AutobetState, 'autobet_count'>]: Signal<
            AutobetState[K] extends boolean ? boolean : string
        >;
    };

    #lastBet: BaccaratBetAmount = 'player_bet_amount';
    #setAllBets: (amount: number) => void;
    #multiplyAllBets: (multiplier: number) => void;

    public constructor({ client, assetsUrl, root }: BaccaratUIOptions) {
        const initialState = client.state();

        super({
            client,
            assetsUrl,
            root,
            keyboardShortcuts,
            config: {
                soundEffects: true,
                hotkeys: false,
                turboMode: window.matchMedia(`(prefers-reduced-motion: reduce)`).matches,
            },
            soundEffects: {
                sources: [
                    { src: new URL(effectsWEBM, assetsUrl).href, type: 'audio/webm; codecs=opus' },
                    { src: new URL(effectsOGG, assetsUrl).href, type: 'audio/ogg; codecs=vorbis' },
                    { src: new URL(effectsMP3, assetsUrl).href, type: 'audio/mpeg' },
                ],
                sprites: effects,
            },
            initialState: {
                ...initialState,
            },
            pendingUpdatesDelay: 500,
        });

        const { signal, effect, memo, untracked, batch } = this.root.store;

        this.#pendingIntents = {
            CloseAutobet: signal(false),
        };

        this.client.on('intentAdded', ({ type }) => {
            if (type === 'CloseAutobet') {
                this.#pendingIntents.CloseAutobet.update(true);
            }
        });
        this.client.on('intentRemoved', ({ type }) => {
            if (type === 'CloseAutobet') {
                this.#pendingIntents.CloseAutobet.update(false);
            }
        });

        this.activeTab = signal<'manual' | 'auto'>('manual');

        this.currency = memo(() => this.client.session().currency);
        this.usdRate = memo(() => this.client.session().usdRate);

        this.selectedChip = signal<ChipValue | null>(null);

        this.bets = {
            player_bet_amount: signal(0),
            banker_bet_amount: signal(0),
            tie_bet_amount: signal(0),
            dragon_7_bet_amount: signal(0),
            panda_bet_amount: signal(0),
        };

        effect(() => {
            const state = this.client.state();
            const usdRate = this.usdRate();
            for (const bet of AllBetAmounts) {
                this.bets[bet].update(clampChipSum(Number(state[bet] ?? '0') / usdRate) * usdRate);
            }
        });

        this.totalBetAmount = signal('0.00');

        this.autobet = {
            autobet_limit: signal('0'),
            on_win: signal('0'),
            on_win_reset: signal(true),
            on_loss: signal('0'),
            on_loss_reset: signal(true),
            stop_on_profit: signal('0.00'),
            stop_on_loss: signal('0.00'),
            autobet_cumulative_payout: signal('0.00'),
        };

        let previousCurrencyCode = this.currency();
        let previousUsdRate = this.usdRate();
        effect(() => {
            const currency = this.currency();
            if (currency !== previousCurrencyCode) {
                const usdRate = untracked(this.usdRate);
                const factor = (1 / previousUsdRate) * usdRate;

                previousCurrencyCode = currency;
                previousUsdRate = usdRate;

                const state = untracked(this.client.state);

                this.#undoStack.length = 0;

                const update = (amount?: string) =>
                    currencyToFixedString(Number(amount ?? '0') * factor, currency);

                this.client.setBetAmounts(
                    AllBetAmounts.reduce(
                        (amounts, bet) => {
                            amounts[bet] = update(state[bet]);
                            return amounts;
                        },
                        {} as Record<BaccaratBetAmount, string>,
                    ),
                );

                this.autobet.stop_on_profit.update(update);
                this.autobet.stop_on_loss.update(update);
            }
        });

        effect(() => {
            const currency = this.currency();
            this.totalBetAmount.update(
                currencyToFixedString(
                    Object.values(this.bets).reduce((total, bet) => total + bet.read(), 0),
                    currency,
                ),
            );
        });

        const updateBets = (bets: Record<BaccaratBetAmount, number>) => {
            const total = Object.values(bets).reduce((sum, amount) => sum + amount, 0);
            const balance = this.gameState().balance;
            const usdRate = this.usdRate();

            if (total > balance) {
                const scale = balance / total;
                let scaledSum = 0;

                for (const bet of AllBetAmounts) {
                    bets[bet] = clampChipSum((bets[bet] * scale) / usdRate) * usdRate;
                    scaledSum += bets[bet];
                }

                if (scaledSum > balance) {
                    const excess = scaledSum - balance;
                    for (const bet of AllBetAmounts) {
                        bets[bet] =
                            clampChipSum((bets[bet] - (bets[bet] / scaledSum) * excess) / usdRate) *
                            usdRate;
                    }
                }
            }

            batch(() => {
                for (const bet of AllBetAmounts) {
                    this.bets[bet].update(bets[bet]);
                }
            });
        };

        this.#setAllBets = (amount) =>
            batch(() => {
                if (amount === 0) {
                    for (const bet of AllBetAmounts) {
                        this.bets[bet].update(0);
                    }
                    return;
                }

                const bets: Record<BaccaratBetAmount, number> = {
                    player_bet_amount: 0,
                    banker_bet_amount: 0,
                    tie_bet_amount: 0,
                    dragon_7_bet_amount: 0,
                    panda_bet_amount: 0,
                };

                for (const bet of AllBetAmounts) {
                    bets[bet] = this.#clampBetAmount(amount, BetAmountToBetLimit[bet]);
                }

                updateBets(bets);
            });

        this.#multiplyAllBets = (multiplier) =>
            batch(() => {
                const bets: Record<BaccaratBetAmount, number> = {
                    player_bet_amount: 0,
                    banker_bet_amount: 0,
                    tie_bet_amount: 0,
                    dragon_7_bet_amount: 0,
                    panda_bet_amount: 0,
                };

                for (const bet of AllBetAmounts) {
                    bets[bet] = this.#clampBetAmount(
                        this.bets[bet].read() * multiplier,
                        BetAmountToBetLimit[bet],
                    );
                }

                updateBets(bets);
            });

        let lastRoundId: string | null = null;

        effect(() => {
            const newState = client.state();

            this.updateGameState((gameState) => {
                if (gameState.isAnimatingState) {
                    return {
                        ...gameState,
                        pendingUpdates: gameState.pendingUpdates.concat([newState]),
                    };
                }

                if (
                    this.isTurboMode ||
                    !newState.round_id ||
                    !newState.action ||
                    !['Bet', 'Autobet', 'Nextbet'].includes(newState.action)
                ) {
                    lastRoundId = null;
                    return {
                        ...gameState,
                        ...newState,
                    };
                }

                const {
                    banker_hand,
                    player_hand,
                    next_actions,
                    round_closed,
                    round_id,
                    autobet_count = 0,
                } = newState;

                const roundId = `${round_id}-${autobet_count}`;
                const isNewRound =
                    lastRoundId !== roundId &&
                    banker_hand.cards.length >= 2 &&
                    player_hand.cards.length >= 2;

                if (!isNewRound) {
                    return {
                        ...gameState,
                        ...newState,
                    };
                }

                lastRoundId = roundId;

                const sidebets = pick(newState, ...BaccaratSidebets);

                return {
                    ...gameState,
                    ...newState,
                    round_closed: false,
                    autobet_count: gameState.autobet_count ?? 0,
                    isAnimatingState: true,
                    next_actions: [],
                    banker_hand: {
                        ...banker_hand,
                        status: 'dealing',
                        cards: [],
                    },
                    player_hand: {
                        ...player_hand,
                        status: 'dealing',
                        cards: [player_hand.cards[0]!],
                    },
                    ...EmptySidebetState,
                    pendingUpdates: gameState.pendingUpdates.concat(
                        (
                            [
                                {
                                    banker_hand: {
                                        ...banker_hand,
                                        status: 'dealing',
                                        cards: [banker_hand.cards[0]!],
                                    },
                                },
                                {
                                    player_hand: {
                                        ...player_hand,
                                        status: 'dealing',
                                        cards: player_hand.cards.slice(0, 2),
                                    },
                                },
                                {
                                    banker_hand: {
                                        ...banker_hand,
                                        status: 'dealing',
                                        cards: banker_hand.cards.slice(0, 2),
                                    },
                                },
                                player_hand.cards.length > 2 || banker_hand.cards.length > 2
                                    ? {}
                                    : null,
                                player_hand.cards.length > 2
                                    ? {
                                          player_hand: {
                                              ...player_hand,
                                              status: 'dealing',
                                              cards: player_hand.cards,
                                          },
                                      }
                                    : null,
                                banker_hand.cards.length > 2
                                    ? {
                                          banker_hand: {
                                              ...banker_hand,
                                              status: 'dealing',
                                              cards: banker_hand.cards,
                                          },
                                      }
                                    : null,
                                {
                                    banker_hand,
                                    player_hand,
                                    ...sidebets,
                                    round_closed,
                                    autobet_count,
                                    next_actions,
                                    isAnimatingState: false,
                                },
                            ] as (typeof gameState)['pendingUpdates']
                        ).filter(Boolean),
                    ),
                };
            });
        });

        const hasNextbet = memo(() => {
            const { isAnimatingState, next_actions } = this.gameState();
            return !isAnimatingState && next_actions.includes('Nextbet');
        });

        const nextbetTimer = timer(2000);
        const turboNextbetTimer = timer(1000);

        this.client.on('roundEnd', () => {
            if (this.client.hasIntent('CloseAutobet')) {
                this.client.closeAutobet().catch(logDevError);
            }
        });

        effect(() => {
            if (hasNextbet()) {
                nextbetTimer.cancel();
                turboNextbetTimer.cancel();
                (this.isTurboMode ? turboNextbetTimer : nextbetTimer)(() => {
                    this.triggerAction('Nextbet');
                });
            }
        });

        let previousReadyState = this.client.readyState;
        let connectionStatusToast: ToastControls | null = null;
        const connectionStatusTimer = timer(1000);

        this.client.on('readyStateChanged', (newReadyState) => {
            if (newReadyState !== previousReadyState) {
                if (newReadyState === 'ready') {
                    connectionStatusTimer.cancel();
                    connectionStatusToast?.dismiss();
                    this.initSession().catch(logDevError);
                } else if (!connectionStatusToast && !untracked(this.gameState).isLoading) {
                    connectionStatusTimer(() => {
                        connectionStatusToast = this.showToast({
                            variant: 'error',
                            content: 'Lost connection. Trying to reconnect...',
                            isDismissable: false,
                            dismiss: undefined,
                            onDismiss() {
                                connectionStatusToast = null;
                            },
                        });
                    });
                }
                previousReadyState = newReadyState;
            }
        });

        this.client.on('beforeGameSessionSwitch', () => {
            const currency = this.currency();
            const limit = this.autobet.autobet_limit.read();
            this.client.setAutobetState({
                ...AllBetAmounts.reduce(
                    (amounts, bet) => {
                        amounts[bet] = currencyToFixedString(this.bets[bet].read(), currency);
                        return amounts;
                    },
                    {} as Record<BaccaratBetAmount, string>,
                ),
                autobet_limit: limit === '∞' ? Infinity : Number(limit),
                on_win: this.autobet.on_win.read(),
                on_win_reset: this.autobet.on_win_reset.read(),
                on_loss: this.autobet.on_loss.read(),
                on_loss_reset: this.autobet.on_loss_reset.read(),
                stop_on_profit: this.autobet.stop_on_profit.read(),
                stop_on_loss: this.autobet.stop_on_loss.read(),
            });
        });

        this.client.onStateReload(() => {
            const { isAutobetActive, on_loss_reset, on_win_reset } = untracked(this.client.state);
            this.activeTab.update(isAutobetActive ? 'auto' : 'manual');

            this.autobet.on_win_reset.update(on_win_reset !== false);
            this.autobet.on_loss_reset.update(on_loss_reset !== false);

            if (isAutobetActive) {
                this.triggerAction('CloseAutobet');
            }
        });

        this.client.on('afterHandle', ({ action }) => {
            if (action === 'CloseAutobet') {
                this.client.signalIdle();
            }
        });
    }

    public get shouldCloseAutobet(): SignalReader<boolean> {
        return this.#pendingIntents.CloseAutobet.read;
    }

    #clampBetAmount(value: number, bet: keyof BaccaratBetAmountLimits): number {
        const { bet_limits, balance } = this.gameState();
        const usdRate = this.usdRate();
        return (
            clampChipSum(
                (Number.isFinite(value)
                    ? Math.min(bet_limits[bet].max, balance, Math.max(0, value))
                    : 0) / usdRate,
            ) * usdRate
        );
    }

    #saveBetAmounts(bets = AllBetAmounts): void {
        const amounts: Partial<Record<BaccaratBetAmount, number>> = {};

        for (const bet of bets) {
            amounts[bet] = this.bets[bet].read();
        }

        this.#undoStack.push(() => {
            this.root.store.batch(() => {
                for (const [bet, amount] of Object.entries(amounts) as [
                    BaccaratBetAmount,
                    number,
                ][]) {
                    this.bets[bet].update(amount);
                }
            });
        });
    }

    public maxBetAmount(betLimit: keyof BaccaratBetAmountLimits): number {
        const { bet_limits, balance } = this.gameState();
        let availableBalance = balance;

        for (const bet of AllBetAmounts) {
            if (BetAmountToBetLimit[bet] != betLimit) {
                availableBalance -= this.bets[bet].read();
            }
        }

        return Math.max(0, Math.min(bet_limits[betLimit].max, availableBalance));
    }

    public triggerAction(action: UIAction, data?: unknown): void {
        const { next_actions, round_closed, readyState, isAnimatingState, isAutobetActive } =
            this.gameState();

        if (readyState !== 'ready') {
            return;
        }

        if (action === 'BetOrStop') {
            action = isAutobetActive
                ? 'CloseAutobet'
                : this.activeTab.read() === 'auto'
                  ? 'Autobet'
                  : 'Bet';
        }

        if (isAnimatingState) {
            if (isAutobetActive && action === 'CloseAutobet') {
                this.client.addIntent({ type: 'CloseAutobet' });
            }
            return;
        }

        const canBet = this.client.can('Bet');

        switch (action) {
            case 'MultiplyAllBets':
                if (typeof data !== 'number' || !canBet) {
                    return;
                }
                this.#saveBetAmounts();
                this.#multiplyAllBets(data);
                break;

            case 'SetAllBets':
                if (typeof data !== 'number' || !canBet) {
                    return;
                }
                this.#saveBetAmounts();
                this.#setAllBets(data * this.usdRate());
                break;

            case 'IncreaseSingleBet': {
                const chip = this.selectedChip.read();
                if (
                    !canBet ||
                    !chip ||
                    typeof data !== 'string' ||
                    !(data in BetAmountToBetLimit)
                ) {
                    return;
                }

                const bet = data as BaccaratBetAmount;

                const previousAmount = this.bets[bet].read();

                const newAmount = Math.min(
                    previousAmount + chipValue(chip) * this.usdRate(),
                    Math.max(0, this.maxBetAmount(BetAmountToBetLimit[bet])),
                );

                if (newAmount === previousAmount) {
                    return;
                }

                const previousLastBet = this.#lastBet;

                this.#undoStack.push(() => {
                    this.#lastBet = previousLastBet;
                    this.bets[bet].update(previousAmount);
                });

                this.#lastBet = bet;
                this.bets[bet].update(newAmount);

                void this.playSound('chip');
                break;
            }

            case 'ClearSingleBet': {
                if (!canBet || typeof data !== 'string' || !(data in BetAmountToBetLimit)) {
                    return;
                }
                const bet = data as BaccaratBetAmount;

                const previousAmount = this.bets[bet].read();

                if (previousAmount === 0) {
                    return;
                }

                const previousLastBet = this.#lastBet;

                this.#undoStack.push(() => {
                    this.#lastBet = previousLastBet;
                    this.bets[bet].update(previousAmount);
                });

                this.#lastBet = bet;
                this.bets[bet].update(0);

                break;
            }

            case 'Bet':
            case 'Autobet': {
                let canBet = next_actions.includes(action);
                if (!canBet && (round_closed || next_actions.length === 0)) {
                    const currency = this.currency();
                    const betAmounts = AllBetAmounts.reduce(
                        (amounts, bet) => {
                            amounts[bet] = currencyToFixedString(this.bets[bet].read(), currency);
                            return amounts;
                        },
                        {} as Record<BaccaratBetAmount, string>,
                    );

                    if (action === 'Autobet') {
                        const limit = this.autobet.autobet_limit.read();
                        this.client.setAutobetState({
                            ...betAmounts,
                            autobet_limit: limit === '∞' ? Infinity : Number(limit),
                            on_win: this.autobet.on_win.read(),
                            on_win_reset: this.autobet.on_win_reset.read(),
                            on_loss: this.autobet.on_loss.read(),
                            on_loss_reset: this.autobet.on_loss_reset.read(),
                            stop_on_profit: this.autobet.stop_on_profit.read(),
                            stop_on_loss: this.autobet.stop_on_loss.read(),
                        });
                    } else {
                        this.client.setBetAmounts(betAmounts);
                    }

                    this.updateGameState((gameState) => ({
                        ...gameState,
                        isAnimatingState: false,
                    }));

                    this.client.reset();

                    canBet = this.gameState().next_actions.includes(action);
                }

                if (!canBet) {
                    return;
                }

                const { bet_limits } = this.gameState();

                const request: Writable<RequestData<'Bet' | 'Autobet'>> = {
                    currency: this.currency(),
                };

                let totalBet = 0;

                for (const bet of MainBetAmounts) {
                    const amount = this.bets[bet].read();
                    if (amount > 0) {
                        request[bet] = amount;
                        totalBet += amount;
                    }
                }

                if (totalBet > 0 && totalBet < bet_limits.bet.min) {
                    this.showToast({
                        content: 'Please place a valid bet amount.',
                        variant: 'error',
                    });
                    return;
                }

                for (const sidebet of SidebetAmounts) {
                    const amount = this.bets[sidebet].read();

                    if (amount > 0) {
                        if (amount < bet_limits[BetAmountToBetLimit[sidebet]].min) {
                            this.showToast({
                                content: `Please place a valid ${SidebetAmountToName[sidebet]} bet amount.`,
                                variant: 'error',
                            });
                            return;
                        }
                        request[sidebet] = amount;
                        totalBet += amount;
                    }
                }

                if (totalBet === 0) {
                    this.showToast({
                        content: 'Please place a valid bet amount.',
                        variant: 'error',
                    });
                    return;
                } else if (totalBet > this.gameState().balance) {
                    this.showToast({
                        content: 'Insufficient balance.',
                        variant: 'error',
                    });
                    return;
                }

                if (action === 'Autobet') {
                    const autobetRequest = {} as Writable<RequestData<'Autobet'>>;
                    const limit = this.autobet.autobet_limit.read();

                    if (limit == '∞') {
                        autobetRequest.autobet_limit = 0;
                    } else {
                        autobetRequest.autobet_limit = Number(limit);
                        if (
                            !Number.isFinite(autobetRequest.autobet_limit) ||
                            autobetRequest.autobet_limit < 0
                        ) {
                            this.showToast({
                                content: 'Please enter a valid number of bets.',
                                variant: 'error',
                            });
                            return;
                        }
                    }

                    for (const key of [
                        'on_win',
                        'on_loss',
                        'stop_on_profit',
                        'stop_on_loss',
                    ] as const) {
                        const value = this.autobet[key].read();
                        autobetRequest[key] = Number(value);
                        if (Number.isFinite(autobetRequest[key])) {
                            autobetRequest[key] = Number(value);
                        }
                    }

                    autobetRequest.on_win_reset = this.autobet.on_win_reset.read();
                    autobetRequest.on_loss_reset = this.autobet.on_loss_reset.read();

                    Object.assign(request, autobetRequest);
                }

                this.client[action.toLowerCase() as 'bet' | 'autobet'](request).catch(logDevError);

                void this.playSound('bet');

                break;
            }
            case 'CloseAutobet':
                if (!isAutobetActive) {
                    return;
                }
                if (next_actions.includes('CloseAutobet')) {
                    void this.client.closeAutobet().catch(logDevError);
                } else {
                    this.client.addIntent({ type: 'CloseAutobet' });
                }
                break;
            case 'Nextbet':
                if (!next_actions.includes('Nextbet')) {
                    return;
                }
                void this.client.nextbet().catch(logDevError);
                break;
            case 'UndoBet':
                if (!canBet || this.#undoStack.length === 0) {
                    return;
                }
                this.#undoStack.pop()!();
                break;
            default:
                logDevError(`Unexpected action: ${String(action)}`);
                return;
        }
    }

    public get toastBarControlsRef(): RefObject<ToastBarControls> {
        return this.#toastBarControlsRef;
    }

    public get notificationsControlsRef(): RefObject<ToastBarControls> {
        return this.#notificationsControlsRef;
    }

    public get hotkeysModalControlsRef(): RefObject<ModalControls> {
        return this.#hotkeysModalControlsRef;
    }

    public get helpModalControlsRef(): RefObject<ModalControls> {
        return this.#helpModalControlsRef;
    }

    public get provablyFairModalControlsRef(): RefObject<ModalControls> {
        return this.#provablyFairModalControlsRef;
    }

    public showToast(toast: ToastProps): ToastControls | null {
        if (this.#toastBarControlsRef.current) {
            return this.#toastBarControlsRef.current.showToast({
                dismiss: { after: 2000 },
                ...toast,
            });
        }
        return null;
    }

    public showNotification(toast: ToastProps): ToastControls | null {
        if (this.#notificationsControlsRef.current) {
            return this.#notificationsControlsRef.current.showToast({
                dismiss: { after: 2500 },
                isDismissable: false,
                ...toast,
            });
        }
        return null;
    }

    public async verifySeeds(params: VerifySeedParams): Promise<FinalState> {
        return this.client.verifySeeds(params);
    }
}
