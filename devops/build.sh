#!/bin/bash

set -eux

use_fnm() {
    eval "$(/usr/local/bin/fnm env --shell bash)"
}

prepare() {
    curl -fsSL https://fnm.vercel.app/install | bash -s -- --install-dir /usr/local/bin --skip-shell

    use_fnm
    fnm install --corepack-enabled
    fnm use

    npm install --global corepack@latest

    corepack install
}

install() {
    use_fnm
    pnpm install --frozen-lockfile
}

build() {
    use_fnm
    pnpm build
    mkdir standalone
    for game in games/*; do
        if [ -f $game/deploy/standalone.html ]; then
            cp $game/deploy/standalone.html standalone/$(basename $game).html
        fi
    done
    rm -rf games/*/deploy
}

case "${1:-build}" in
prepare)
    prepare
    ;;
install)
    install
    ;;
*)
    build
    ;;
esac
