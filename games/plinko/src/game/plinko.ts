import {
    APIClient,
    createWebSocketTransport,
    currencyToFixedString,
    GameClient,
    GameError,
    isGameUpdate,
    merge,
    pick,
    RetryError,
    tryParseFloat,
    validateObject,
    type AnyState,
    type BetAmountLimits,
    type GameActionName,
    type GameState,
    type MessageName,
    type RequestData,
    type VerifySeedParams,
    type Writable,
} from '@monkey-tilt/client';
import type { Root, SignalReader, SignalUpdater } from '@monkey-tilt/ui';
import { deepMerge, logDevError } from '@monkey-tilt/utils';
import {
    DEFAULT_MULTIPLIERS_CHANCES_CONFIG,
    DEFAULT_RTP,
    type AutobetState,
    type FinalState,
    type PlinkoBetAmountLimits,
    type PlinkoMultipliersChancesMap,
    type PlinkoRisk,
    type PlinkoState,
} from './state';

export interface PlinkoClientOptions {
    readonly root: Root;
    readonly gameId: string;
    readonly gatewayUrl: string | URL;
    readonly websocketUrl?: string | URL;
    readonly gameSessionId: string;
    readonly currency: string;
}

interface PlinkoGameOptions extends PlinkoClientOptions {
    readonly apiClient: APIClient;
    readonly betAmountLimits: PlinkoBetAmountLimits;
    readonly multipliersChancesMap: PlinkoMultipliersChancesMap;
    readonly rtp: number;
}

export async function createPlinkoClient(options: PlinkoClientOptions): Promise<PlinkoClient> {
    const apiClient = new APIClient(options.gatewayUrl);
    apiClient.gameId = options.gameId;
    apiClient.gameSessionId = options.gameSessionId;

    return new Plinko({
        ...options,
        apiClient,
        betAmountLimits: deepMerge<BetAmountLimits>(
            {
                bet: {
                    min: 0,
                    max: 5000,
                    max_payout: 100_000,
                },
            },
            await apiClient.getBetLimits(),
        ) as PlinkoBetAmountLimits,
        multipliersChancesMap:
            (await apiClient.getMultipliers()) ?? DEFAULT_MULTIPLIERS_CHANCES_CONFIG,
        rtp: (await apiClient.getRTP()) ?? DEFAULT_RTP,
    });
}

export interface PlinkoClient extends GameClient<PlinkoState> {
    readonly state: SignalReader<PlinkoState>;
    onStateReload(handler: () => void): void;
    bet(request: RequestData<'Bet'>): Promise<void>;
    autobet(request: RequestData<'Autobet'>): Promise<void>;
    nextbet(): Promise<void>;
    closeAutobet(): Promise<void>;
    updateBalance(): void;
    incrementBalance(balance: number): void;
    setBetState(
        amounts: Partial<Pick<PlinkoState, 'bet_amount' | 'number_of_rows' | 'risk'>>,
    ): void;
    setAutobetState(
        autobet: Partial<
            AutobetState & Pick<PlinkoState, 'bet_amount' | 'number_of_rows' | 'risk'>
        >,
    ): void;
    reload(): Promise<GameState>;
    verifySeeds(params: VerifySeedParams): Promise<FinalState>;
}

class Plinko extends GameClient<PlinkoState> implements PlinkoClient {
    #getState: SignalReader<PlinkoState>;
    #setState: SignalUpdater<PlinkoState>;

    #stateReloadHandlers = new Set<() => void>();

    #updateBalance: () => Promise<void>;
    #updateBetLimits: () => Promise<void>;
    #updateMultipliers: () => Promise<void>;
    #updateMaxMultiplier: () => Promise<void>;
    #updateRTP: () => Promise<void>;

    public constructor({
        root,
        gameId,
        gatewayUrl,
        websocketUrl,
        gameSessionId,
        currency,
        apiClient,
        betAmountLimits,
        rtp,
        multipliersChancesMap,
    }: PlinkoGameOptions) {
        super({
            root,
            gameId,
            gameSessionId,
            currency,
            gatewayUrl,
            apiClient,
            transport: createWebSocketTransport({
                gameSessionId,
                gatewayUrl,
                websocketUrl,
            }),
            syncableActions: () => false,
            actionTimeout: 2000,
            autoRetryAttempts: 5,
        });

        const { signal, effect, memo } = this.root.store;

        [this.#getState, this.#setState] = signal<PlinkoState>({
            balance: 0,
            bet_limits: betAmountLimits,
            max_multiplier: 1000,
            error: null,
            isLoading: true,
            readyState: 'closed',
            isAuthenticated: false,
            round_id: '',
            on_loss_reset: true,
            on_win_reset: true,
            autobet_limit: 0,
            bet_amount: '0.00',
            number_of_rows: 10,
            risk: 0,
            next_actions: [],
            isAutobetActive: false,
            rtp,
            multipliersChancesMap,
        });

        this.#updateBalance = this.#createUpdater('balance', () =>
            this.api.getBalance().then(({ balance }) => balance),
        );

        const usdRate = memo(() => this.session().usdRate);
        const usdBetLimits = signal(betAmountLimits);

        this.#updateBetLimits = async () => {
            const limits = await this.api.getBetLimits();
            usdBetLimits.update(
                (prevLimits) =>
                    deepMerge<BetAmountLimits>(prevLimits, limits) as PlinkoBetAmountLimits,
            );
        };

        this.#updateMultipliers = this.#createUpdater('multipliersChancesMap', () =>
            this.api.getMultipliers().then((multipliers) => multipliers),
        );
        this.#updateMaxMultiplier = this.#createUpdater('max_multiplier', () =>
            this.api
                .getConfigCustomProps({ max_multiplier: 'string?' })
                .then(({ max_multiplier }) => tryParseFloat(max_multiplier) || 1000),
        );

        this.#updateRTP = this.#createUpdater('rtp', async () => {
            const rtp = await this.api.getRTP();
            return (previousRTP) => rtp ?? previousRTP;
        });

        effect(() => {
            const limits = usdBetLimits.read();
            const factor = usdRate();
            this.#setState((state) => ({
                ...state,
                bet_limits: {
                    bet: {
                        min: limits.bet.min * factor,
                        max: limits.bet.max * factor,
                        max_payout: limits.bet.max_payout * factor,
                    },
                },
            }));
        });

        const prepareNextRound = () => {
            this.#updateBetLimits().catch(logDevError);
            this.#updateRTP().catch(logDevError);
            this.#updateMultipliers().catch(logDevError);
            this.#updateMaxMultiplier().catch(logDevError);
        };

        prepareNextRound();
        this.#updateBalance().catch(logDevError);

        this.on('roundEnd', prepareNextRound);

        this.on('readyStateChanged', (readyState) => {
            this.#setState((state) => ({
                ...state,
                isLoading: !(state.isLoading && readyState === 'ready'),
                readyState,
                isAuthenticated: this.isAuthenticated,
            }));
        });

        this.on('error', (event) => {
            let error: unknown = event.error;

            if (error instanceof RetryError) {
                error = error.reason;
            }

            if (error instanceof GameError) {
                this.#setState((state) => {
                    return {
                        ...state,
                        next_actions:
                            error.code === 'INSUFFICIENT_FUNDS'
                                ? this.roundStartActions
                                : state.next_actions,
                        error,
                    };
                });
            }
        });
    }

    protected updateState(value: PlinkoState | ((prevValue: PlinkoState) => PlinkoState)): void {
        return this.#setState(value);
    }

    public get state(): SignalReader<PlinkoState> {
        return this.#getState;
    }

    public onStateReload(handler: () => void): void {
        this.#stateReloadHandlers.add(handler);
    }

    public override can<A extends MessageName>(action: A): boolean {
        if (super.can(action)) {
            return true;
        }
        return (
            this.roundStartActions.includes(action as GameActionName) &&
            this.#getState().next_actions.length === 0
        );
    }

    #validateBetAmount(request: RequestData<'Bet'>): { balance: number; betAmount: number } {
        const state = this.#getState();

        const betAmount = request.bet_amount ?? 0;
        const balance = state.balance - betAmount;

        if (balance < 0) {
            throw new GameError('Insufficient funds', 'INSUFFICIENT_FUNDS', '');
        }

        const number_of_rows = request.number_of_rows ?? 16;
        const risk: PlinkoRisk = state.risk ?? 2;

        const highestMultiplierStr =
            state.multipliersChancesMap[state.rtp]?.[risk]?.[number_of_rows]?.[0]?.[0];

        const highestMultiplier = highestMultiplierStr
            ? parseFloat(highestMultiplierStr)
            : state.max_multiplier;

        const maxBetAmount = Math.min(
            state.bet_limits.bet.max,
            state.bet_limits.bet.max_payout / highestMultiplier,
        );

        if (betAmount > maxBetAmount) {
            throw new GameError('Bet amount exceeds maximum limit', 'BET_AMOUNT_EXCEEDS_LIMIT', '');
        }

        return { balance, betAmount };
    }

    public async bet(request: RequestData<'Bet'>): Promise<void> {
        if (this.can('Bet')) {
            const { betAmount, balance } = this.#validateBetAmount(request);
            this.#setState((state) => ({
                ...state,
                bet_amount: currencyToFixedString(betAmount, this.session().currency),
                balance,
                round_closed: false,
            }));
        }

        return this.#send(
            'Bet',
            request,
            (error) => !(error instanceof GameError) || error.code !== 'INSUFFICIENT_FUNDS',
        );
    }

    public async autobet(request: RequestData<'Autobet'>): Promise<void> {
        if (this.can('Autobet')) {
            this.removeIntent('CloseAutobet');

            const { betAmount, balance } = this.#validateBetAmount(request);
            this.#setState((state) => ({
                ...state,
                bet_amount: currencyToFixedString(betAmount, this.session().currency),
                balance,
                isAutobetActive: true,
                autobet_limit:
                    request.autobet_limit != undefined && Number.isFinite(request.autobet_limit)
                        ? request.autobet_limit
                        : 0,
                autobet_count: 0,
                on_win_reset: request.on_win_reset ?? true,
                on_win: (request.on_win ?? 0).toFixed(0),
                on_loss_reset: request.on_loss_reset ?? true,
                on_loss: (request.on_loss ?? 0).toFixed(0),
                stop_on_profit: (request.stop_on_profit ?? 0).toFixed(2),
                stop_on_loss: (request.stop_on_loss ?? 0).toFixed(2),
                round_closed: false,
            }));
        }

        return this.#send(
            'Autobet',
            request,
            (error) => !(error instanceof GameError) || error.code !== 'INSUFFICIENT_FUNDS',
        ).catch(() => {
            this.#setState((state) => ({ ...state, isAutobetActive: false }));
        });
    }

    public nextbet(): Promise<void> {
        if (this.hasIntent('CloseAutobet')) {
            return this.closeAutobet();
        }
        if (this.can('Nextbet')) {
            const { bet_amount = '0.00', balance } = this.#getState();

            const newBalance = balance - Number.parseFloat(bet_amount);
            if (newBalance < 0) {
                this.closeAutobet().catch(logDevError);
                return Promise.reject(
                    new GameError('Insufficient funds', 'INSUFFICIENT_FUNDS', ''),
                );
            }

            this.#setState((state) => ({
                ...state,
                balance: newBalance,
                round_closed: false,
            }));
        }
        return this.#send('Nextbet');
    }

    public updateBalance(): void {
        this.#updateBalance().catch(logDevError);
    }

    public incrementBalance(payout: number): void {
        this.#setState((state) => ({
            ...state,
            balance: state.balance + payout,
        }));
    }

    public closeAutobet(): Promise<void> {
        this.removeIntent('CloseAutobet');
        return this.#send('CloseAutobet');
    }

    public setBetState(
        amounts: Partial<Pick<PlinkoState, 'bet_amount' | 'number_of_rows' | 'risk'>>,
    ): void {
        this.#setState((state) => ({
            ...state,
            ...amounts,
        }));
    }

    public setAutobetState(
        autobet: Partial<
            AutobetState & Pick<PlinkoState, 'bet_amount' | 'number_of_rows' | 'risk'>
        >,
    ): void {
        this.#setState((state) => ({
            ...state,
            ...autobet,
        }));
    }

    public override async reload(): Promise<GameState> {
        this.#setState((state) => ({ ...state, error: null }));
        return super.reload();
    }

    public override get roundStartActions(): GameActionName[] {
        return ['Bet', 'Autobet'];
    }

    public override reset(): void {
        super.reset();

        const { isAuthenticated } = this;

        this.#setState((state) => ({
            ...state,
            error: null,
            next_actions: isAuthenticated ? this.roundStartActions : [],
            isAuthenticated,
        }));
    }

    async #send(
        action: GameActionName,
        request: RequestData<GameActionName>,
        shouldRetry?: (error: Error) => boolean,
    ): Promise<void> {
        this.#setState((state) => ({
            ...state,
            error: null,
            next_actions: [],
        }));
        await this.sendAndAwait(action, request, shouldRetry);
    }

    protected override handleUpdate(state: AnyState): void {
        if (state.type === 'Authenticate') {
            this.reset();
            return;
        }

        if (state.type === 'GameState') {
            const resetState = state.data as Writable<PlinkoState>;

            if (state.round_id) {
                resetState.round_id = state.round_id;
            }

            resetState.bet_amount = currencyToFixedString(
                resetState.bet_amount !== undefined ? Number.parseFloat(resetState.bet_amount) : 0,
                this.session().currency,
            );

            this.allowedActions = new Set(resetState.next_actions);

            this.#setState((currentState) => ({
                ...currentState,
                ...resetState,
                round_id: state.round_id ?? currentState.round_id,
                round_closed: resetState.round_closed == true,
                isAutobetActive: resetState.next_actions.includes('CloseAutobet'),
                isAuthenticated: true,
            }));

            if (resetState.round_closed) {
                this.signalRoundEnd();
            }

            void Promise.resolve().then(() => {
                for (const handler of this.#stateReloadHandlers) {
                    try {
                        handler();
                    } catch {
                        // ignore
                    }
                }
            });

            return;
        }

        if (!isGameUpdate(state)) {
            return;
        }

        const newState = structuredClone(this.#getState()) as Writable<PlinkoState>;
        let isNewRound = false;

        if (state.round_id) {
            isNewRound = state.round_id !== newState.round_id;
            newState.round_id = state.round_id;
        }

        newState.action = state.action;
        newState.isAuthenticated = this.isAuthenticated;
        newState.next_actions =
            state.data.next_actions.length === 0 && state.data.round_closed
                ? this.roundStartActions
                : state.data.next_actions;
        this.allowedActions = new Set(newState.next_actions);

        if (state.data.bucket && isNewRound) {
            newState.dropped_ball = {
                id: newState.round_id,
                risk: newState.risk ?? 0,
                bucketNumber: state.data.bucket + 1,
                payout: state.data.total_payout ?? '0.00',
                number_of_rows: newState.number_of_rows ?? 10,
            };
        }

        if (isNewRound) {
            this.#setState({
                ...newState,
                round_closed: false,
            });
            // N.B. due to removal of Open step, we have to split state update
            // into two parts to allow UI to properly clean up the previous round
            void Promise.resolve().then(() => {
                this.handleUpdate(state);
            });
            return;
        }

        merge(
            newState,
            pick(
                state.data,
                'bet_amount',
                'autobet_count',
                'round_closed',
                'bucket',
                'total_payout',
            ),
        );

        if (
            (state.action === 'Autobet' || state.action === 'Nextbet') &&
            (newState.autobet_limit ?? 0) > 0
        ) {
            (newState.autobet_limit as number)--;
        }

        if (state.data.round_closed) {
            newState.isAutobetActive = newState.next_actions.includes('CloseAutobet');
        }

        if (newState.round_closed || newState.isAutobetActive) {
            this.signalRoundEnd();
        }

        this.#setState(newState);
    }

    #createUpdater<T extends keyof PlinkoState>(
        key: T,
        updater: () => Promise<
            PlinkoState[T] | ((previousValue: PlinkoState[T]) => PlinkoState[T])
        >,
    ): () => Promise<void> {
        let isFetching = false;
        return async () => {
            if (isFetching) {
                return;
            }

            isFetching = true;
            try {
                const value = await updater();
                this.#setState((state) => ({
                    ...state,
                    [key]: typeof value === 'function' ? value(state[key]) : value,
                }));
            } catch (error) {
                logDevError(`Failed to fetch ${key}:`, error);
            } finally {
                isFetching = false;
            }
        };
    }

    protected validateSeedVerificationData(data: unknown): boolean {
        return validateObject<FinalState>(data, {
            bucket: 'number',
            risk: 'number',
            number_of_rows: 'number',
            metadata: {
                properties: {
                    rtp: 'number',
                },
            },
        });
    }

    public async verifySeeds(params: VerifySeedParams): Promise<FinalState> {
        return (await this._validateSeeds(params)) as FinalState;
    }
}
