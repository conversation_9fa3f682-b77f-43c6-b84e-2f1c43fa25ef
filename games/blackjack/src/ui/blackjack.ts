import {
    currencyToFixedString,
    GameUI,
    timer,
    type RequestData,
    type VerifySeedParams,
    type Writable,
} from '@monkey-tilt/client';
import {
    type ModalControls,
    type RefObject,
    type Root,
    type Signal,
    type SignalReader,
    type ToastBarControls,
    type ToastControls,
    type ToastProps,
} from '@monkey-tilt/ui';
import { logDevError } from '@monkey-tilt/utils';
import type { BlackjackClient } from '../game/blackjack';
import type { BlackjackState, CardInfo } from '../game/state';
import type { UIAction } from './actions';
import { keyboardShortcuts } from './input/keyboard';

import effects from './assets/effects.json';
import effectsMP3 from './assets/effects.mp3';
import effectsOGG from './assets/effects.ogg';
import effectsWEBM from './assets/effects.webm';

export type SoundEffect = keyof typeof effects;

type Config = {
    readonly soundEffects: boolean;
    readonly hotkeys: boolean;
    readonly turboMode: boolean;
};

export interface BlackjackUIOptions {
    /**
     * The base URL used to load assets from.
     */
    readonly assetsUrl: URL;

    /**
     * The Blackjack GameClient instance.
     */
    readonly client: BlackjackClient;

    readonly root: Root;
}

export interface GameState extends BlackjackState {
    readonly dealtInitialCards: boolean;
}

export class BlackjackUI extends GameUI<GameState, BlackjackClient, UIAction, Config, SoundEffect> {
    #toastBarControlsRef: RefObject<ToastBarControls> = { current: null };
    #hotkeysModalControlsRef: RefObject<ModalControls> = { current: null };
    #provablyFairModalControlsRef: RefObject<ModalControls> = { current: null };

    #lastAction: Signal<UIAction | null>;

    public readonly activeTab: Signal<'standard' | 'sidebet'>;

    public readonly currency: SignalReader<string>;
    public readonly usdRate: SignalReader<number>;

    public readonly betAmountValue: Signal<string>;
    public readonly perfectPairAmountValue: Signal<string>;
    public readonly pokerAmountValue: Signal<string>;

    public constructor({ client, assetsUrl, root }: BlackjackUIOptions) {
        const initialState = client.state();

        super({
            client,
            assetsUrl,
            root,
            keyboardShortcuts,
            config: {
                soundEffects: true,
                hotkeys: false,
                turboMode: window.matchMedia(`(prefers-reduced-motion: reduce)`).matches,
            },
            soundEffects: {
                sources: [
                    { src: new URL(effectsWEBM, assetsUrl).href, type: 'audio/webm; codecs=opus' },
                    { src: new URL(effectsOGG, assetsUrl).href, type: 'audio/ogg; codecs=vorbis' },
                    { src: new URL(effectsMP3, assetsUrl).href, type: 'audio/mpeg' },
                ],
                sprites: effects,
            },
            initialState: {
                ...initialState,
                dealtInitialCards:
                    !initialState.round_closed && initialState.dealer_hand.cards.length >= 2,
            },
        });

        const { signal, effect, memo, untracked } = this.root.store;

        this.activeTab = signal<'standard' | 'sidebet'>('standard');

        this.currency = memo(() => this.client.session().currency);
        this.usdRate = memo(() => this.client.session().usdRate);

        this.betAmountValue = signal('0.00');
        this.perfectPairAmountValue = signal('0.00');
        this.pokerAmountValue = signal('0.00');

        this.#lastAction = signal<UIAction | null>(null);

        let previousCurrencyCode = this.currency();
        let previousUsdRate = this.usdRate();
        effect(() => {
            const currency = this.currency();
            if (currency !== previousCurrencyCode) {
                const usdRate = untracked(this.usdRate);
                const factor = (1 / previousUsdRate) * usdRate;

                previousCurrencyCode = currency;
                previousUsdRate = usdRate;

                [this.betAmountValue, this.perfectPairAmountValue, this.pokerAmountValue].forEach(
                    (signal) => {
                        signal.update((value) =>
                            currencyToFixedString(
                                Number.parseFloat(value ?? '0.00') * factor,
                                currency,
                            ),
                        );
                    },
                );
            }
        });

        effect(() => {
            const newState = client.state();

            this.updateGameState((gameState) => {
                if (gameState.isAnimatingState) {
                    return {
                        ...gameState,
                        pendingUpdates: gameState.pendingUpdates.concat([newState]),
                    };
                }

                const lastAction = untracked(this.#lastAction.read);

                if (
                    !this.isTurboMode &&
                    lastAction === 'Bet' &&
                    !gameState.dealtInitialCards &&
                    newState.dealer_hand.cards.length == 2 &&
                    newState.player_hands.length == 1 &&
                    newState.player_hands[0]!.cards.length == 2
                ) {
                    const dealer_hand = newState.dealer_hand;
                    const player_hand = newState.player_hands[0]!;

                    return {
                        ...gameState,
                        ...newState,
                        round_closed: false,
                        // sequentially deal first 4 cards:
                        isAnimatingState: true,
                        dealtInitialCards: true,
                        dealer_hand: {
                            ...dealer_hand,
                            cards: [],
                        },
                        player_hands: [
                            {
                                ...player_hand,
                                status: 'playing',
                                cards: [player_hand.cards[0]!],
                            },
                        ],
                        pendingUpdates: gameState.pendingUpdates.concat([
                            {
                                dealer_hand: {
                                    ...dealer_hand,
                                    cards: [dealer_hand.cards[0]!],
                                },
                            },
                            {
                                player_hands: [
                                    {
                                        ...player_hand,
                                        status: 'playing',
                                    },
                                ],
                            },
                            {
                                player_hands: [player_hand],
                                dealer_hand,
                                round_closed: newState.round_closed == true,
                            },
                        ]),
                    };
                }

                return {
                    ...gameState,
                    ...newState,
                    dealtInitialCards: newState.round_closed
                        ? false
                        : gameState.dealtInitialCards || newState.dealer_hand.cards.length >= 2,
                };
            });
        });

        let previousReadyState = this.client.readyState;
        let connectionStatusToast: ToastControls | null = null;
        const connectionStatusTimer = timer(1000);

        this.client.on('readyStateChanged', (newReadyState) => {
            if (newReadyState !== previousReadyState) {
                if (newReadyState === 'ready') {
                    connectionStatusTimer.cancel();
                    connectionStatusToast?.dismiss();
                    this.initSession().catch(logDevError);
                } else if (!connectionStatusToast && !untracked(this.gameState).isLoading) {
                    connectionStatusTimer(() => {
                        connectionStatusToast = this.showToast({
                            variant: 'error',
                            content: 'Lost connection. Trying to reconnect...',
                            isDismissable: false,
                            dismiss: undefined,
                            onDismiss() {
                                connectionStatusToast = null;
                            },
                        });
                    });
                }
                previousReadyState = newReadyState;
            }
        });

        client.onStateReload(() => {
            const { poker_bet_amount = '0.00', perfect_pairs_amount = '0.00' } = client.state();

            if (parseFloat(poker_bet_amount) > 0 || parseFloat(perfect_pairs_amount) > 0) {
                this.activeTab.update('sidebet');
            }
        });
    }

    public get lastAction(): SignalReader<UIAction | null> {
        return this.#lastAction.read;
    }

    #clampBetAmount(value: number): number {
        const { bet_limits, balance } = this.gameState();
        return Number.isFinite(value)
            ? Math.min(bet_limits.bet.max, balance, Math.max(0, value))
            : 0;
    }

    public triggerAction(action: UIAction, data?: unknown): void {
        const { next_actions, hand_owner, round_closed, readyState, isAnimatingState } =
            this.gameState();

        if (
            isAnimatingState ||
            readyState !== 'ready' ||
            (hand_owner !== 'Player' && !round_closed)
        ) {
            return;
        }
        switch (action) {
            case 'MultiplyBetAmount':
                if (next_actions.length == 0 || next_actions.includes('Bet')) {
                    if (typeof data === 'number') {
                        this.betAmountValue.update((value) =>
                            currencyToFixedString(
                                this.#clampBetAmount(Number.parseFloat(value) * data),
                                this.currency(),
                            ),
                        );
                    }
                }
                break;
            case 'SetBetAmount':
                if (next_actions.length == 0 || next_actions.includes('Bet')) {
                    if (typeof data === 'number') {
                        this.betAmountValue.update(
                            currencyToFixedString(
                                this.#clampBetAmount(data) * this.usdRate(),
                                this.currency(),
                            ),
                        );
                    }
                }
                break;
            case 'Insure':
                if (next_actions.includes('Peek')) {
                    if (typeof data === 'boolean') {
                        void this.client.peek({ accepted_insurance: data }).catch(logDevError);
                    }
                }
                break;
            case 'Bet':
                let canBet = next_actions.includes('Bet');
                if (!canBet && (round_closed || next_actions.length === 0)) {
                    this.client.setBetAmounts({
                        bet_amount: this.betAmountValue.read(),
                        perfect_pairs_amount: this.perfectPairAmountValue.read(),
                        poker_bet_amount: this.pokerAmountValue.read(),
                    });
                    this.client.reset();
                    canBet = this.gameState().next_actions.includes('Bet');
                }
                if (canBet) {
                    const bet_amount = Number.parseFloat(this.betAmountValue.read());

                    const { bet_limits } = this.gameState();

                    if (!Number.isFinite(bet_amount) || bet_amount <= bet_limits.bet.min) {
                        this.showToast({
                            content: 'Please enter a valid bet amount.',
                            variant: 'error',
                        });
                        return;
                    }
                    const request: Writable<RequestData<'Bet'>> = {
                        bet_amount,
                        currency: this.currency(),
                    };

                    if (this.activeTab.read() === 'sidebet') {
                        const perfect_pairs_amount = Number.parseFloat(
                            this.perfectPairAmountValue.read(),
                        );

                        if (perfect_pairs_amount > 0) {
                            if (perfect_pairs_amount < bet_limits.perfect_pairs.min) {
                                this.showToast({
                                    content: 'Please enter a valid Perfect Pairs bet amount.',
                                    variant: 'error',
                                });
                                return;
                            }
                            request.perfect_pairs_amount = perfect_pairs_amount;
                        }

                        const poker_bet_amount = Number.parseFloat(this.pokerAmountValue.read());

                        if (poker_bet_amount > 0) {
                            if (poker_bet_amount < bet_limits.poker.min) {
                                this.showToast({
                                    content: 'Please enter a valid 21 + 3 bet amount.',
                                    variant: 'error',
                                });
                                return;
                            }
                            request.poker_bet_amount = poker_bet_amount;
                        }
                    }

                    void this.client.bet(request).catch(logDevError);
                    void this.playSound('bet');
                }
                break;
            default:
                if (next_actions.includes(action)) {
                    void this.client.action(action).catch(logDevError);
                }
        }

        this.#lastAction.update(action);
    }

    public get toastBarControlsRef(): RefObject<ToastBarControls> {
        return this.#toastBarControlsRef;
    }

    public get hotkeysModalControlsRef(): RefObject<ModalControls> {
        return this.#hotkeysModalControlsRef;
    }

    public get provablyFairModalControlsRef(): RefObject<ModalControls> {
        return this.#provablyFairModalControlsRef;
    }

    public showToast(toast: ToastProps): ToastControls | null {
        if (this.#toastBarControlsRef.current) {
            return this.#toastBarControlsRef.current.showToast({
                dismiss: { after: 2000 },
                ...toast,
            });
        }
        return null;
    }

    public async verifySeeds(params: VerifySeedParams): Promise<CardInfo[]> {
        return this.client.verifySeeds(params);
    }
}
