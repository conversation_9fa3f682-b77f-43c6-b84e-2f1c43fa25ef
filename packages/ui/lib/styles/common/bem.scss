@use 'sass:map';
@use 'sass:meta';
@use 'sass:string';

$-blocks: (
    'scope': 's',
    'layout': 'l',
    'module': 'm',
    'utility': 'u',
);
$-element-separator: '__';
$-modifier-separator: '--';

///
/// Returns a BEM selector based on the given block, element, and modifier.
///
/// @param {String} $block    - The block name.
/// @param {String} $element  - The element name, defaults to null. If not set, or is set to
///                             an empty string, no element is added to the selector.
/// @param {String} $modifier - The modifier name, defaults to null. If not set, or is set to
///                             an empty string or 'default', no modifier is added to the selector.
///
@function selector($block, $element: null, $modifier: null) {
    $selector: $block;
    @if not string.index($selector, '.') and not string.index($selector, '&') {
        $selector: namespaced-selector($selector);
    }
    @if $element and $element != '' {
        $selector: '#{$selector}#{$-element-separator}#{$element}';
    }
    @if $modifier and $modifier != 'default' and $modifier != '' {
        $selector: '#{$selector}#{$-modifier-separator}#{$modifier}';
    }
    @return $selector;
}

///
/// Turns the given selector (that must not start with a dot) into a namespaced class selector.
///
@function namespaced-selector($selector) {
    @return '.#{lib-config(ns)}#{$selector}';
}

///
/// Wraps the content in a BEM modifier of a current selector.
///
/// If the $modifier is 'default' or an empty string, the content is emitted
/// as is, without being wrapped in a nested class.
///
/// @param {String} $modifier - The modifier name to apply (without '--').
///
@mixin modifier($modifier, $block: '&') {
    @if not meta.content-exists() {
        @error 'The bem.modifier() mixin must be called with the content to wrap.';
    }
    $selector: selector($block, $modifier: $modifier);
    @if $selector != '&' {
        #{$selector} {
            @content;
        }
    } @else {
        @content;
    }
}

///
/// Wraps the content in a BEM element of a current selector.
///
/// If the $element is 'default' or an empty string, the content is emitted
/// as is, without being wrapped in a nested class.
///
/// @param {String} $element - The element name to apply (without '__').
///
@mixin element($element, $block: '&') {
    @if not meta.content-exists() {
        @error 'The bem.element() mixin must be called with the content to wrap.';
    }
    $selector: selector($block, $element: $element);
    @if $selector != '&' {
        #{$selector} {
            @content;
        }
    } @else {
        @content;
    }
}

///
/// Wraps the content in a BEMIT block of a given type and name.
///
/// Allowed types are: 'scope', 'layout', 'module', and 'utility'.
///
/// @param {String} $type - The type of the block to wrap the content in.
/// @param {String} $name - The name of the block to wrap the content in.
///
@mixin block($type, $name) {
    @if not meta.content-exists() {
        @error 'The bem.block() mixin must be called with the content to wrap.';
    }
    #{block-selector($type, $name)} {
        @content;
    }
}

///
/// Returns a BEMIT block selector based on the given type and name.
///
/// Allowed types are: 'scope', 'layout', 'module', and 'utility'.
///
/// @param {String} $type - The type of the block to return the selector for.
/// @param {String} $name - The name of the block to return the selector for.
/// @param {String} $element  - The element name, defaults to null. If not set, or is set to
///                             an empty string, no element is added to the selector.
/// @param {String} $modifier - The modifier name, defaults to null. If not set, or is set to
///                             an empty string or 'default', no modifier is added to the selector.
///
@function block-selector($type, $name, $element: null, $modifier: null) {
    @if not map.has-key($-blocks, $type) {
        @error 'Invalid block type: #{$type}, expected one of: #{map.keys($-blocks)}.';
    }
    @return selector(
        '#{map.get($-blocks, $type)}-#{$name}', 
        $element: $element, 
        $modifier: $modifier,
    );
}

///
/// Wraps the content in a BEMIT scope block of a given name.
///
/// @param {String} $name - The name of the scope block to wrap the content in.
///
@mixin scope($name) {
    @if not meta.content-exists() {
        @error 'The bem.scope() mixin must be called with the content to wrap.';
    }
    @include block('scope', $name) {
        @content;
    }
}

///
/// Wraps the content in a BEMIT module block of a given name.
///
/// @param {String} $name - The name of the module block to wrap the content in.
///
@mixin module($name) {
    @if not meta.content-exists() {
        @error 'The bem.module() mixin must be called with the content to wrap.';
    }
    @include block('module', $name) {
        @content;
    }
}

///
/// Wraps the content in a BEMIT layout block of a given name.
///
/// @param {String} $name - The name of the layout block to wrap the content in.
///
@mixin layout($name) {
    @if not meta.content-exists() {
        @error 'The bem.layout() mixin must be called with the content to wrap.';
    }
    @include block('layout', $name) {
        @content;
    }
}

///
/// Wraps the content in a BEMIT utility block of a given name.
///
/// @param {String} $name - The name of the utility block to wrap the content in.
///
@mixin utility($name) {
    @if not meta.content-exists() {
        @error 'The bem.utility() mixin must be called with the content to wrap.';
    }
    @include block('utility', $name) {
        @content;
    }
}
