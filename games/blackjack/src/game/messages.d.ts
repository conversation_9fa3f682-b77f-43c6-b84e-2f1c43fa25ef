import type { <PERSON>ActionName } from '@monkey-tilt/client';
import type { BlackjackState, HandOwner } from './state';

interface GameStateUpdate extends Partial<BlackjackState> {
    readonly next_actions: ReadonlyArray<GameActionName>;
    readonly hand_owner: HandOwner;
    readonly hand_index: number;
}

declare module '@monkey-tilt/client' {
    export interface MessageTypes {
        readonly Hit: {
            readonly request: void;
            readonly response: GameStateUpdate;
        };

        readonly Split: {
            readonly request: void;
            readonly response: GameStateUpdate;
        };

        readonly Stand: {
            readonly request: void;
            readonly response: GameStateUpdate;
        };

        readonly Peek: {
            readonly request: {
                readonly accepted_insurance: boolean;
            };
            readonly response: GameStateUpdate;
        };

        readonly Double: {
            readonly request: void;
            readonly response: GameStateUpdate;
        };

        readonly Surrender: {
            readonly request: void;
            readonly response: GameStateUpdate;
        };
    }

    export interface BetPayload {
        readonly bet_amount: number;
        readonly currency: string;
        readonly perfect_pairs_amount?: number;
        readonly poker_bet_amount?: number;
    }

    export interface GameState extends BlackjackState {}
}

export {};
