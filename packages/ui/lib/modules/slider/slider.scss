@use '../../styles/common/bem';
@use '../../styles/common/prop';
@use '../../styles/common/util';

$-path: '../modules/slider/assets';
$-radius-extra-small: util.to-rem(2px);
$-radius-small: util.to-rem(4px);
$-radius-medium: util.to-rem(8px);
$-radius-large: util.to-rem(16px);
$-slider-height: util.to-rem(66px);
$-thumb-size-small: util.to-rem(18px);
$-thumb-size: util.to-rem(32px);
$-thumb-scale: 1.1;
$-track-height: util.to-rem(8px);
$-tooltip-border: 1px solid prop.get(color-zinc-600);
$-tooltip-offset: util.to-rem(-2px);

@mixin styles {
    @include bem.module('slider') {
        position: relative;
        display: flex;
        align-items: center;
        justify-content: center;
        block-size: $-slider-height;
        inline-size: 100%;
        max-inline-size: 100%;

        &:has([disabled]) {
            pointer-events: none;
        }

        &__background,
        &__track,
        &__progress {
            position: absolute;
            inset-block-start: 50%;
            transform: translateY(-50%);
        }

        &__background {
            inset-inline: -1rem;
            block-size: util.to-rem(42px);
            border: util.to-rem(6px) solid prop.get(color-zinc-600);
            border-radius: $-radius-large;
        }

        &__track {
            inline-size: 100%;
            block-size: $-track-height;
            border-radius: util.to-rem(6px);
            background: linear-gradient(
                to right,
                prop.get(slider-color-start, prop.get(color-green-450)) 0%,
                prop.get(slider-color-start, prop.get(color-green-450))
                    calc(prop.get(slider-percentage, 0) * 1%),
                prop.get(slider-color-end, prop.get(color-red-500))
                    calc(prop.get(slider-percentage, 0) * 1%),
                prop.get(slider-color-end, prop.get(color-red-500)) 100%
            );
        }

        &--roll-over {
            @include prop.set(
                (
                    slider-color-start: prop.get(color-red-500),
                    slider-color-end: prop.get(color-green-450),
                )
            );
        }

        &__progress {
            inset-inline-start: 0;
            block-size: $-track-height;
            border-radius: $-radius-small;
            background: var(--track-left-color);
        }

        &__input {
            appearance: none;
            position: relative;
            inline-size: 100%;
            min-inline-size: 100%;
            padding: 0;
            border: none;
            cursor: pointer;
            z-index: 2;

            &::-webkit-slider-thumb {
                appearance: none;
                inline-size: $-thumb-size;
                block-size: $-thumb-size;
                transition: transform 0.2s ease-in-out;
                background: url('#{$-path}/thumb.svg') no-repeat center / cover;
                border: none;
            }

            &::-moz-range-thumb {
                inline-size: $-thumb-size;
                block-size: $-thumb-size;
                transition: transform 0.2s ease-in-out;
                background: url('#{$-path}/thumb.svg') no-repeat center / cover;
                border: none;
            }

            &:hover &::-webkit-slider-thumb {
                transform: scale($-thumb-scale);
            }

            &:hover &::-moz-range-thumb {
                transform: scale($-thumb-scale);
            }
        }

        &__labels {
            position: absolute;
            inline-size: 100%;
            inset-block-start: $-slider-height;
            display: flex;
            justify-content: space-between;
            color: prop.get(color-white);
        }

        &__label {
            position: relative;
            inline-size: 0;

            &::before {
                content: '';
                position: absolute;
                inset-inline-start: 50%;
                inset-block-start: -1.025em;
                transform: translateX(-50%);
                border: util.to-rem(6px) solid transparent;
                border-top: util.to-rem(8px) solid prop.get(color-zinc-600);
            }

            &::after {
                content: attr(data-label);
                white-space: nowrap;
                position: absolute;
                left: 50%;
                transform: translateX(-50%);
            }
        }

        &__tooltip {
            position: absolute;
            inset-inline-start: calc(
                ($-thumb-size * $-thumb-scale) / 4 +
                    (
                        prop.get(slider-percentage, 0) / 100 *
                            (100% - ($-thumb-size * $-thumb-scale) / 2)
                    )
            );
            inset-block-start: $-tooltip-offset;
            inline-size: util.to-rem(1px);
            block-size: 1em;
            transition: opacity 0.2s ease-in-out;
            opacity: 0;

            &::before {
                content: attr(data-value);
                position: absolute;
                inset-inline-start: 0;
                inset-block-end: 0;
                background: prop.get(color-zinc-800);
                color: prop.get(color-white);
                padding: util.to-rem(8px) util.to-rem(12px);
                border-radius: $-radius-small;
                border: util.to-rem(1px) solid prop.get(color-zinc-600);
                transform: translateX(-50%);
            }

            &::after {
                content: '';
                position: absolute;
                inset-inline-start: 50%;
                inset-block-end: -(util.to-rem(5px));
                transform: translateX(-50%) rotate(45deg);
                inline-size: util.to-rem(10px);
                block-size: util.to-rem(10px);
                background: prop.get(color-zinc-800);
                border-bottom: $-tooltip-border;
                border-right: $-tooltip-border;
                border-bottom-right-radius: $-radius-extra-small;
            }
        }

        &:focus-within &__tooltip, &--plinko:hover &__tooltip {
            opacity: 1;
        }

        &--plinko {
            block-size: auto;
            margin: 0 1rem;
            position: relative;

            #{bem.namespaced-selector(m-slider__background)} {
                display: block;
                inset-inline-start: -3.5rem;
                inset-inline-end: -3.5rem;
                block-size: util.to-rem(54px);
                background: rgba(255, 255, 255, 0.04);
                border: 1px solid rgba(255, 255, 255, 0.12);
                border-radius: $-radius-medium;
            }

            #{bem.namespaced-selector(m-slider__track)} {
                block-size: util.to-rem(10px);
                inline-size: 100%;
                border-radius: $-radius-large;
                background: linear-gradient(
                    to right,
                    #fcd34d 0%,
                    #ef4444 var(--m-slider__plinko-percentage, 0%),
                    prop.get(color-zinc-700) var(--m-slider__plinko-percentage, 0%),
                    prop.get(color-zinc-700) 100%
                );
                transition: background 0.3s ease-out;
            }

            #{bem.namespaced-selector(m-slider__progress)},
            #{bem.namespaced-selector(m-slider__labels)} {
                display: none;
            }

            #{bem.namespaced-selector(m-slider__input)} {
                min-inline-size: 100%;
                position: relative;
                z-index: 2;

                &::-webkit-slider-thumb {
                    appearance: none;
                    inline-size: $-thumb-size-small;
                    block-size: $-thumb-size-small;
                    border-radius: util.to-rem(100px);
                    background: radial-gradient(circle, #ffe600 60%, #ffcc00 100%);
                    border: 3px solid #000;
                    box-shadow:
                        0 0 0 2px #ffe600,
                        0 0 8px 4px rgba(255, 255, 0, 0.25);

                    cursor: pointer;
                }

                &:hover {
                    &::-webkit-slider-thumb {
                        transform: scale($-thumb-scale);
                    }
                }

                &::-moz-range-thumb {
                    appearance: none;
                    inline-size: $-thumb-size-small;
                    block-size: $-thumb-size-small;
                    border-radius: 50%;
                    background: radial-gradient(circle, #ffe600 60%, #ffcc00 100%);
                    border: 3px solid #000;
                    box-shadow:
                        0 0 0 2px #ffe600,
                        0 0 8px 4px rgba(255, 255, 0, 0.25);

                    cursor: pointer;
                }

                &:hover::-moz-range-thumb {
                    transform: scale($-thumb-scale);
                }
            }
            #{bem.namespaced-selector(m-slider__tooltip)} {
                position: absolute;
                inset-block-start: util.to-rem(-30px);
                inset-inline-start: calc(
                    ($-thumb-size-small * $-thumb-scale) / 4 +
                        (
                            var(--slider-tooltip-percent, 0) / 100 *
                                (100% - ($-thumb-size-small * $-thumb-scale) / 2)
                        )
                );
                pointer-events: none;
                z-index: 3;

                &::before {
                    content: attr(data-value);
                    position: absolute;
                    inset-inline-start: 0;
                    transform: translateX(-50%);
                    background: prop.get(color-zinc-800);
                    color: prop.get(color-white);
                    padding: util.to-rem(8px) util.to-rem(12px);
                    border-radius: $-radius-small;
                    border: $-tooltip-border;
                    white-space: nowrap;
                }

                &::after {
                    content: '';
                    position: absolute;
                    inset-inline-start: 50%;
                    inset-block-start: calc(100% - util.to-rem(5px));
                    transform: translateX(-50%) rotate(45deg);
                    inline-size: util.to-rem(10px);
                    block-size: util.to-rem(10px);
                    background: prop.get(color-zinc-800);
                    border-bottom: $-tooltip-border;
                    border-right: $-tooltip-border;
                    border-bottom-right-radius: $-radius-extra-small;
                }
            }
        }
    }
}
