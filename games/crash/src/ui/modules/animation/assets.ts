import * as PIXI from 'pixi.js';
import { TARGET_MONKEY_WIDTH, RENDERING_CONFIG } from './constants';

export class AssetHelper {
    private app: PIXI.Application;
    private width: number;
    private height: number;

    constructor(app: PIXI.Application, width: number, height: number) {
        this.app = app;
        this.width = width;
        this.height = height;
    }

    public updateDimensions(width: number, height: number): void {
        this.width = width;
        this.height = height;
    }

    public async loadSvgAsHiResTexture(svgUrl: string): Promise<PIXI.Texture> {
        const dpr = window.devicePixelRatio || 1;
        const rasterWidth = TARGET_MONKEY_WIDTH * dpr;
    
        return new Promise((resolve, reject) => {
            const img = new Image();
            img.crossOrigin = 'anonymous';
            img.src = svgUrl;
    
            img.onload = () => {
                const aspectRatio = img.height / img.width;
                const rasterHeight = rasterWidth * aspectRatio;
                
                const canvas = document.createElement('canvas');
                canvas.width = rasterWidth;
                canvas.height = rasterHeight;
                
                const ctx = canvas.getContext('2d');
                if (!ctx) {
                    return reject(new Error('Failed to get canvas context'));
                }
                
                ctx.imageSmoothingEnabled = true;
                ctx.imageSmoothingQuality = 'high';
                ctx.drawImage(img, 0, 0, rasterWidth, rasterHeight);
                
                resolve(PIXI.Texture.from(canvas));
            };
            
            img.onerror = (err) => {
                const errMessage = typeof err === 'string' ? err : err.type;
                reject(new Error(`Failed to load SVG as image from ${svgUrl}: ${errMessage}`));
            };
        });
    }

    public async createBackgroundSprite(svgUrl: string): Promise<PIXI.Sprite | null> {
        try {
            const testUrl = new URL(svgUrl, window.location.origin).href;
            
            try {
                const response = await fetch(testUrl);
                if (!response.ok) {
                    return null;
                }
            } catch (fetchError) {
                return null;
            }
            
            const superResolutionMultiplier = RENDERING_CONFIG.SUPER_RESOLUTION_MULTIPLIER;
            
            const img = new Image();
            img.crossOrigin = 'anonymous';
            
            const svgTexture = await new Promise<PIXI.Texture>((resolve, reject) => {
                img.onload = () => {
                    const canvas = document.createElement('canvas');
                    const ctx = canvas.getContext('2d');
                    if (!ctx) {
                        reject(new Error('Failed to get canvas context'));
                        return;
                    }
                    
                    const targetWidth = this.width * superResolutionMultiplier;
                    const targetHeight = this.height * superResolutionMultiplier;
                    
                    ctx.imageSmoothingEnabled = true;
                    ctx.imageSmoothingQuality = 'high';
                    
                    const imgAspect = img.width / img.height;
                    const canvasAspect = targetWidth / targetHeight;
                    
                    let drawWidth, drawHeight;
                    
                    if (imgAspect > canvasAspect) {
                        // Image is wider than the canvas. Scale to fit height, preserving full width.
                        drawHeight = targetHeight;
                        drawWidth = drawHeight * imgAspect;
                    } else {
                        // Image is taller. Scale to fit width, preserving full height.
                        drawWidth = targetWidth;
                        drawHeight = drawWidth / imgAspect;
                    }
                    
                    // Set canvas dimensions to match the scaled image to avoid clipping
                    canvas.width = drawWidth;
                    canvas.height = drawHeight;

                    // Settings might be reset on resize, so re-apply
                    ctx.imageSmoothingEnabled = true;
                    ctx.imageSmoothingQuality = 'high';
                    
                    // Log all relevant values for debugging
                    console.log('[setBackgroundSVG] img.width:', img.width, 'img.height:', img.height);
                    console.log('[setBackgroundSVG] canvas.width:', canvas.width, 'canvas.height:', canvas.height);
                    console.log('[setBackgroundSVG] imgAspect:', imgAspect, 'canvasAspect:', canvasAspect);
                    
                    ctx.drawImage(img, 0, 0, drawWidth, drawHeight);
                    
                    const texture = PIXI.Texture.from(canvas);
                    texture.source.resolution = superResolutionMultiplier;
                    
                    resolve(texture);
                };
                
                img.onerror = () => {
                    reject(new Error('Failed to load SVG as image'));
                };
                
                img.src = testUrl;
            });
            
            if (!svgTexture) {
                return null;
            }
            
            return new PIXI.Sprite(svgTexture);
            
        } catch (error) {
            console.error('Error creating background sprite:', error);
            return null;
        }
    }

    public async createMonkeyHeadSprite(svgUrl: string): Promise<PIXI.Container | null> {
        try {
            const texture = await this.loadSvgAsHiResTexture(svgUrl);
            const monkeyHeadSprite = new PIXI.Sprite(texture);

            monkeyHeadSprite.anchor.set(0.5);
            
            // Normalize sprite width while maintaining aspect ratio
            monkeyHeadSprite.width = TARGET_MONKEY_WIDTH;
            monkeyHeadSprite.scale.y = monkeyHeadSprite.scale.x;

            const container = new PIXI.Container();
            container.addChild(monkeyHeadSprite);
            
            return container;
        } catch (error) {
            console.error('Failed to create monkey head sprite:', error);
            return null;
        }
    }

    public positionBackgroundSprite(backgroundSprite: PIXI.Sprite): void {
        if (!backgroundSprite) {
            return;
        }
        
        const texture = backgroundSprite.texture;
        
        const isTextureReady = () => {
            if ((texture?.width || 0) > 0 && (texture?.height || 0) > 0) return true;
            if (texture?.source && (texture.source.width || 0) > 0) return true;
            return false;
        };
        
        if (!isTextureReady()) {
            return;
        }
        
        const textureWidth = texture?.width || 1;
        const textureHeight = texture?.height || 1;
        
        const scaleX = this.width / textureWidth;
        const scaleY = this.height / textureHeight;
        const scale = Math.max(scaleX, scaleY) * RENDERING_CONFIG.BACKGROUND_SCALE_MULTIPLIER;
        
        backgroundSprite.scale.set(scale);
        
        // Position image at bottom-left of the canvas
        backgroundSprite.x = 0;
        backgroundSprite.y = 0;
        
        backgroundSprite.alpha = 1.0;
        backgroundSprite.visible = true;
        
        // Ensure background is behind all other graphics
        this.app.stage.addChildAt(backgroundSprite, 0);
    }

    public updateBackgroundPosition(
        backgroundSprite: PIXI.Sprite, 
        tipXPosition: number, 
        tipYPosition: number,
        padding: number
    ): void {
        if (!backgroundSprite) return;

        const graphWidth = this.width - padding * 2;
        const graphHeight = this.height - padding * 2;
        
        const texture = backgroundSprite.texture;
        const scale = backgroundSprite.scale.x;
        const scaledWidth = texture.width * scale;
        const scaledHeight = texture.height * scale;
        
        const tipProgressX = Math.max(0, Math.min(1, (tipXPosition - padding) / graphWidth));
        const tipProgressY = Math.max(0, Math.min(1, (tipYPosition - padding) / graphHeight));
        
        const availableMovementX = Math.max(0, scaledWidth - this.width);
        const availableMovementY = Math.max(0, scaledHeight - this.height);
        
        const backgroundOffsetX = tipProgressX * availableMovementX;
        const backgroundOffsetY = tipProgressY * availableMovementY;
        
        backgroundSprite.x = -backgroundOffsetX;
        backgroundSprite.y = -backgroundOffsetY;
    }

    public disposeSprite(sprite: PIXI.Sprite | null): void {
        if (sprite) {
            if (sprite.parent) {
                sprite.parent.removeChild(sprite);
            }
            sprite.destroy({ children: true, texture: false });
        }
    }

    public disposeContainer(container: PIXI.Container | null): void {
        if (container) {
            if (container.parent) {
                container.parent.removeChild(container);
            }
            container.destroy({ children: true });
        }
    }
} 