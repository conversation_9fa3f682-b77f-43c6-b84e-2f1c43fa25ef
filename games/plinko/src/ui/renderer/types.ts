import type { PlinkoRisk } from '../../game/state';
/**
 * Represents renderable objects.
 */
export interface Renderable {
    render(context: CanvasRenderingContext2D, alpha: number): void;
}

/**
 * Represents dynamic renderable objects (i.e., non-static renderable objects).
 */
export interface DynamicRenderable extends Renderable {
    update(dt: number): void;
}

/**
 * Represents a particle.
 */
export interface Particle extends DynamicRenderable {
    readonly isAlive: boolean;
}

/**
 * The risk level associated with a ball.
 */
export const Risk = {
    Low: 0,
    Medium: 1,
    High: 2,
} as const satisfies Record<string, PlinkoRisk>;
export type Risk = (typeof Risk)[keyof typeof Risk];

export type { BallData } from '../../game/state';
