import type { BetAmountLimits, GameActionName, GameError, ReadyState } from '@monkey-tilt/client';

export type BetOutcome = 'win' | 'loss';

export interface FinalState {
    readonly random_multiplier: string;
    readonly previous_bet_outcome: BetOutcome;
}

export interface AutobetState {
    readonly autobet_limit: number;
    readonly autobet_count: number;
    readonly on_win_reset: boolean;
    readonly on_win: string;
    readonly on_loss_reset: boolean;
    readonly on_loss: string;
    readonly stop_on_profit: string;
    readonly stop_on_loss: string;
    readonly autobet_cumulative_payout: string;
}

export interface RoundSummary {
    readonly time: Date;
    readonly round_id: string;
    readonly bet_amount: string;
    readonly multiplier: string;
    readonly random_multiplier: string;
    readonly total_payout: string;
    readonly outcome: BetOutcome;
}

export interface LimboBetAmountLimits extends BetAmountLimits {
    readonly bet: {
        readonly min: number;
        readonly max: number;
        readonly max_payout: number;
    };
}

export interface LimboState extends Partial<AutobetState>, Partial<FinalState> {
    readonly action?: GameActionName;
    readonly history: ReadonlyArray<RoundSummary>;

    readonly balance: number;
    readonly bet_limits: LimboBetAmountLimits;

    readonly multiplier_limits: {
        readonly min: number;
        readonly max: number;
    };

    readonly error: GameError | null;
    readonly isLoading: boolean;
    readonly readyState: ReadyState;
    readonly isAuthenticated: boolean;

    readonly round_id: string;

    readonly next_actions: ReadonlyArray<GameActionName>;

    readonly isAutobetActive: boolean;

    readonly bet_amount?: string;
    readonly original_bet_amount?: string;

    readonly multiplier?: string;

    readonly round_closed?: boolean;
    readonly total_payout?: string;
}
