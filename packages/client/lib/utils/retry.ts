export class RetryError extends Error {
    static readonly name = 'RetryError';
    static readonly code = 'RETRY_ERROR';
    readonly reason: unknown;
    readonly action: () => Promise<any>;
    readonly retryAborted: boolean;
    public constructor(action: () => Promise<any>, reason: unknown, retryAborted = false) {
        super('Failed to run action after maximum number of attempts');
        this.action = action;
        this.reason = reason;
        this.retryAborted = retryAborted;
    }
}

export interface RetryOptions {
    readonly maxAttempts?: number;
    readonly delay?: number;
    readonly jitter?: number;
    readonly shouldRetry?: ((error: unknown) => boolean) | undefined;
}

export function runWithRetry<T extends Promise<any>>(action: () => T): T;
export function runWithRetry<T extends Promise<any>>(options: RetryOptions, action: () => T): T;
export function runWithRetry<T extends Promise<any>>(
    options: RetryOptions | (() => T),
    action?: () => T,
): T {
    if (typeof options === 'function') {
        action = options;
        options = {};
    }

    if (!action) {
        throw new Error('No action provided');
    }

    const { maxAttempts = 5, delay = 100, jitter = 50, shouldRetry = () => true } = options;

    return new Promise<Awaited<T>>((resolve, reject) => {
        let attempts = 0;

        function run() {
            attempts++;
            action!()
                .then(resolve)
                .catch((error) => {
                    if (!shouldRetry(error) || attempts >= maxAttempts) {
                        reject(new RetryError(action!, error, attempts < maxAttempts));
                    } else {
                        setTimeout(
                            run,
                            2 ** (attempts - 1) * delay + jitter * (Math.random() * 2 - 1),
                        );
                    }
                });
        }

        run();
    }) as T;
}
