@use 'sass:map';
@use 'sass:meta';
@use '../common/util';
@use '../common/prop';

// The default width of a line of text, in characters.
$measure: 60ch;

// minimum viewport width (in pixels)
$fluid-scale-min-width: 358;

// maximum viewport width (in pixels)
$fluid-scale-max-width: 1536;

// font size and line height fluid scale
$fluid-font-sizes: (
    caps: (
        font-size: (
            min: 10,
            max: 12,
        ),
        line-height: (
            min: 14,
            max: 16,
        ),
    ),
    0: (
        font-size: (
            min: 12,
            max: 14,
        ),
        line-height: (
            min: 12,
            max: 14,
        ),
    ),
    1: (
        font-size: 14,
        line-height: 20,
    ),
    2: (
        font-size: (
            min: 14,
            max: 16,
        ),
        line-height: (
            min: 20,
            max: 24,
        ),
    ),
    3: (
        font-size: (
            min: 14,
            max: 18,
        ),
        line-height: (
            min: 20,
            max: 22,
        ),
    ),
    4: (
        font-size: (
            min: 14,
            max: 20,
        ),
        line-height: (
            min: 20,
            max: 28,
        ),
    ),
    5: (
        font-size: (
            min: 18,
            max: 24,
        ),
        line-height: (
            min: 24,
            max: 32,
        ),
    ),
    10: (
        font-size: (
            min: 28,
            max: 50,
        ),
        line-height: (
            min: 36,
            max: 60,
        ),
    ),
    caption: (
        font-size: (
            min: 14,
            max: 20,
        ),
        line-height: (
            min: 20,
            max: 28,
        ),
    ),
    label: (
        font-size: (
            min: 12,
            max: 20,
        ),
        line-height: (
            min: 16,
            max: 28,
        ),
    ),
    label-s: (
        font-size: (
            min: 12,
            max: 16,
        ),
        line-height: (
            min: 16,
            max: 20,
        ),
    ),
    multiplier: (
        font-size: (
            min: 70,
            max: 120,
        ),
        line-height: (
            min: 70,
            max: 120,
        ),
    ),
);

// spacing fluid scale
$fluid-space-sizes: (
    none: 0,
    3xs: 2,
    2xs: 5,
    xs: 8,
    3xs-xs: (
            min: 2,
            max: 8,
        ),
    2xs-xs: (
            min: 4,
            max: 8,
        ),
    s: 10,
    3xs-s: (
            min: 2,
            max: 10,
        ),
    2xs-s: (
            min: 4,
            max: 10,
        ),
    xs-s: (
        min: 8,
        max: 10,
    ),
    xs-sm: (
        min: 8,
        max: 14,
    ),
    m: (
        min: 10,
        max: 16,
    ),
    xs-m: (
        min: 8,
        max: 16,
    ),
    l: 20,
    m-l: (
        min: 12,
        max: 20,
    ),
    xl: (
        min: 16,
        max: 24,
    ),
    m-xl: (
        min: 12,
        max: 24,
    ),
    2xl: (
        min: 20,
        max: 32,
    ),
    l-2xl: (
        min: 20,
        max: 32,
    ),
    3xl: (
        min: 24,
        max: 40,
    ),
    xl-3xl: (
        min: 24,
        max: 40,
    ),
);

$space-scale: map.keys($fluid-space-sizes);

@function fluid-size($min, $max) {
    @if $min == $max {
        @return util.to-rem($min, $from: px);
    }
    @return fluid-clamp($min, $max, $fluid-scale-min-width, $fluid-scale-max-width);
}

@mixin styles {
    $font-sizes: util.empty-map();
    @each $level, $sizes in $fluid-font-sizes {
        @each $type, $prop in (fs: 'font-size', lh: 'line-height') {
            $limits: map.get($sizes, $prop);
            $font-sizes: map.set(
                $font-sizes,
                '#{$type}-#{$level}',
                fluid-size(util.get($limits, 'min', $limits), util.get($limits, 'max', $limits))
            );
        }
    }

    $space-sizes: util.empty-map();
    @each $size, $limits in $fluid-space-sizes {
        $space-sizes: map.set(
            $space-sizes,
            'size-#{$size}',
            fluid-size(util.get($limits, 'min', $limits), util.get($limits, 'max', $limits))
        );
    }

    @include prop.set('measure', $measure);
    @include prop.set($font-sizes);
    @include prop.set($space-sizes);
}
