import { logDevError } from '@monkey-tilt/utils';
import { GameError } from './error';
import { get, mergeURL, mustParseFloat, pick, post, tryParseFloat } from './utils';

export interface BalanceData {
    readonly balance: number;
    readonly currency: string;
}

export interface SeedData {
    readonly client_seed: string;
    readonly server_seed_hashed: string;
    readonly next_server_seed_hashed: string;
}

interface ErrorResponse {
    readonly type: string;
    readonly message: string;
    readonly code: string;
    readonly action_id?: string;
}

export interface VerifySeedParams {
    readonly client_seed: string;
    readonly server_seed: string;
    readonly nonce: number;
}

export interface SeedVerificationData {
    readonly type: string;
    readonly action: string;
    readonly data: unknown;
}

export type BetAmountLimits<T extends string = never> = {
    [K in T | 'bet']: {
        readonly min: number;
        readonly max: number;
        readonly max_payout?: number | undefined;
    };
};
export type MultipliersMap = {
    [rtp: string]: {
        [risk: string]: {
            [num_chosen: string]: [multiplier: string, chance: string][];
        };
    };
};
interface ConfigResponse<T extends string = never> {
    readonly min_bet: string;
    readonly max_bet: string;
    readonly custom_props: T extends string
        ? Record<`${T}_min_bet` | `${T}_max_bet` | 'rtp' | `${T}_max_payout` | 'max_payout', string>
        : never;
    readonly multipliers?: MultipliersMap;
}

interface ConversionRateResponse {
    readonly rate?: {
        readonly crRate: number;
        readonly crExpiresOn: string;
    };
}

interface ConversionRate {
    readonly rate: number;
    readonly expiresOn: number;
}

const CONFIG_CACHE_TTL = 5 * 1000; // 5 seconds

export class APIClient {
    #gatewayUrl: URL;

    #pendingConfigResponse: Promise<ConfigResponse<string>> | null = null;
    #configCache: {
        expiresAt: number;
        data: ConfigResponse<string>;
    } = {
        expiresAt: 0,
        data: {} as ConfigResponse<string>,
    };

    #usdRateCache = new Map<string, ConversionRate | Promise<ConversionRate>>([
        ['USD', { rate: 1, expiresOn: Infinity }],
    ]);

    public gameId: string | undefined;
    #gameSessionId: string | undefined;

    public constructor(gatewayUrl: string | URL) {
        this.#gatewayUrl = new URL(gatewayUrl);
    }

    public get gameSessionId(): string | undefined {
        return this.#gameSessionId;
    }

    public set gameSessionId(value: string | undefined) {
        this.#gameSessionId = value;
        this.#dropCache();
    }

    public async getBalance(): Promise<BalanceData> {
        return this.#get<BalanceData>(
            'players/balance',
            {
                game_session_id: this.gameSessionId,
                game_id: this.gameId,
            },
            {
                balance: 'number',
                currency: 'string',
            },
        );
    }

    public async getUSDRate(currency: string): Promise<ConversionRate> {
        const crTo = currency.toUpperCase();
        const cachedRate = this.#usdRateCache.get(crTo);

        if (cachedRate && (cachedRate instanceof Promise || cachedRate.expiresOn > Date.now())) {
            return cachedRate;
        }

        const fetchRate = (shouldRetry = true): Promise<ConversionRate> =>
            this.#get<ConversionRateResponse>(
                'currency/conversion',
                {
                    game_session_id: this.gameSessionId,
                    crFrom: 'USD',
                    crTo,
                },
                {
                    rate: {
                        optional: true,
                        properties: {
                            crRate: 'number',
                            crExpiresOn: 'string',
                        },
                    },
                },
            )
                .then((data) => {
                    if (!data.rate) {
                        throw new Error('no rate returned');
                    }

                    const now = Date.now();
                    const cachedRate: ConversionRate = {
                        rate: data.rate.crRate,
                        expiresOn: new Date(data.rate.crExpiresOn).getTime(),
                    };

                    if (now >= cachedRate.expiresOn) {
                        if (shouldRetry) {
                            return fetchRate(false);
                        }
                    }

                    this.#usdRateCache.set(crTo, cachedRate);

                    return cachedRate;
                })
                .catch((error) => {
                    logDevError('Failed to fetch conversion rate', error);
                    if (shouldRetry) {
                        return fetchRate(false);
                    }
                    throw new Error('Failed to fetch conversion rate');
                });

        const request = fetchRate();

        this.#usdRateCache.set(crTo, request);

        return request;
    }

    public async getBetLimits(): Promise<BetAmountLimits>;
    public async getBetLimits<T extends string>(...customBets: T[]): Promise<BetAmountLimits<T>>;
    public async getBetLimits<T extends string>(...customBets: T[]): Promise<BetAmountLimits<T>> {
        const betTypes = customBets.filter((bet) => bet !== 'bet');
        return this.#fetchConfig<ConfigResponse<T>>(
            betTypes.reduce(
                (acc, bet) => {
                    acc[`${bet}_min_bet`] = 'string';
                    acc[`${bet}_max_bet`] = 'string';
                    acc[`${bet}_max_payout`] = 'string?';
                    return acc;
                },
                { max_payout: 'string?' } as Record<
                    `${T}_min_bet` | `${T}_max_bet` | `${T}_max_payout` | 'max_payout',
                    string
                >,
            ) as TypeSpec<ConfigResponse<T>['custom_props']>,
        ).then((data) => {
            const limits: BetAmountLimits<string> = {
                bet: {
                    min: mustParseFloat(data.min_bet),
                    max: mustParseFloat(data.max_bet),
                    max_payout: tryParseFloat(data.custom_props.max_payout),
                },
            };

            for (const bet of betTypes) {
                const min_bet = data.custom_props[`${bet}_min_bet`];
                const max_bet = data.custom_props[`${bet}_max_bet`];
                const max_payout = data.custom_props[`${bet}_max_payout`];

                limits[bet] = {
                    min: tryParseFloat(min_bet) ?? limits.bet!.min,
                    max: tryParseFloat(max_bet) ?? limits.bet!.max,
                    max_payout: tryParseFloat(max_payout) ?? limits.bet!.max_payout,
                };
            }

            return limits as BetAmountLimits<T>;
        });
    }

    public async getRTP(): Promise<number | undefined> {
        return this.getConfigCustomProps({
            rtp: 'string?',
        }).then((data) => {
            return tryParseFloat(data.rtp);
        });
    }

    /**
     * Fetches the config from CMS and returns a subset of the custom_props, validated using the
     * provided type spec object.
     *
     * The shape of the returned props object will match the shape of the type spec object.
     *
     * @param spec - The type spec object to validate the custom_props against.
     * @returns A promise that resolves to the validated custom_props.
     */
    public async getConfigCustomProps<
        T extends Partial<TypeSpec<ConfigResponse<string>['custom_props']>>,
    >(spec: T): Promise<ValidatedType<T>> {
        return this.#fetchConfig(spec).then((data) => {
            return pick(
                data.custom_props as Record<string, unknown>,
                ...Object.keys(spec),
            ) as ValidatedType<T>;
        });
    }

    public async getMultipliers(): Promise<MultipliersMap> {
        return this.#fetchConfig().then((data) => {
            return data.multipliers as MultipliersMap;
        });
    }

    public async getSeeds(): Promise<SeedData> {
        return this.#get<SeedData>(`games/keys/${this.gameSessionId}`, null, {
            client_seed: 'string',
            server_seed_hashed: 'string',
            next_server_seed_hashed: 'string',
        });
    }

    public async verifySeeds(data: VerifySeedParams): Promise<SeedVerificationData> {
        return this.#post<SeedVerificationData>(
            `games/keys/verify/${this.gameSessionId}`,
            {
                type: 'VerifySeeds',
                game_id: this.gameId,
                data,
            },
            {
                type: 'string',
                action: 'string',
                data: 'object',
            },
        );
    }

    public async setClientSeed(client_seed: string): Promise<void> {
        return this.#post<void>(`games/keys/${this.gameSessionId}`, { client_seed });
    }

    #dropCache() {
        this.#pendingConfigResponse = null;
        this.#configCache.expiresAt = 0;
        this.#configCache.data = {} as ConfigResponse<string>;
    }

    #fetchConfig<T extends ConfigResponse<string> = ConfigResponse<string>>(
        spec?: Partial<TypeSpec<T['custom_props']>>,
    ): Promise<T> {
        let configDataPromise: Promise<ConfigResponse<string>>;
        let cacheHit = false;

        if (this.#configCache.expiresAt > Date.now()) {
            cacheHit = true;
            configDataPromise = Promise.resolve(this.#configCache.data);
        } else {
            configDataPromise = this.#pendingConfigResponse =
                this.#pendingConfigResponse ??
                this.#get<ConfigResponse<string>>(
                    'games/configs',
                    {
                        game_session_id: this.gameSessionId,
                        game_id: this.gameId,
                    },
                    {
                        min_bet: 'string',
                        max_bet: 'string',
                        custom_props: 'object?',
                    } as TypeSpec<ConfigResponse<string>>,
                );
        }
        return configDataPromise.then((data) => {
            this.#pendingConfigResponse = null;

            if (!cacheHit) {
                this.#configCache.data = data;
                this.#configCache.expiresAt = Date.now() + CONFIG_CACHE_TTL;
            }

            if (spec) {
                this.#assertResponseDataType<T['custom_props']>(
                    data.custom_props,
                    spec as TypeSpec<T['custom_props']>,
                    'Invalid config response',
                );
            }

            return data as T;
        });
    }

    #get<T = void>(endpoint: string, query: unknown, spec?: TypeSpec<T>): Promise<T> {
        this.#validate();
        return get(mergeURL(this.#gatewayUrl, endpoint), query, {
            handle: this.#handle(`GET ${endpoint}`, spec || ({} as TypeSpec<T>)),
        });
    }

    #post<T = void>(endpoint: string, body: unknown, spec?: TypeSpec<T>): Promise<T> {
        this.#validate();
        return post(mergeURL(this.#gatewayUrl, endpoint), body, {
            handle: this.#handle(`POST ${endpoint}`, spec || ({} as TypeSpec<T>)),
        });
    }

    #handle<T>(
        endpointInfo: string,
        responseSpec: TypeSpec<T>,
    ): (response: Response) => Promise<T> {
        return async (response): Promise<T> => {
            const data = (await response.json()) as unknown;

            if (!response.ok) {
                if (
                    validateObject<ErrorResponse>(data, {
                        type: 'string',
                        message: 'string',
                        code: 'string',
                        action_id: 'string?',
                    }) &&
                    data.type === 'Error'
                ) {
                    throw new GameError(data.message, data.code, data.action_id ?? '');
                }
                throw new Error(response.statusText);
            }

            this.#assertResponseDataType<T>(data, responseSpec, `Invalid ${endpointInfo} response`);

            return data;
        };
    }

    #assertResponseDataType<T>(
        data: unknown,
        spec: TypeSpec<T>,
        message = 'Invalid response data',
    ): asserts data is T {
        if (!validateObject(data, spec)) {
            const error = new Error(message);
            logDevError(error, data);
            throw error;
        }
    }

    #validate() {
        if (!this.gameId || !this.gameSessionId) {
            throw new Error('Game ID and Game Session ID must be set');
        }
    }
}

export type TypeName<T> = T extends string
    ? 'string'
    : T extends number
      ? 'number'
      : T extends boolean
        ? 'boolean'
        : T extends undefined
          ? 'undefined'
          : T extends any[]
            ? 'array'
            : T extends Function
              ? 'function'
              : 'object';

interface TypeNameMap {
    string: string;
    number: number;
    boolean: boolean;
    undefined: undefined;
    array: any[];
    function: (...args: any[]) => any;
    object: Record<string, any>;
}

type TypeForName<T> = T extends keyof TypeNameMap
    ? TypeNameMap[T]
    : T extends `${infer U}?`
      ? TypeForName<U> | undefined
      : never;

type ValidatedType<T> = { [K in keyof T]: TypeForName<T[K]> };

export type TypeSpec<T> = {
    [K in keyof T]:
        | TypeName<T[K]>
        | `${TypeName<T[K]>}?`
        | (T[K] extends Record<string, any> | undefined
              ? { properties: TypeSpec<T[K]>; optional?: boolean }
              : never);
};

export function validateObject<T>(value: unknown, props: TypeSpec<T>): value is T {
    if (typeof value !== 'object' || value === null) {
        return false;
    }

    for (const [key, type] of Object.entries(props) as [
        keyof T & string,
        TypeSpec<T>[keyof T & string],
    ][]) {
        const object = value as Record<string, unknown>;
        const isSimpleType = typeof type === 'string';
        const isOptional = isSimpleType
            ? type.endsWith('?')
            : typeof type === 'object' && type?.optional;

        if (isOptional && (!(key in object) || object[key] == null)) {
            continue;
        }

        if (!isSimpleType) {
            if (!validateObject(object[key], type.properties)) {
                return false;
            }
            continue;
        }

        const typeName = isOptional ? type.slice(0, -1) : type;
        if (
            !(key in object) ||
            (typeName === 'array' ? !Array.isArray(object[key]) : typeof object[key] !== typeName)
        ) {
            return false;
        }
    }

    return true;
}
