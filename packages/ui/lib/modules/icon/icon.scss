@use 'sass:map';
@use '../../styles/common/bem';
@use '../../styles/common/prop';
@use '../../styles/common/util';
@use '../../styles/config/globals';

$-path: '../modules/icon/assets'; // relative to styles/index.scss entry point
$-icons: (
    arrow-up: (
        url: url('#{$-path}/arrow.svg'),
    ),
    arrow-down: (
        url: prop.get(icon-url-arrow-up),
        angle: 180deg,
    ),
    arrow-left: (
        url: prop.get(icon-url-arrow-up),
        angle: -90deg,
    ),
    arrow-right: (
        url: prop.get(icon-url-arrow-up),
        angle: 90deg,
    ),
    ask: (
        url: url('#{$-path}/ask.svg'),
        color: prop.get(color-mt-400),
        background: prop.get(color-mt-950),
    ),
    bolt: (
        url: url('#{$-path}/bolt.svg'),
    ),
    cards: (
        url: url('#{$-path}/cards.svg'),
        color: prop.get(color-blue-400),
    ),
    chevron-l: (
        url: prop.get(icon-url-chevron-r),
        angle: 180deg,
    ),
    chevron-r: (
        url: url('#{$-path}/chevron-r.svg'),
    ),
    chevron: (
        url: url('#{$-path}/chevron.svg'),
    ),
    chips: (
        url: url('#{$-path}/chips.svg'),
    ),
    circle-dollar: (
        url: url('#{$-path}/circle-dollar.svg'),
    ),
    clear: (
        url: url('#{$-path}/clear.svg'),
    ),
    clockwise: (
        url: url('#{$-path}/clockwise.svg'),
    ),
    close: (
        url: url('#{$-path}/close.svg'),
    ),
    coins: (
        url: url('#{$-path}/coins.svg'),
        color: prop.get(color-yellow-400),
    ),
    copy: (
        url: url('#{$-path}/copy.svg'),
    ),
    double-check: (
        url: url('#{$-path}/double-check.svg'),
    ),
    error: (
        url: url('#{$-path}/error.svg'),
        color: prop.get(color-zinc-900),
        background: prop.get(color-red-400),
    ),
    flag: (
        url: url('#{$-path}/flag.svg'),
    ),
    hand: (
        url: url('#{$-path}/hand.svg'),
    ),
    infinity: (
        url: url('#{$-path}/infinity.svg'),
    ),
    info: (
        url: url('#{$-path}/info.svg'),
    ),
    keyboard: (
        url: url('#{$-path}/keyboard.svg'),
    ),
    lock: (
        url: url('#{$-path}/lock.svg'),
    ),
    multiply: (
        url: url('#{$-path}/multiply.svg'),
    ),
    music: (
        url: url('#{$-path}/music.svg'),
    ),
    notepad: (
        url: url('#{$-path}/notepad.svg'),
    ),
    plus: (
        url: url('#{$-path}/plus.svg'),
    ),
    raise: (
        url: url('#{$-path}/raise.svg'),
        color: prop.get(color-green-400),
    ),
    risk: (
        url: url('#{$-path}/risk.svg'),
    ),
    rows: (
        url: url('#{$-path}/rows.svg'),
    ),
    sound: (
        url: url('#{$-path}/sound.svg'),
    ),
    stop: (
        url: url('#{$-path}/stop.svg'),
        color: prop.get(color-red-400),
    ),
    switch: (
        url: url('#{$-path}/switch.svg'),
    ),
    undo: (
        url: url('#{$-path}/undo.svg'),
    ),
    x: (
        url: url('#{$-path}/x.svg'),
    ),
);

@include globals.define(util.affix-keys(util.pluck($-icons, 'url'), $prefix: 'icon-url-'));
@include globals.define(
    (
        null-mask:
            url('data:image/svg+xml,,%3Csvg viewBox="0 0 1 1" xmlns="http://www.w3.org/2000/svg"%3E%3Cpath d="M0 0h1v1H0z"/%3E%3C/svg%3E'),
    )
);

@mixin styles {
    @include bem.module('icon') {
        display: block;
        inline-size: prop.get(icon-size, prop.get(size-l));
        block-size: prop.get(icon-size, prop.get(size-l));
        position: relative;
        flex-shrink: 0;
        transform: rotate(prop.get(icon-angle, 0));

        &::before,
        &::after {
            content: '';
            display: block;
            position: absolute;
            inset: 0;
        }

        &::before {
            border-radius: 100%;
            background: prop.get(icon-background, transparent);
        }

        &:after {
            mask: 100% 100% no-repeat;
            mask-image: prop.get(icon-url, prop.get(null-mask));
            background: prop.get(icon-color, prop.get(icon-default-color, currentColor));
        }

        @each $name, $icon in $-icons {
            &--#{$name} {
                $icon: map.set($icon, 'url', prop.get(icon-url-#{$name}));
                @include prop.set(util.affix-keys($icon, $prefix: 'icon-'));
            }
        }

        &--currency {
            inline-size: auto;
            min-inline-size: calc(
                prop.get(icon-size, prop.get(size-l)) - 2 * prop.get(icon-padding)
            );
            max-inline-size: min-content;
            font-style: normal;
            display: inline-flex;
            justify-content: center;
            align-items: center;
            padding: 0 prop.get(icon-padding);
            box-sizing: content-box;

            @include prop.set(
                (
                    icon-background: prop.get(color-green-850),
                    icon-padding: calc(prop.get(icon-size, prop.get(size-l)) / 4),
                )
            );

            &::before {
                border-radius: prop.get(icon-size, prop.get(size-l));
            }

            &::after {
                position: relative;
                content: attr(data-currency);
                mask: none;
                background: none;
                color: prop.get(icon-color, prop.get(color-green-350));
            }
        }
    }
}
