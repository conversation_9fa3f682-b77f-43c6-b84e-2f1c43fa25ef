import { create, defineComponent, type Props } from '../../component';
import { mergeClasses } from '../../utilities/classes';
import { Chip } from './chip';
import { ChipProps as BaseChipProps } from './props';

export interface ChipStackProps extends BaseChipProps, Props<HTMLDivElement> {
    /**
     * The number of chips in the stack.
     */
    readonly count: number;
}

export const ChipStack = defineComponent(
    'Chip.Stack',
    () =>
        ({ count, value, text, color, isSelected, ...props }: ChipStackProps): HTMLElement => {
            return create(
                'div',
                {
                    props: {
                        role: 'img',
                        ariaLabel:
                            count < 1
                                ? 'Empty chip stack'
                                : count == 1
                                  ? `Chip with denomination ${text}`
                                  : `Stack of ${count} chips with denomination ${text}`,
                        ...props,
                        className: mergeClasses(props.className, {
                            'm-chip-stack': true,
                        }),
                        cssProps: {
                            ...(props.cssProps ?? {}),
                            'chip-count': count.toString(),
                        },
                    },
                },
                ...new Array(count).fill(null).map((_, i) =>
                    Chip({
                        value,
                        text,
                        color,
                        isSelected,
                        cssProps: { 'chip-index': i.toString() },
                    }),
                ),
            );
        },
);
