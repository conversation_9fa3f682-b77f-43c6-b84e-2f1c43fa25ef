@use 'sass:list';
@use 'sass:map';
@use 'sass:meta';

///
/// Get a CSS custom property value by name.
///
/// The property name is automatically prefixed with the configured namespace.
///
/// @param {String} $name - The name of the custom property. Without --
/// @param {Any} $default - The default value.
///
@function get($name, $default: null) {
    @return var(--#{lib-config(ns)}#{$name}, $default);
}

///
/// Set CSS custom properties.
///
/// Property names are automatically prefixed with the configured namespace.
///
/// @param {Map} $props - A map of custom property names and values.
///
/// or
///
/// @param {String} $name - The name of the custom property. Without --.
/// @param {Any} $value - The value of the custom property.
///
@mixin set($args...) {
    $props: ();
    $important: false;

    $keywords: meta.keywords($args);
    @if map.has-key($keywords, 'important') {
        $important: map.get($keywords, 'important');
    }

    @if list.length($args) == 1 {
        $props: list.nth($args, 1);
        @if not meta.type-of($props) == map {
            @error 'Expected a map as the only argument.';
        }
    } @else if list.length($args) == 2 {
        $props: map.set((), list.nth($args, 1), list.nth($args, 2));
    } @else {
        @error 'Expected 1 or 2 arguments.';
    }

    @each $name, $value in $props {
        @if $important {
            --#{lib-config(ns)}#{$name}: #{$value} !important;
        } @else {
            --#{lib-config(ns)}#{$name}: #{$value};
        }
    }
}
