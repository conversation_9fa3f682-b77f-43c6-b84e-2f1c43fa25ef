import { Meta, StoryObj } from '@storybook/html';
import { Bar, type BarProps } from './bar';
import { html } from '../../component';

const meta: Meta<BarProps> = {
    title: 'Modules/Bar',
};
export default meta;

export const Default: StoryObj<BarProps> = {
    render: (props) =>
        html('div')({
            css: { 'margin-top': '10rem' },
            children: [
                Bar({
                    ...props,
                    multipliers: () => [
                        { multiplier: '0', chance: '0.45', payout: '55' },
                        { multiplier: '1', chance: '0.88', payout: '55' },
                        { multiplier: '2', chance: '0.88', payout: '55' },
                        { multiplier: '3', chance: '0.88', payout: '55' },
                        { multiplier: '4', chance: '0.88', payout: '55' },
                        { multiplier: '5', chance: '0.88', payout: '55' },
                    ],
                }),
            ],
        }).render(),
};

export const TwoItems: StoryObj<BarProps> = {
    render: (props) =>
        html('div')({
            css: { 'margin-top': '10rem' },
            children: [
                Bar({
                    ...props,
                    multipliers: () => [
                        { multiplier: '0', chance: '0.45', payout: '55' },
                        { multiplier: '1', chance: '0.88', payout: '55' },
                    ],
                }),
            ],
        }).render(),
};

export const Empty: StoryObj<BarProps> = {
    render: (props) =>
        html('div')({
            css: { 'margin-top': '10rem' },
            children: [
                Bar({
                    ...props,
                    multipliers: () => [],
                }),
            ],
        }).render(),
};
