@use '../../styles/common/bem';
@use '../../styles/common/prop';
@use '../../styles/common/respond';
@use '../../styles/common/type-style' as *;
@use '../../styles/common/util';

@mixin styles {
    $border-radius: util.to-rem(24px);
    $border-radius-md: util.to-rem(12px);
    $border-radius-sm: util.to-rem(8px);
    $backdrop-fade-in: #{lib-config(ns)}modal-backdrop-fade-in;
    $fade-in: #{lib-config(ns)}modal-fade-in;
    $fade-out: #{lib-config(ns)}modal-fade-out;

    @include bem.module('modal') {
        padding: 0;
        border-radius: $border-radius;
        inline-size: 85vw;
        max-inline-size: util.to-rem(600px);
        block-size: min-content;
        animation: $fade-out 0.3s ease-out;
        color: prop.get(color-white);
        background: none;
        align-self: center;

        @include type-style(body2);

        @include respond.until('m') {
            inline-size: 100%;
            margin-inline: 0;
        }

        &--wide {
            max-inline-size: util.to-rem(900px);
        }

        & > #{bem.block-selector('module', 'frame')} {
            min-block-size: fit-content;

            @include prop.set(
                (
                    stack-gap: prop.get(size-m-l),
                    frame-padding: util.to-rem(32px),
                    frame-fill: linear-gradient(135deg, #131314 0%, #09090b 100%),
                )
            );
        }

        &::backdrop {
            backdrop-filter: blur(15px);
        }

        &[open] {
            animation: $fade-in 0.5s ease-out;

            &::backdrop {
                animation: $backdrop-fade-in 0.5s ease-out forwards;
            }
        }

        &__close {
            border: none;
            padding: 0;

            @include prop.set(icon-size, prop.get(size-xl));
        }

        &__description {
            color: prop.get(color-zinc-400);
        }

        &__history-header {
            display: flex;
            justify-content: space-between;
            color: prop.get(color-zinc-400);
        }
        

        &__limits {
            display: flex;
            flex-direction: column;
            gap: util.to-rem(8px);
        
            > [class*='-m-label-group'] {
                padding: util.to-rem(10px);
                background-color: transparent;

                &:nth-child(odd) {
                    background-color: prop.get(color-zinc-800);
                }
            }
        }        

        &__note {
            background-color: rgba(69, 63, 2, 0.2);
            color: prop.get(color-mt-400);
            padding: 0.75rem;
            border-radius: $border-radius-md;
            margin-block-end: util.to-rem(8px);
        }

        &__steps {
            list-style: none;
            display: flex;
            flex-direction: column;
            gap: util.to-rem(24px);
        }

        &__step {
            display: flex;
            flex-direction: row;
            justify-content: center;
            gap: util.to-rem(16px);
            margin-left: 0 !important;
            align-items: center;

            strong {
                display: block;
                color: prop.get(color-mt-400);
                margin-bottom: util.to-rem(4px);
            }

            p {
                margin: 0;
                color: prop.get(color-zinc-400);
            }
        }

        &__step-number {
            font-size: util.to-rem(56px);
            font-weight: 600;
            min-inline-size: util.to-rem(72px);
            color: prop.get(color-zinc-700);
        }

        &__table {
            width: 100%;
            border-collapse: collapse;
            font-size: util.to-rem(14px);

            th,
            td {
                padding: util.to-rem(12px) util.to-rem(16px);
                color: prop.get(color-white);
            }

            th {
                font-weight: normal;
                color: prop.get(color-zinc-400);
            }

            tbody tr:nth-child(odd) td {
                background-color: prop.get(color-zinc-800);
            }

            th:first-child,
            td:first-child {
                text-align: left;
            }

            th:nth-child(2),
            td:nth-child(2) {
                text-align: center;
            }

            th:last-child,
            td:last-child {
                text-align: right;
            }

            td:first-child {
                border-top-left-radius: $border-radius-sm;
                border-bottom-left-radius: $border-radius-sm;
            }

            td:last-child {
                border-top-right-radius: $border-radius-sm;
                border-bottom-right-radius: $border-radius-sm;
            }
        }

        &__input {
            appearance: none;
            background-color: prop.get('color-zinc-900');
            border: util.to-rem(1px) solid prop.get('color-zinc-800');
            border-radius: util.to-rem(8px);
            color: prop.get('color-white');
            padding: util.to-rem(10px) util.to-rem(14px);
            font-size: util.to-rem(14px);
            width: 100%;
            cursor: pointer;
            line-height: 1.5;
            font-family: inherit;

            &:focus {
                outline: none;
                box-shadow: 0 0 util.to-rem(1px) prop.get('color-mt-400');
                border-color: prop.get('color-mt-400');
            }

            &::-ms-expand {
                display: none;
            }

            &:disabled {
                background-color: prop.get('color-zinc-800');
                color: prop.get('color-zinc-450');
                cursor: not-allowed;
            }
        }
    }

    @keyframes #{$fade-in} {
        0% {
            opacity: 0;
            display: none;
        }

        100% {
            opacity: 1;
            display: block;
        }
    }

    @keyframes #{$fade-out} {
        0% {
            opacity: 1;
            display: block;
        }

        100% {
            opacity: 0;
            display: none;
        }
    }

    @keyframes #{$backdrop-fade-in} {
        0% {
            background-color: rgb(39 39 42 / 0%);
        }

        100% {
            background-color: rgb(39 39 42 / 50%);
        }
    }
}
