import { defineComponent, forwardRef, html, type Component, type Props } from '../../component';
import { mergeClasses, withNamespace } from '../../utilities';
import type { Size } from '../types';

export interface ReelProps extends Props<HTMLDivElement> {
    readonly gap?: Size;
}

export const Reel = defineComponent(
    'Reel',
    () =>
        ({ gap, ...props }: ReelProps): Component<HTMLDivElement> =>
            html('div')({
                ...props,
                ref: forwardRef(props.ref, (element: HTMLDivElement) => {
                    const isOverflowing = withNamespace('is-overflowing');

                    element.classList.toggle(
                        isOverflowing,
                        element.scrollWidth > element.clientWidth,
                    );

                    new ResizeObserver(() => {
                        element.classList.toggle(
                            isOverflowing,
                            element.scrollWidth > element.clientWidth,
                        );
                    }).observe(element);
                }),
                cssProps: {
                    'reel-gap': gap && { var: `size-${gap}` },
                    ...(props.cssProps ?? {}),
                },
                className: mergeClasses(props.className, {
                    'l-reel': true,
                }),
            }),
);
