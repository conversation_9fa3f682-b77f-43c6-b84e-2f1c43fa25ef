const MIN_NORMAL: number = 2 ** -1023; // 1.1125369292536007e-308

/**
 * Checks if two numbers are nearly equal, within a specified tolerance.
 *
 * @param a - The first number to compare.
 * @param b - The second number to compare.
 * @param epsilon - The tolerance for the comparison. Default is 1e-6.
 */
export function nearlyEqual(a: number, b: number, epsilon: number = 1e-6): boolean {
    if (a === b) {
        return true;
    }

    const absA = Math.abs(a);
    const absB = Math.abs(b);
    const diff = Math.abs(a - b);

    if (a === 0 || b === 0 || absA + absB < MIN_NORMAL) {
        return diff < epsilon * MIN_NORMAL;
    }
    return diff / Math.min(absA + absB, Number.MAX_VALUE) < epsilon;
}

/**
 * Rounds a number to the given precision using the "half-even" (i.e., banker's rounding) mode.
 *
 * @param value The number to round.
 * @param precision The number of decimal places to round to.
 * @returns The rounded number.
 */
export function roundHalfEven(value: number, precision: number): number {
    const factor = Math.pow(10, precision);
    const x = value * factor;
    const n = Math.round(x);
    return (Math.abs(x) % 1 === 0.5 ? (n % 2 === 0 ? n : n - 1) : n) / factor;
}

/**
 * Tries to parse a string as a float.
 *
 * @param value The string to parse.
 * @returns The parsed float, or undefined if the value is invalid.
 */
export function tryParseFloat(value: string | undefined): number | undefined {
    if (value === undefined) {
        return undefined;
    }
    const parsed = parseFloat(value);
    return Number.isNaN(parsed) ? undefined : parsed;
}

/**
 * Parses a string as a float or throws an error if input is not valid.
 *
 * @param value The string to parse.
 * @returns The parsed float.
 */
export function mustParseFloat(value: string | undefined): number {
    const parsed = tryParseFloat(value);
    if (parsed === undefined) {
        throw new Error('Invalid number');
    }
    return parsed;
}

/**
 * Clamps a number between a minimum and maximum value.
 *
 * @param value The number to clamp. If undefined, or NaN, the min value is returned.
 * @param limits An object containing the min and max values.
 * @returns The clamped value.
 */
export function clamp(
    value: number | undefined,
    { min, max }: { min: number; max: number },
): number {
    return typeof value !== 'number' || Number.isNaN(value)
        ? min
        : Math.max(min, Math.min(max, value));
}
