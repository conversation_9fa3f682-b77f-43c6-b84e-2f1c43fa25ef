import type { Meta, StoryObj } from '@storybook/html';
import { html } from '../../component';
import { namespaced } from '../../utilities/classes';
import iconNames from '../icon/icon-names';
import { Label, type LabelProps } from './label';
import { LabelGroup, type LabelGroupProps } from './group';

const meta: Meta = {
    title: 'Modules/Label',
};

export default meta;

export const Default: StoryObj = (props: LabelProps) => {
    return html('div')(
        {
            className: namespaced('l-box', 'u-gap--m'),
            css: {
                background: '#09090B',
            },
        },
        Label(props),
    ).render();
};
Default.argTypes = {
    text: { control: 'text' },
    tooltip: { control: 'text' },
    variant: {
        control: 'select',
        options: [
            'default',
            'light',
            'medium',
            'big',
            'box',
            'small-box',
            'subtle',
            'ribbon-l',
            'ribbon-r',
            'caption',
            'error',
            'bubble',
        ],
    },
    icon: { control: 'select', options: ['', ...iconNames] },
};
Default.args = {
    text: 'Bet amount',
    tooltip: 'Enter the amount you wish to bet',
    variant: 'default',
    icon: '',
};

export const provablyFair: StoryObj = () => {
    return html('div')(
        {
            className: namespaced('l-box', 'u-gap--m'),
            css: {
                background: '#09090B',
            },
        },
        html('a')(
            {
                href: '#',
                target: '_blank',
            },
            Label(
                {
                    variant: 'subtle',
                    icon: 'double-check',
                },
                html('span')({ className: namespaced('u-hide@m') }, 'This Game is'),
                'Provably Fair',
            ),
        ),
    ).render();
};
provablyFair.storyName = 'Provably Fair';

export const ribbons: StoryObj = () => {
    return html('div')(
        {
            className: namespaced('l-box', 'l-stack', 'u-gap--m'),
            css: {
                background: '#09090B',
            },
        },
        Label({
            text: 'Blackjack pays 3 to 2',
            variant: 'ribbon-l',
        }),
        Label({
            text: 'Insurance pays 2 to 1',
            variant: 'ribbon-r',
        }),
    ).render();
};
ribbons.storyName = 'Ribbons';

// Label group stories

export const LabelGroupDefault: StoryObj = (props: LabelGroupProps) => {
    return html('div')({ className: namespaced('l-box', 'u-gap--m') }, LabelGroup(props)).render();
};
LabelGroupDefault.argTypes = {
    text: { control: 'text' },
    value: { control: 'text' },
    variant: { control: 'select', options: ['default', 'box'] },
};
LabelGroupDefault.args = {
    text: 'Result',
    value: '0.1x',
    variant: 'default',
};

export const TwoLabelGroups: StoryObj = () => {
    return html('div')(
        {
            className: namespaced('l-box', 'u-gap--m', 'l-flex'),
        },
        LabelGroup({
            text: 'Target',
            value: '0.20000000x',
        }),
        LabelGroup({
            text: 'Result',
            value: html('span')(
                {
                    className: namespaced('m-label-group__value--highlight'),
                },
                '0.1',
            ),
        }),
    ).render();
};
TwoLabelGroups.storyName = 'Two Label Groups';
