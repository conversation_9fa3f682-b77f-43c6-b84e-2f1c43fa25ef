@use 'sass:math';
@use 'sass:meta';
@use 'sass:string';
@use '@monkey-tilt/ui/scss/common/bem';
@use '@monkey-tilt/ui/scss/common/prop';
@use '@monkey-tilt/ui/scss/common/respond';
@use '@monkey-tilt/ui/scss/common/util';
@use '@monkey-tilt/ui/scss/config/metrics';

$-assets: './modules/card/assets';

@function -sprite($name) {
    // @return url(#{$-assets}/decks/default.svg##{$name});
    @return url(#{$-assets}/decks/default/#{$name}.svg);
}

$-rW: 120;
$-rH: 165;
$-aspect-ratio: math.div($-rW, $-rH);
$-min-size: 70;
$-max-size: 120;
$-inline-size: metrics.fluid-size($-min-size, $-max-size);

$-padding: 5%;
$-value-size: 10%;

@mixin overlap($factor) {
    @include bem.module(card) {
        inline-size: calc(
            prop.get(card-size, metrics.fluid-size($-min-size, $-max-size)) * $factor *
                prop.get(card-scale, 1)
        );
        block-size: 100%;
        aspect-ratio: math.div($-rW * $factor, $-rH);
        border-radius: metrics.fluid-size(4, 8);

        @include prop.set(card-value-scale, $factor + 0.2);

        > * {
            block-size: 100%;
            aspect-ratio: $-aspect-ratio;
        }

        &:last-child {
            inline-size: prop.get(card-size, calc($-inline-size * prop.get(card-scale, 1)));
            aspect-ratio: $-aspect-ratio;
        }
    }
}

@mixin styles {
    @include bem.module('card') {
        position: relative;
        aspect-ratio: $-aspect-ratio;
        inline-size: prop.get(card-size, calc($-inline-size * prop.get(card-scale, 1)));
        max-inline-size: prop.get(card-size, none);
        border-radius: 5% / 3.5%;

        &__content {
            position: absolute;
            inset: 0;
            perspective: util.to-rem($-max-size * 3px);
            border-radius: 5% / 3.5%;
        }

        &__face,
        &__back {
            position: absolute;
            inset: 0;
            transition: transform prop.get(blackjack-anim-duration, 0.6s) ease;
            transform-style: preserve-3d;
            backface-visibility: hidden;
            border-radius: 5% / 3.5%;
            box-shadow:
                -3px 0px 4px -2px rgba(0, 0, 0, 0.1),
                -10px 25px 50px -12px rgba(0, 0, 0, 0.55);

            * {
                background: prop.get(card-sprite, prop.get(card-color)) center center no-repeat;
                mask: prop.get(card-suit, prop.get(null-mask)) center center no-repeat;
                mask-size: cover;
            }
        }

        &--face-down &__face {
            transform: rotateY(180deg);
        }
        &--face-down &__back {
            transform: rotateY(0);
        }

        &__face {
            background: prop.get(color-zinc-50);

            &::before,
            &::after {
                content: '';
                display: block;
                position: absolute;
                background: prop.get(card-color);
                mask: prop.get(card-value, prop.get(null-mask)) center center no-repeat;
                mask-size: cover;
                aspect-ratio: 1;
            }

            &::before {
                inline-size: calc(53.33% * prop.get(card-value-scale, 1));
                inset: calc(4% * prop.get(card-value-scale, 1)) auto auto
                    calc(1% * prop.get(card-value-scale, 1));
            }

            &::after {
                inline-size: calc(37.5% * prop.get(card-value-scale, 1));
                inset: calc(42% * prop.get(card-value-scale, 1)) auto auto
                    calc(8.6% * prop.get(card-value-scale, 1));

                @include prop.set(card-value, prop.get(card-suit));
            }
        }

        &__back {
            transform: rotateY(-180deg);
            background-size: cover;
            background-color: #202020;
            background-image: radial-gradient(
                    50% 50% at 50% 50%,
                    rgba(255, 229, 0, 0.18) 0%,
                    rgba(255, 229, 0, 0) 100%
                ),
                prop.get(card-noise, url(#{$-assets}/decks/default-back.png));

            @supports (
                background: image-set(url(#{$-assets}/decks/default-back.webp) type('image/webp'))
            ) {
                @include prop.set(
                    card-noise,
                    image-set(
                        url(#{$-assets}/decks/default-back.webp) type('image/webp'),
                        url(#{$-assets}/decks/default-back.png) type('image/png')
                    )
                );
            }

            &::before {
                content: '';
                position: absolute;
                inset: 0;
                border-radius: inherit;
                border: util.to-rem(1px) solid prop.get(color-mt-400);
            }

            > * {
                position: absolute;
                inline-size: 38%;
                aspect-ratio: 1;
                background: -sprite(mt) center no-repeat;
                background-size: cover;
                inset: 50% auto auto 50%;
                transform: translate(-50%, -50%);
                mask: none;
                backface-visibility: hidden;
            }
        }

        &--hearts,
        &--diamonds {
            @include prop.set(card-color, #cb0d07);
        }

        &--spades,
        &--clubs {
            @include prop.set(card-color, #000000);
        }

        @each $suit in (hearts, diamonds, spades, clubs) {
            &--#{$suit} {
                @include prop.set(card-suit, -sprite(string.slice($suit, 1, 1)));
            }
        }

        @each $value in (2, 3, 4, 5, 6, 7, 8, 9, 10, jack, queen, king, ace) {
            &--#{$value} {
                @include prop.set(
                    card-value,
                    -sprite(
                            if(meta.type-of($value) == 'string', string.slice($value, 1, 1), $value)
                        )
                );
            }
        }
    }
}
