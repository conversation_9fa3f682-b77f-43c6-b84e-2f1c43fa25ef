import { shuffle } from '@monkey-tilt/utils';
import { CELL_SIZE, PEG_RADIUS } from '../../../physics/constants';

/**
 * The encoded ball starting positions data for a specific board (i.e., number of rows).
 */
type StartingPositionsRawData = [
    [numberOfRows: number, xFactor: number, yFactor: number],
    ...[i: number, j: number][][],
];

export interface StartingPositionsPool {
    /**
     * Returns the next starting position for the given bucket number.
     */
    next(bucketNumber: number): [x: number, y: number];
}

/**
 * The cache of decoded starting positions data.
 */
const startingPositionsCache: Map<number, Map<number, [x: number, y: number][]>> = new Map();

/**
 * Fetches the starting positions for the given number of rows, decodes the data, and caches it.
 *
 * A Map that is returned contains a shuffled pool of coords for each bucket.
 *
 * @param rows - The number of rows for which to fetch starting positions.
 */
export async function getStartingPositions(rows: number): Promise<StartingPositionsPool> {
    if (rows < 8 || rows > 16 || !Number.isInteger(rows)) {
        throw new Error('Invalid number of rows');
    }

    let startingPositions = startingPositionsCache.get(rows);

    if (!startingPositions) {
        startingPositions = new Map();

        const [[, xFactor, yFactor], ...buckets] = (await import(`../../../data/${rows}.json`))
            .default as StartingPositionsRawData;

        const x = ((rows - 1) / 2 + 0.5) * CELL_SIZE - PEG_RADIUS * 2;
        const y = -(CELL_SIZE * 2);

        const dx = ((CELL_SIZE + PEG_RADIUS * 2) * 2) / (rows * xFactor);
        const dy = CELL_SIZE / 2 / ((rows / 2) * yFactor);

        for (let i = 0; i < buckets.length; i++) {
            startingPositions.set(
                i + 1,
                buckets[i]!.map(([i, j]) => [x + i * dx, y + j * dy]),
            );
        }

        startingPositionsCache.set(rows, startingPositions);
    }

    const data = new Map(
        Array.from(startingPositions.entries()).map(([bucketNumber, coords]) => [
            bucketNumber,
            { coords: shuffle(coords), nextIndex: 0 },
        ]),
    );

    return {
        next: (bucketNumber: number): [x: number, y: number] => {
            const pool = data.get(bucketNumber);

            if (pool === undefined) {
                throw new Error(`Invalid bucket number: ${bucketNumber}`);
            }

            const coords = pool.coords[pool.nextIndex]!;

            pool.nextIndex = (pool.nextIndex + 1) % pool.coords.length;

            return coords;
        },
    };
}
