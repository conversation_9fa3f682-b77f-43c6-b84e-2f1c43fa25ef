server {
    listen 80;
    listen  [::]:80;
    server_name  localhost;

    access_log /var/log/nginx/access.log;
    error_log /var/log/nginx/error.log;

    etag on;
    if_modified_since exact;
    expires off;

    root /app;

    location / {
        # Add CORS headers only if $cors_origin is set
        add_header 'Access-Control-Allow-Origin' "$cors_origin";
        add_header 'Access-Control-Allow-Methods' 'GET, POST, OPTIONS';
        add_header 'Access-Control-Allow-Headers' 'Authorization, Content-Type, X-Requested-With';

        # Handle preflight requests (OPTIONS)
        add_header 'Access-Control-Max-Age' 86400;
        if ($request_method = OPTIONS) {
            return 204;
        }
    }

    location ~ /\. {
        return 404;
    }

    location = /healthz {
        add_header Content-Type text/plain;
        return 200 'alive';
    }

    location = / {
        return 404;
    }

    error_page   500 502 503 504  /50x.html;
    location = /50x.html {
        root   /usr/share/nginx/html;
    }
}
