/* eslint-disable @typescript-eslint/no-unsafe-argument, @typescript-eslint/no-unsafe-call */
import { currencyToFixedString, formatCurrency, nearlyEqual, timer } from '@monkey-tilt/client';
import {
    defineComponent,
    html,
    Label,
    mergeClasses,
    withNamespace,
    type Component,
    type LabelControls,
    type Props,
    type RefObject,
} from '@monkey-tilt/ui';
import { type CrashUI } from '../../crash';

export interface BalanceProps extends Props<HTMLDivElement> {
    /**
     * The Crash UI state manager.
     */
    readonly ui: CrashUI;
}

export const Balance = defineComponent(
    'Balance',
    ({ state, render }) =>
        ({ ui, ...props }: BalanceProps): Component<HTMLDivElement> => {
            const payoutsRef: RefObject<HTMLDivElement> = { current: null };
            const { effect, memo, untracked } = state();

            const balanceDeltas: { text: string; isPositive: boolean }[] = [];
            const payoutsY = `--${withNamespace('payouts-y')}`;

            const duration = (ms: number) => (ui.isTurboMode ? 0.1 : ms);

            const showNextDelta = () => {
                if (balanceDeltas.length === 0) {
                    return;
                }

                const payouts = payoutsRef.current!;
                const prevHeight = payouts.offsetHeight;

                const { text, isPositive } = balanceDeltas.shift()!;

                const label = render(
                    Label({
                        text,
                        variant: 'caption',
                        className: { 'is-positive': isPositive, 'is-negative': !isPositive },
                    }),
                );
                payouts.appendChild(label);

                const animation = label.animate([{ opacity: 0 }, { opacity: 1 }], {
                    duration: 150,
                    fill: 'forwards',
                    easing: 'ease-out',
                });

                setTimeout(() => {
                    const delta = payouts.offsetHeight - prevHeight;
                    animation.onfinish = () => {
                        showDeltaDelay(showNextDelta);

                        setTimeout(() => {
                            label.animate([{ opacity: 1 }, { opacity: 0 }], {
                                duration: 100,
                                fill: 'forwards',
                                easing: 'ease-in',
                            });

                            const y = parseFloat(payouts.style.getPropertyValue(payoutsY) || '0');
                            payouts.style.setProperty(payoutsY, `${y - delta}px`);
                            payouts.addEventListener(
                                'transitionend',
                                () => {
                                    if (
                                        -parseFloat(
                                            payouts.style.getPropertyValue(payoutsY) || '0',
                                        ) >= payouts.offsetHeight
                                    ) {
                                        payouts.style.setProperty(payoutsY, '0');
                                        payouts.innerHTML = '';
                                    }
                                },
                                { once: true },
                            );
                        }, 2000);
                    };
                }, 100);
            };

            const showDeltaDelay = timer(300);
            const displayDelta = (amount: number, label = '') => {
                setTimeout(() => {
                    balanceDeltas.push({
                        text:
                            (amount > 0 ? '+' : '') +
                            currencyToFixedString(amount, untracked(ui.currency)) +
                            (label && ` ${label}`),
                        isPositive: amount >= 0,
                    });
                    if (payoutsRef.current) {
                        if (balanceDeltas.length === 1 || ui.isTurboMode) {
                            showNextDelta();
                        } else {
                            showDeltaDelay(showNextDelta);
                        }
                    }
                }, duration(100));
            };

            return html('div')(
                {
                    ...props,
                    className: mergeClasses(props.className, withNamespace('m-crash__balance')),
                },
                Label({
                    variant: 'caption',
                    controls({ setText }: LabelControls) {
                        const isAnimatingState = memo(() => ui.gameState().isAnimatingState);
                        const playerBalance = memo(() => ui.gameState().balance);

                        let currentBalance = playerBalance();
                        let targetBalance = currentBalance;
                        let updateInterval: ReturnType<typeof setInterval> | undefined = undefined;

                        const update = () => {
                            if (updateInterval) {
                                return;
                            }
                            const startingBalance = currentBalance;
                            updateInterval = setInterval(() => {
                                const delta = Math.abs(targetBalance - startingBalance) / 5;
                                if (targetBalance > currentBalance) {
                                    currentBalance = Math.min(
                                        currentBalance + delta,
                                        targetBalance,
                                    );
                                } else {
                                    currentBalance = Math.max(
                                        currentBalance - delta,
                                        targetBalance,
                                    );
                                }

                                if (nearlyEqual(currentBalance, targetBalance)) {
                                    clearInterval(updateInterval);
                                    updateInterval = undefined;
                                    currentBalance = targetBalance;
                                }

                                setText(
                                    `Balance: ${formatCurrency(currentBalance, untracked(ui.currency))}`,
                                );
                            }, 50);
                        };

                        const payout = memo(() => {
                            const {
                                round_closed = false,
                                isAutobetActive = false,
                                isAnimatingState = false,
                                next_actions = [],
                                total_payout = '0',
                            } = ui.gameState();
                            return !isAnimatingState &&
                                (round_closed ||
                                    (isAutobetActive && next_actions.includes('Nextbet')))
                                ? Number.parseFloat(total_payout)
                                : 0;
                        });

                        effect(() => {
                            const payoutAmount = payout();
                            if (payoutAmount !== 0) {
                                displayDelta(payoutAmount);
                            }
                        });

                        let didCurrencyChange = false;

                        effect(() => {
                            void ui.currency();
                            didCurrencyChange = true;
                        });

                        effect(() => {
                            const newBalance = playerBalance();
                            if (isAnimatingState()) {
                                return;
                            }
                            targetBalance = newBalance;
                            if (updateInterval) {
                                clearInterval(updateInterval);
                                updateInterval = undefined;
                            }
                            if (didCurrencyChange) {
                                didCurrencyChange = false;
                                currentBalance = newBalance;
                                setText(
                                    `Balance: ${formatCurrency(currentBalance, untracked(ui.currency))}`,
                                );
                            } else {
                                setTimeout(update, duration(750));
                            }
                        });
                    },
                }),
                html('div')(
                    { className: withNamespace('m-crash__payouts') },
                    html('div')({ ref: payoutsRef }),
                ),
            );
        },
);
