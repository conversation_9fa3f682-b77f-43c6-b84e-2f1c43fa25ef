import {
    create,
    defineComponent,
    forwardRef,
    html,
    type Props,
    type RefObject,
    type Renderable,
} from '../../component';
import type { Reactive } from '../../state';
import { mergeClasses, namespaced } from '../../utilities/classes';
import { Tooltip } from '../tooltip/tooltip';

/**
 * The available icon names.
 */
export type IconName =
    | 'arrow-down'
    | 'arrow-left'
    | 'arrow-right'
    | 'arrow-up'
    | 'ask'
    | 'bolt'
    | 'cards'
    | 'chevron-l'
    | 'chevron-r'
    | 'chevron'
    | 'chips'
    | 'circle-dollar'
    | 'clear'
    | 'clockwise'
    | 'close'
    | 'coins'
    | 'copy'
    | 'double-check'
    | 'error'
    | 'flag'
    | 'hand'
    | 'infinity'
    | 'info'
    | 'keyboard'
    | 'lock'
    | 'multiply'
    | 'music'
    | 'notepad'
    | 'plus'
    | 'raise'
    | 'risk'
    | 'rows'
    | 'sound'
    | 'stop'
    | 'switch'
    | 'undo'
    | 'x';

export type IconProps = Props<HTMLElement> & {
    /**
     * The optional icon description, used for accessibility.
     */
    readonly description?: string;

    /**
     * The optional tooltip to display on hover.
     */
    readonly tooltip?: Renderable;
} & (
        | {
              /**
               * The icon name.
               */
              readonly name: IconName;
          }
        | {
              /**
               * The currency code.
               */
              readonly currency: Reactive<string>;
          }
    );

export const Icon = defineComponent(
    'Icon',
    ({ state }) =>
        ({ description, tooltip, ...props }: IconProps): HTMLElement => {
            let currency: Reactive<string> | undefined;
            let name = 'currency';

            if ('name' in props) {
                name = props.name;
                delete (props as { name?: IconName }).name;
            } else {
                currency = props.currency;
                delete (props as { currency?: string }).currency;
            }

            if (description) {
                props = {
                    ...props,
                    children: [
                        description &&
                            html('span')({
                                children: (props.children ?? []).concat([description]),
                                className: namespaced('u-hide-visually'),
                            }),
                    ],
                };
            }

            const iconRef: RefObject<HTMLElement> = { current: null };

            if (tooltip) {
                props = {
                    ...props,
                    children: (props.children ?? []).concat([
                        Tooltip({ target: iconRef }, tooltip),
                    ]),
                };
            }

            return create('i', {
                props: {
                    ...props,
                    ref: forwardRef(props.ref, iconRef, (element: HTMLElement) => {
                        if (!currency) {
                            return;
                        }
                        const { onPropChange } = state();
                        onPropChange(currency, (value) => {
                            element.setAttribute('data-currency', value);
                        });
                    }),
                    className: mergeClasses(
                        props.className,
                        namespaced('m-icon', `m-icon--${name}`),
                    ),
                },
            });
        },
);
