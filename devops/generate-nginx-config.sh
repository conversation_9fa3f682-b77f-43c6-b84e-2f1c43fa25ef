#!/bin/sh

ALLOWED_ORIGINS="$1"
CONFIG_PATH="$2"

# Begin map configuration
echo "map \$http_origin \$cors_origin {" > "$CONFIG_PATH"
echo "    default \"\";" >> "$CONFIG_PATH"

# Loop through each domain by splitting on commas
echo "$ALLOWED_ORIGINS" | tr ',' '\n' | while read -r DOMAIN; do
    echo "    \"$DOMAIN\" \"$DOMAIN\";" >> "$CONFIG_PATH"
done

# End map configuration
echo "}" >> "$CONFIG_PATH"

# Append the rest of the Nginx configuration
cat /etc/nginx/conf.d/default.conf.template >> "$CONFIG_PATH"
