import type { <PERSON>A<PERSON>Name } from '@monkey-tilt/client';
import type { BaccaratState } from './state';

interface GameStateUpdate extends Partial<BaccaratState> {
    readonly next_actions: ReadonlyArray<GameActionName>;
}

declare module '@monkey-tilt/client' {
    export interface MessageTypes {
        readonly Autobet: {
            readonly request: {
                readonly currency: string;
                readonly player_bet_amount?: number;
                readonly banker_bet_amount?: number;
                readonly tie_bet_amount?: number;
                readonly dragon_7_bet_amount?: number;
                readonly panda_bet_amount?: number;
                readonly autobet_limit?: number;
                readonly on_win?: number;
                readonly on_win_reset?: boolean;
                readonly on_loss?: number;
                readonly on_loss_reset?: boolean;
                readonly stop_on_profit?: number;
                readonly stop_on_loss?: number;
            };
            readonly response: GameStateUpdate;
        };

        readonly Nextbet: {
            readonly request: void;
            readonly response: GameStateUpdate;
        };

        readonly CloseAutobet: {
            readonly request: void;
            readonly response: GameStateUpdate;
        };
    }

    export interface BetPayload {
        readonly currency: string;
        readonly player_bet_amount?: number;
        readonly banker_bet_amount?: number;
        readonly tie_bet_amount?: number;
        readonly dragon_7_bet_amount?: number;
        readonly panda_bet_amount?: number;
    }

    export interface GameState extends BaccaratState {}
}

export {};
