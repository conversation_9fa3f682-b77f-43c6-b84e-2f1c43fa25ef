import { format<PERSON>ur<PERSON>cy, BetAmount, Options } from '@monkey-tilt/client';
import {
    Button,
    defineComponent,
    Flex,
    Frame,
    html,
    Label,
    mergeClasses,
    namespaced,
    Stack,
    Tabs,
    withNamespace,
    type Component,
    type Props,
    type TabControls,
} from '@monkey-tilt/ui';
import { type KenoUI } from '../../keno';
import { Autobet } from './autobet';

export interface SidebarProps extends Props<HTMLDivElement> {
    /**
     * The Keno UI state manager.
     */
    readonly ui: KenoUI;
}

export const Sidebar = defineComponent(
    'Sidebar',
    ({ state }) =>
        ({ ui, ...props }: SidebarProps): Component<HTMLDivElement> => {
            const { effect, memo } = state();

            const div = html('div');

            const actionState = memo(() => {
                const {
                    next_actions = [],
                    round_closed = true,
                    readyState = 'closed',
                    isAuthenticated = false,
                    isLoading,
                    isAnimatingState,
                } = ui.gameState();
                return {
                    next_actions,
                    round_closed,
                    canPlay:
                        !isLoading &&
                        !isAnimatingState &&
                        readyState === 'ready' &&
                        isAuthenticated,
                };
            });

            const isAutobetActive = memo(() => ui.gameState().isAutobetActive);

            const limits = memo(() => ui.gameState().bet_limits.bet);

            const betButton = Button({
                cta: true,
                label: memo(() => (isAutobetActive() ? 'Stop' : 'Bet')),
                icon: 'plus',
                className: { 'm-keno__bet-button': true },
                ref: (element: HTMLButtonElement) => {
                    effect(() => {
                        element.disabled =
                            ui.chosen_numbers.read().length == 0 ||
                            !ui.isBetAllowed() ||
                            (ui.isBettingLocked() &&
                                (!isAutobetActive() || ui.shouldCloseAutobet()));
                    });
                },
                onClick() {
                    ui.triggerAction('BetOrStop');
                },
            });

            const handleActionAvailable =
                (action: 'clear' | 'auto-pick') => (element: HTMLButtonElement) => {
                    effect(() => {
                        const chosen_numbers = ui.chosen_numbers.read();
                        const isDisabled =
                            ui.isBettingLocked() ||
                            ui.pickingInProgress.read() ||
                            (action == 'clear' && chosen_numbers.length == 0) ||
                            (action == 'auto-pick' && chosen_numbers.length > 50);

                        element.disabled = isDisabled;
                    });
                };

            const handleRiskButton = (n: number) => (element: HTMLButtonElement) => {
                effect(() => {
                    element.classList.toggle('pressed', ui.risk.read() == n);
                });
                effect(() => {
                    const isDisabled = ui.isBettingLocked();
                    if (element && 'disabled' in element) {
                        element.disabled = isDisabled;
                    }
                });
            };

            const hideOnMobile = withNamespace('u-hide@m');
            const showOnMobile = withNamespace('u-show@m');

            return Frame(
                { ...props, className: { 'm-keno__sidebar': true } },

                Frame.Section(
                    {
                        variant: 'subtle',
                        className: { 'm-keno__tabs': true },
                        ref: (element: HTMLDivElement) => {
                            effect(() => {
                                element.classList.toggle(hideOnMobile, ui.isBettingLocked());
                            });
                        },
                    },
                    Tabs({
                        ariaLabel: 'Bet type',
                        value: ui.activeTab.read(),
                        tabs: [
                            { label: 'Manual', value: 'manual' },
                            { label: 'Auto', value: 'auto' },
                        ],
                        onChange(value: 'manual' | 'auto') {
                            ui.activeTab.update(value);
                        },
                        controls({ selectTab, setEnabled }: TabControls) {
                            effect(() => {
                                selectTab(ui.activeTab.read());
                            });
                            effect(() => {
                                setEnabled(!ui.isBettingLocked());
                            });
                        },
                    }),
                ),

                Frame.Section(
                    {
                        variant: 'subtle',
                        className: mergeClasses(showOnMobile, { 'l-flex': true }),
                    },
                    betButton,
                ),

                Frame.Section(
                    {
                        variant: 'subtle',
                        className: { 'u-gap--l': true, 'm-keno__controls': true },
                    },

                    Stack(
                        { gap: 'xs' },
                        Stack(
                            { scroll: true, css: { padding: '4px' } },
                            Stack(
                                {
                                    gap: 'm',
                                },

                                BetAmount({
                                    label: 'Bet Amount',
                                    value: ui.betAmount,
                                    disabled: ui.isBettingLocked,
                                    limits,
                                    step: 0.01,
                                    presets: [0.1, 1.0, 10.0],
                                    currency: ui.currency,
                                    conversionFactor: ui.usdRate,
                                    onMultiply: (factor) => {
                                        ui.triggerAction('MultiplyBetAmount', factor);
                                    },
                                }),
                                Label({
                                    variant: 'error',
                                    className: { 'u-hide': true },
                                    ref: (element: HTMLSpanElement) => {
                                        const hide = withNamespace('u-hide');
                                        effect(() => {
                                            element.classList.toggle(hide, ui.isBetAllowed());
                                        });
                                    },
                                    text: memo(() => [
                                        'The maximum bet amount is ',
                                        formatCurrency(ui.maxBetAmount(), ui.currency()) + '.',
                                        html('br')(),
                                        ui.defaultMaxBetAmount() > ui.maxBetAmount() &&
                                            (ui.risk.read() != 1
                                                ? 'Lower the Risk or pick fewer numbers to increase maximum bet.'
                                                : 'Pick fewer numbers to increase maximum bet.'),
                                    ]),
                                }),
                                Stack(
                                    {
                                        className: { 'm-keno__risk': true },
                                    },
                                    Stack(
                                        { gap: 's' },
                                        Label(
                                            {},
                                            html('span')(
                                                {
                                                    className: {
                                                        'm-label': true,
                                                    },
                                                },
                                                'Risk',
                                            ),
                                        ),
                                        Flex(
                                            { wrap: false, gap: 'xs', justify: 'space-evenly' },
                                            Button({
                                                css: { flex: '1', padding: '10px 8px' },
                                                label: 'Classic',
                                                variant: 'risk--classic',
                                                ref: handleRiskButton(0),
                                                onClick: () => ui.triggerAction('PickRisk', 0),
                                            }),
                                            Button({
                                                label: 'Low',
                                                variant: 'risk--low',
                                                css: { flex: '1', padding: '10px 8px' },
                                                ref: handleRiskButton(1),
                                                onClick: () => ui.triggerAction('PickRisk', 1),
                                            }),
                                            Button({
                                                css: { flex: '1', padding: '10px 8px' },
                                                label: 'Medium',
                                                variant: 'risk--medium',
                                                ref: handleRiskButton(2),
                                                onClick: () => ui.triggerAction('PickRisk', 2),
                                            }),
                                            Button({
                                                css: { flex: '1', padding: '10px 8px' },
                                                label: 'High',
                                                variant: 'risk--high',
                                                ref: handleRiskButton(3),
                                                onClick: () => ui.triggerAction('PickRisk', 3),
                                            }),
                                        ),
                                    ),

                                    Flex(
                                        {
                                            justify: 'space-between',
                                            gap: 's',
                                            className: { 'm-keno__pick': true },
                                        },
                                        Button({
                                            variant: 'secondary',
                                            ref: handleActionAvailable('auto-pick'),
                                            onClick: () => ui.triggerAction('AutoPick'),
                                            label: 'AUTO PICK',
                                        }),
                                        Button({
                                            variant: 'secondary',
                                            ref: handleActionAvailable('clear'),
                                            onClick: () => ui.triggerAction('Clear'),
                                            label: 'CLEAR TABLE',
                                        }),
                                    ),
                                ),
                            ),

                            Autobet({
                                ui,
                                actionState,
                                ref(element: HTMLDivElement) {
                                    const hide = withNamespace('u-hide');
                                    effect(() => {
                                        element.classList.toggle(
                                            hide,
                                            ui.activeTab.read() !== 'auto',
                                        );
                                    });
                                },
                            }),
                        ),
                        div(
                            { className: mergeClasses(hideOnMobile, { 'l-flex': true }) },
                            betButton,
                        ),
                    ),
                ),

                Frame.Section(
                    {
                        className: {
                            'l-stack__split': true,
                            'l-stack': true,
                            'u-gap--s': true,
                        },
                    },
                    Options({ ui, namespace: 'keno' }),
                    html('button')(
                        {
                            onClick(e) {
                                e.preventDefault();
                                ui.provablyFairModalControlsRef.current?.open();
                            },
                            className: { 'u-text--center': true },
                            css: { 'border-width': '0' },
                        },
                        Label(
                            { variant: 'subtle', icon: 'double-check' },
                            html('span')({ className: namespaced('u-hide@m') }, 'This Game is'),
                            'Provably Fair',
                        ),
                    ),
                ),
            );
        },
);
