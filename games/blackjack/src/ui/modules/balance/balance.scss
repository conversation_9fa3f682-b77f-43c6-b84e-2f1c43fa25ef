@use '@monkey-tilt/ui/scss/common/bem';
@use '@monkey-tilt/ui/scss/common/prop';

@mixin styles {
    @include bem.module('blackjack__balance') {
        position: absolute;
        inset: 0 auto auto 0;
        pointer-events: none;
        padding: prop.get(size-m) prop.get(size-m-l);
    }

    @include bem.module('blackjack__payouts') {
        overflow: hidden;

        > * {
            display: flex;
            flex-direction: column;
            white-space: nowrap;
            transition: 500ms ease-out;
            transform: translateY(prop.get(payouts-y, 0));
        }

        #{bem.block-selector(module, label)} {
            opacity: 0;

            &#{bem.namespaced-selector(is-positive)} {
                color: prop.get(color-green-400);
            }

            &#{bem.namespaced-selector(is-negative)} {
                color: prop.get(color-red-400);
            }
        }
    }
}
