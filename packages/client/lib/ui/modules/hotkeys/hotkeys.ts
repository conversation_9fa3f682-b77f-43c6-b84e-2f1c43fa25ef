import {
    defineComponent,
    Frame,
    html,
    Icon,
    KeyMapping,
    Label,
    Modal,
    RefObject,
    Toggle,
    type Component,
    type ModalControls,
    type ModalProps,
    type ToggleControls,
} from '@monkey-tilt/ui';

interface StateManager {
    keyboardShortcuts: IterableIterator<KeyMapping<string, unknown>>;
    config: {
        hotkeys: {
            read: () => boolean;
            update: (value: boolean | ((prevValue: boolean) => boolean)) => void;
        };
    };
    hotkeysModalControlsRef: RefObject<ModalControls>;
}

export interface HotkeysProps extends ModalProps {
    /**
     * The Game UI state manager.
     */
    readonly ui: StateManager;
}

export const Hotkeys = defineComponent(
    'Hotkeys',
    ({ state }) =>
        ({ ui, ...props }: HotkeysProps): Component<HTMLDialogElement> => {
            const { effect } = state();

            return Modal(
                {
                    title: 'Hotkeys',
                    controls: (controls: ModalControls) => {
                        ui.hotkeysModalControlsRef.current = controls;
                    },
                    ...props,
                },
                html('dl')(
                    { role: 'list' },
                    ...Array.from(ui.keyboardShortcuts)
                        .filter(({ hidden }) => !hidden)
                        .map(({ key, description }) =>
                            html('div')(
                                null,
                                html('dt')(null, description),
                                html('dd')(null, html('kbd')(null, key)),
                            ),
                        ),
                ),
                Frame(
                    { variant: 'medium' },
                    html('div')(
                        { className: { 'l-cluster': true } },
                        Label({ variant: 'light', htmlFor: 'hotkeys' }, 'Enable Hotkeys'),
                        Toggle({
                            id: 'hotkeys',
                            autofocus: true,
                            className: { 'l-cluster__split': true },
                            checked: ui.config.hotkeys.read(),
                            onCheckedChange: (checked: boolean) =>
                                ui.config.hotkeys.update(checked),
                            controls({ setChecked }: ToggleControls) {
                                effect(() => {
                                    setChecked(ui.config.hotkeys.read());
                                });
                            },
                        }),
                    ),
                    Label(
                        { variant: 'subtle', className: { 'l-cluster': !true } },
                        Icon({
                            name: 'info',
                            description: 'Hint',
                            css: { 'align-self': 'flex-start' },
                        }),
                        html('span')(
                            null,
                            'When the hotkeys are enabled, they will remain on for all games until disabled. ' +
                                "Despite some games sharing similar key binds, it's always advised to confirm " +
                                'what key interactions are set for each game.',
                        ),
                    ),
                ),
            );
        },
);
