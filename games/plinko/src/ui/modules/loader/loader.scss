@use '@monkey-tilt/ui/scss/common/bem';
@use '@monkey-tilt/ui/scss/common/prop';
@use '@monkey-tilt/ui/scss/common/respond';
@use '@monkey-tilt/ui/scss/common/util';

@mixin styles {
    $-tilt-animation: #{lib-config(ns)}loader-tilt;
    $-glow-animation: #{lib-config(ns)}loader-glow;

    @include bem.module('loader') {
        position: absolute;
        inset: 0;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        gap: prop.get(size-m-xl);
        transition: opacity 0.4s ease-in;

        &--hidden {
            opacity: 0;
            pointer-events: none;
        }

        &__image {
            inline-size: 30%;
            max-inline-size: calc(5 * prop.get(size-xl));
            aspect-ratio: 1 / 1;
            background: url(./modules/loader/assets/mt.svg) no-repeat;
            background-size: cover;
            filter: drop-shadow(0 0 util.to-rem(1px) rgba(255, 255, 255, 0.75));
            animation: $-tilt-animation 8s infinite;
            transform-origin: 50% 80%;
        }

        &__text {
            animation: $-glow-animation 5s infinite;
        }
    }

    @keyframes #{$-tilt-animation} {
        0%,
        100% {
            transform: rotate(0);
        }
        10%,
        30%,
        50%,
        70%,
        90% {
            transform: rotate(-5deg);
        }
        20%,
        40%,
        60%,
        80% {
            transform: rotate(5deg);
        }
    }

    @keyframes #{$-glow-animation} {
        0%,
        100% {
            text-shadow: 0 0 0 rgba(255, 255, 255, 0.75);
            transform: scale(1);
        }
        50% {
            text-shadow: 0 0 util.to-rem(2px) rgba(255, 255, 255, 0.25);
            transform: scale(1.05);
        }
    }
}
