import type { Repository } from '.';
import type { EffectConstructor, Signal, SignalConstructor, SignalUpdater, Store } from '../store';

export interface LocalStorageRepositoryConfiguration extends Pick<Store, 'effect' | 'signal'> {
    readonly storageKey: string;
}

export class LocalStorageRepository<T> implements Repository<T> {
    #storageKey: string;
    #effect: EffectConstructor;
    #signal: SignalConstructor;
    #data: Map<string, T> = new Map();
    #signalUpdaters: Map<string, { update: SignalUpdater<any>; remove: () => void }> = new Map();

    public constructor({ storageKey, effect, signal }: LocalStorageRepositoryConfiguration) {
        this.#storageKey = storageKey;
        this.#effect = effect;
        this.#signal = signal;
    }

    #load(shouldReload = false) {
        if (!shouldReload && this.#data.size > 0) {
            return;
        }
        this.#data.clear();
        const data = localStorage.getItem(this.#storageKey);
        if (data) {
            // TODO: validate parsed data!
            for (const [key, value] of Object.entries(JSON.parse(data) as Record<string, T>)) {
                this.#data.set(key, value);
            }
        }
    }

    #store() {
        localStorage.setItem(this.#storageKey, JSON.stringify(Object.fromEntries(this.#data)));
    }

    #update<K extends keyof T>(scope: string, key: K, value: T[K]) {
        this.#load(true);
        this.#data.set(scope, {
            ...(this.#data.get(scope) ?? ({} as T)),
            [key]: value,
        });
        if (scope !== 'default') {
            this.#data.set('default', {
                ...(this.#data.get('default') ?? ({} as T)),
                [key]: value,
            });
        }
        this.#store();
    }

    public signal<K extends keyof T>(scope: string, key: K, initialValue: T[K]): Signal<T[K]> {
        this.#load();

        const signal = this.#signal<T[K]>(this.#data.get(scope)?.[key] ?? initialValue);

        this.#signalUpdaters.set(`${scope}/${String(key)}`, {
            update: signal.update,
            remove: this.#effect(() => {
                this.#update(scope, key, signal.read());
            }),
        });

        return signal;
    }

    public get<K extends keyof T>(scope: string, key: K): T[K] | undefined {
        this.#load();
        return this.#data.get(scope)?.[key];
    }

    public set<K extends keyof T>(scope: string, key: K, value: T[K]): void {
        const signalId = `${scope}/${String(key)}`;

        if (this.#signalUpdaters.has(signalId)) {
            this.#signalUpdaters.get(signalId)!.update(value);
        } else {
            this.#update(scope, key, value);
        }
    }

    public delete<K extends keyof T>(scope: string, key: K): void {
        this.#load();
        const data = this.#data.get(scope);
        if (data) {
            delete data[key];
            this.#store();
        }

        const signalId = `${scope}/${String(key)}`;
        if (this.#signalUpdaters.has(signalId)) {
            this.#signalUpdaters.get(signalId)!.remove();
            this.#signalUpdaters.delete(signalId);
        }
    }

    public clear(scope: string): void {
        this.#load();

        if (this.#data.has(scope)) {
            this.#data.delete(scope);
            this.#store();
        }

        const keys = Array.from(this.#signalUpdaters.keys()).filter((key) =>
            key.startsWith(`${scope}/`),
        );
        for (const key of keys) {
            this.#signalUpdaters.get(key)!.remove();
            this.#signalUpdaters.delete(key);
        }
    }
}
