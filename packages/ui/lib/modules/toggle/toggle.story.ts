import type { <PERSON>a, StoryObj } from '@storybook/html';
import { fn } from '@storybook/test';
import { Toggle, type ToggleProps } from './toggle';
import { html } from '../../component';
import { namespaced } from '../../utilities';

const meta: Meta = {
    title: 'Modules/Toggle',
};
export default meta;

export const Default: StoryObj = (props: ToggleProps) => {
    return html('div')(
        {
            className: namespaced('l-box', 'u-gap--xl'),
            css: { background: '#09090B' },
        },
        Toggle(props),
    ).render();
};
Default.argTypes = {
    checked: { control: 'boolean' },
    disabled: { control: 'boolean' },
};
Default.args = {
    checked: false,
    disabled: false,
    onCheckedChange: fn(),
};
