import type { Meta, StoryObj } from '@storybook/html';
import { Tile, type TileProps } from './tile';
import { html } from '../../component';
import { withNamespace } from '../../utilities/classes';

const meta: Meta = {
    title: 'Modules/Tile',
};
export default meta;

export const Single: StoryObj<TileProps> = {
    render: (props) => {
        return Tile({ ...props, number: 7 }).render();
    },
    args: {
        isSelected: false,
        isHit: false,
        disabled: false,
        isMiss: false,
    },
    argTypes: {
        isSelected: { control: 'boolean' },
        isHit: { control: 'boolean' },
        disabled: { control: 'boolean' },
        isMiss: { control: 'boolean' },
    },
};
Single.storyName = 'Single';

export const Board: StoryObj<TileProps> = {
    render: (props) => {
        const wrapper = html('div')({
            className: withNamespace('m-tile-board'),
        }).render();

        for (let i = 1; i <= 40; i++) {
            const tile = Tile({ ...props, number: i }).render();
            wrapper.appendChild(tile);
        }

        return wrapper;
    },
};
Board.storyName = 'Board';
