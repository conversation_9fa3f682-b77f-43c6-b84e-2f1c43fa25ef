import type { GameActionName } from '@monkey-tilt/client';
import type { PlinkoState } from './state';

interface GameStateUpdate extends Partial<PlinkoState> {
    readonly next_actions: ReadonlyArray<GameActionName>;
}

declare module '@monkey-tilt/client' {
    export interface MessageTypes {
        readonly Autobet: {
            readonly request: AutobetPayload;
            readonly response: GameStateUpdate;
        };

        readonly Nextbet: {
            readonly request: void;
            readonly response: GameStateUpdate;
        };

        readonly CloseAutobet: {
            readonly request: void;
            readonly response: GameStateUpdate;
        };
    }

    export interface BetPayload {
        readonly currency: string;
        readonly bet_amount?: number;
        readonly number_of_rows?: number;
        readonly risk?: number;
    }

    export interface AutobetPayload extends BetPayload {
        readonly autobet_limit?: number;
        readonly on_win?: number;
        readonly on_win_reset?: boolean;
        readonly on_loss?: number;
        readonly on_loss_reset?: boolean;
        readonly stop_on_profit?: number;
        readonly stop_on_loss?: number;
    }

    export interface GameState extends PlinkoState {}
}

export {};
