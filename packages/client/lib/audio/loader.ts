export interface Source {
    readonly src: string;
    readonly type: string;
}

const supportedTypes = new Map<string, boolean>();
let audioElement: HTMLAudioElement | null = null;

function canPlayType(mimeType: string): boolean {
    if (supportedTypes.has(mimeType)) {
        return supportedTypes.get(mimeType)!;
    }

    let isSupported = false;

    if (!audioElement) {
        audioElement = document.createElement('audio');
    }

    isSupported = audioElement.canPlayType(mimeType) === 'probably';
    supportedTypes.set(mimeType, isSupported);

    return isSupported;
}

/**
 * Load an audio buffer from a supported source.
 *
 * @param context - The audio context to use (preferably an OfflineAudioContext).
 * @param sources - The sources to load from, in order of preference.
 * @returns A Promise that resolves with the loaded AudioBuffer.
 */
export async function loadAudio(
    context: BaseAudioContext,
    sources: ReadonlyArray<Source>,
): Promise<AudioBuffer> {
    if (!sources.length) {
        return context.createBuffer(1, 1, 44100);
    }

    const supported = sources.find(({ type }) => canPlayType(type));

    if (supported) {
        return await loadAudioBuffer(context, supported.src);
    }

    return new Promise((resolve, reject) => {
        try {
            const audio = document.createElement('audio');

            for (const { src, type } of sources) {
                const source = document.createElement('source');
                source.src = src;
                source.type = type;
                audio.appendChild(source);
            }

            audio.addEventListener(
                'canplaythrough',
                () => {
                    const url = audio.currentSrc || audio.src;
                    const type = sources.find(({ src }) => src === url)?.type;

                    if (type) {
                        supportedTypes.set(type, true);
                    }

                    loadAudioBuffer(context, url).then(resolve, reject);
                },
                { once: true },
            );
        } catch (err) {
            reject(err && err instanceof Error ? err : new Error('Failed to load audio'));
        }
    });
}

async function loadAudioBuffer(context: BaseAudioContext, url: string): Promise<AudioBuffer> {
    const response = await fetch(url);
    const arrayBuffer = await response.arrayBuffer();
    return await context.decodeAudioData(arrayBuffer);
}
