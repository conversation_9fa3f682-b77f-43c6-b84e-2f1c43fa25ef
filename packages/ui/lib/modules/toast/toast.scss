@use '../../styles/common/bem';
@use '../../styles/common/prop';
@use '../../styles/common/type-style' as *;
@use '../../styles/common/util';

@mixin styles {
    @include bem.module('toast') {
        position: relative;
        overflow: hidden;
        display: grid;
        grid-template-columns: 1fr;
        gap: prop.get(size-xs);
        border-radius: prop.get(size-xs-sm);
        border: util.to-rem(1px) solid prop.get(color-zinc-800);
        background: prop.get(color-zinc-950) linear-gradient(135deg, #212121 0%, transparent 100%);
        color: prop.get(color-white);
        inline-size: max-content;
        min-inline-size: 25%;
        max-inline-size: 75%;
        overflow: hidden;
        max-block-size: prop.get(toast-block-size, none);
        opacity: 1;
        cursor: default;
        pointer-events: all;
        flex-shrink: 0;
        padding: prop.get(size-xs-sm);
        transition:
            opacity prop.get(toast-transition-duration, 0.6s) ease,
            max-block-size prop.get(toast-transition-duration, 0.3s) ease;

        &#{bem.namespaced-selector('is-dismissed')} {
            opacity: 0;
            pointer-events: none;
        }

        &:has(&__dismiss) {
            grid-template-columns: auto min-content;
        }

        &::before {
            content: '';
            position: absolute;
            inline-size: util.to-rem(212px);
            block-size: util.to-rem(212px);
            inset: util.to-rem(-99px) auto auto util.to-rem(-99px);
            background: radial-gradient(
                50% 50% at 50% 50%,
                prop.get(toast-highlight, transparent) 0%,
                transparent 100%
            );
            opacity: 0.25;
            pointer-events: none;
        }

        &__dismiss {
            border: none;
            background: none;
            padding: 0;
        }

        &__content {
            position: relative;
            display: flex;
            align-items: center;
            gap: prop.get(size-xs);

            @include type-style('alert');
        }

        &__text {
            display: grid;
            grid-template-rows: 1.5fr 0.7fr;
            align-items: center;
            gap: prop.get(toast-gap, prop.get(size-2xs-s));
            font-size: 90%;
        }

        &__label {
            position: relative;
            inline-size: 100%;
            flex: 1 0.5 auto;
            padding-block-start: prop.get(toast-gap, prop.get(size-2xs-s));
            text-align: center;
            font-size: 50%;
            opacity: 0.7;

            &::before {
                content: '';
                position: absolute;
                inset: 0 15% auto 15%;
                display: block;
                block-size: util.to-rem(1px);
                background: color-mix(in srgb, currentColor 40%, transparent);
            }
        }

        &--error {
            @include prop.set(toast-highlight, prop.get(color-red-400));
        }

        &--status {
            border-radius: util.to-rem(24px);
            color: prop.get(color-zinc-350);
            background: color-mix(in srgb, currentColor 10%, transparent);
            border: util.to-rem(1px) solid currentColor;
            backdrop-filter: blur(util.to-rem(20px));
            padding: 0 prop.get(size-xs);

            @include prop.set(toast-gap, 0);
        }

        &--status &__content {
            justify-content: center;
            padding: prop.get(size-2xs) prop.get(size-l);

            @include type-style('alert-xl');
        }

        &--win {
            color: prop.get(color-green-500);
        }

        &--lose {
            color: prop.get(color-red-500);
        }

        &--tie {
            color: prop.get(color-orange-400);
        }

        &-bar {
            display: flex;
            flex-direction: column;
            justify-content: flex-end;
            align-items: center;
            position: fixed;
            padding: prop.get(size-l);
            inset: 0;
            pointer-events: none;

            > * {
                margin-block-end: prop.get(size-m);
            }

            &--start {
                justify-content: flex-start;
            }

            &--center {
                justify-content: center;
            }
        }
    }
}
