{"name": "@monkey-tilt/client", "version": "0.1.0-dev", "description": "Monkey Tilt Original Games API Client library", "private": true, "type": "module", "license": "UNLICENSED", "author": "Monkey Tilt", "contributors": ["Alek<PERSON><PERSON> <<EMAIL>>"], "files": ["dist"], "module": "./dist/client.es.js", "main": "./dist/client.umd.js", "types": "./dist/index.d.ts", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/client.es.js", "require": "./dist/client.umd.js"}}, "scripts": {"build": "vite build", "lint": "eslint ./lib", "typecheck": "tsc --noEmit"}, "peerDependencies": {"@monkey-tilt/ui": "workspace:*", "@monkey-tilt/utils": "workspace:*"}, "devDependencies": {"@eslint/js": "catalog:eslint", "@monkey-tilt/ui": "workspace:*", "@monkey-tilt/utils": "workspace:*", "@types/node": "catalog:typescript", "eslint": "catalog:eslint", "typescript-eslint-language-service": "catalog:eslint", "typescript-eslint": "catalog:eslint", "typescript": "catalog:typescript", "vite-plugin-dts": "catalog:vite", "vite": "catalog:vite"}, "packageManager": "pnpm@9.15.9+sha512.68046141893c66fad01c079231128e9afb89ef87e2691d69e4d40eee228988295fd4682181bae55b58418c3a253bde65a505ec7c5f9403ece5cc3cd37dcf2531"}