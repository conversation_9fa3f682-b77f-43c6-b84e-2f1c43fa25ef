import {
    APIClient,
    createWebSocketTransport,
    currencyToFixedString,
    GameClient,
    GameError,
    isGameUpdate,
    merge,
    pick,
    RetryError,
    uuid,
    validateObject,
    writable,
    type AnyState,
    type BetAmountLimits,
    type BetPayload,
    type GameActionName,
    type GameState,
    type MessageName,
    type RequestData,
    type TypeSpec,
    type VerifySeedParams,
    type Writable,
} from '@monkey-tilt/client';
import type { Root, SignalReader, SignalUpdater } from '@monkey-tilt/ui';
import { deepMerge, logDevError } from '@monkey-tilt/utils';
import { BetAmountToBetLimit } from '../ui/baccarat';
import {
    ActionsExcludedFromSync,
    BaccaratBetAmounts,
    BaccaratSidebets,
    HandInfo,
    type AutobetState,
    type BaccaratBetAmount,
    type BaccaratBetAmountLimits,
    type BaccaratSidebet,
    type BaccaratState,
    type Card,
    type CardInfo,
    type FinalState,
} from './state';

export interface BaccaratClientOptions {
    readonly root: Root;
    readonly gameId: string;
    readonly gatewayUrl: string | URL;
    readonly websocketUrl?: string | URL;
    readonly gameSessionId: string;
    readonly currency: string;
}

interface BaccaratGameOptions extends BaccaratClientOptions {
    readonly apiClient: APIClient;
    readonly betAmountLimits: BaccaratBetAmountLimits;
}

export async function createBaccaratClient(
    options: BaccaratClientOptions,
): Promise<BaccaratClient> {
    const apiClient = new APIClient(options.gatewayUrl);
    apiClient.gameId = options.gameId;
    apiClient.gameSessionId = options.gameSessionId;

    return new BaccaratGame({
        ...options,
        apiClient,
        betAmountLimits: deepMerge<BetAmountLimits<'dragon_7' | 'panda'>>(
            {
                bet: {
                    min: 0,
                    max: 5000,
                    max_payout: 100_000,
                },
                dragon_7: {
                    min: 0,
                    max: 500,
                    max_payout: 25_000,
                },
                panda: {
                    min: 0,
                    max: 500,
                    max_payout: 25_000,
                },
            },
            await apiClient.getBetLimits(),
        ) as BaccaratBetAmountLimits,
    });
}

export interface BaccaratClient extends GameClient<BaccaratState> {
    readonly state: SignalReader<BaccaratState>;
    onStateReload(handler: () => void): void;
    bet(request: RequestData<'Bet'>): Promise<void>;
    autobet(request: RequestData<'Autobet'>): Promise<void>;
    nextbet(): Promise<void>;
    closeAutobet(): Promise<void>;
    setBetAmounts(amounts: Pick<BaccaratState, BaccaratBetAmount>): void;
    setAutobetState(autobet: Partial<AutobetState & Pick<BaccaratState, BaccaratBetAmount>>): void;
    reset(): void;
    verifySeeds(params: VerifySeedParams): Promise<FinalState>;
}

export const EmptySidebetState = BaccaratSidebets.reduce(
    (acc, bet) => ({ ...acc, [`${bet}_sidebet`]: undefined }),
    {} as Record<`${BaccaratSidebet}_sidebet`, undefined>,
);

class BaccaratGame extends GameClient<BaccaratState> implements BaccaratClient {
    #getState: SignalReader<BaccaratState>;
    #setState: SignalUpdater<BaccaratState>;

    #stateReloadHandlers = new Set<() => void>();

    #updateBalance: () => Promise<void>;
    #updateBetLimits: () => Promise<void>;

    public constructor({
        root,
        gameId,
        apiClient,
        betAmountLimits,
        gatewayUrl,
        websocketUrl,
        gameSessionId,
        currency,
    }: BaccaratGameOptions) {
        super({
            root,
            gameId,
            gameSessionId,
            currency,
            gatewayUrl,
            apiClient,
            transport: createWebSocketTransport({
                gameSessionId,
                gatewayUrl,
                websocketUrl,
            }),
            syncableActions: (action: string) => !ActionsExcludedFromSync.includes(action),
            actionTimeout: 2000,
            autoRetryAttempts: 5,
        });

        const { signal, memo, effect } = this.root.store;

        [this.#getState, this.#setState] = signal<BaccaratState>({
            balance: 0,
            bet_limits: betAmountLimits,
            bet_odds: {
                player: 2,
                banker: 1.98,
                tie: 8,
                dragon7_sidebet: 40,
                panda_sidebet: 25,
            },
            error: null,
            isLoading: true,
            readyState: 'closed',
            isAuthenticated: false,
            round_id: '',
            on_loss_reset: true,
            on_win_reset: true,
            autobet_limit: 0,
            ...this.#initHands(),
            ...EmptySidebetState,
            ...BaccaratBetAmounts.reduce(
                (amounts, bet) => ({ ...amounts, [bet]: '0.00' }),
                {} as Record<BaccaratBetAmount, string>,
            ),
            next_actions: [],
            isAutobetActive: false,
        });

        this.#updateBalance = this.#createUpdater('balance', () =>
            this.api.getBalance().then(({ balance }) => balance),
        );

        const usdRate = memo(() => this.session().usdRate);
        const usdBetLimits = signal(betAmountLimits);

        this.#updateBetLimits = async () => {
            const limits = await this.api.getBetLimits('dragon_7', 'panda');
            usdBetLimits.update(
                (prevLimits) =>
                    deepMerge<BetAmountLimits>(prevLimits, limits) as BaccaratBetAmountLimits,
            );
        };

        effect(() => {
            const limits = usdBetLimits.read();
            const factor = usdRate();
            this.#setState((state) => ({
                ...state,
                bet_limits: {
                    ...(['bet', 'dragon_7', 'panda'] as const).reduce(
                        (acc, bet) => ({
                            ...acc,
                            [bet]: {
                                min: limits[bet].min * factor,
                                max: limits[bet].max * factor,
                                max_payout: limits[bet].max_payout * factor,
                            },
                        }),
                        {} as BaccaratBetAmountLimits,
                    ),
                },
            }));
        });

        const prepareNextRound = () => {
            this.#updateBalance().catch(logDevError);
            this.#updateBetLimits().catch(logDevError);
        };

        prepareNextRound();
        this.on('roundEnd', prepareNextRound);

        this.on('readyStateChanged', (readyState) => {
            this.#setState((state) => ({
                ...state,
                isLoading: !(state.isLoading && readyState === 'ready'),
                readyState,
                isAuthenticated: this.isAuthenticated,
            }));
        });

        this.on('error', (event) => {
            let error: unknown = event.error;

            if (error instanceof RetryError) {
                error = error.reason;
            }

            if (error instanceof GameError) {
                this.#setState((state) => {
                    return {
                        ...state,
                        next_actions:
                            error.code === 'INSUFFICIENT_FUNDS'
                                ? this.roundStartActions
                                : state.next_actions,
                        error,
                    };
                });
            }
        });
    }

    public override close(): void {
        this.root.unmount();
        super.close();
    }

    protected updateState(
        value: BaccaratState | ((prevValue: BaccaratState) => BaccaratState),
    ): void {
        return this.#setState(value);
    }

    public get state(): SignalReader<BaccaratState> {
        return this.#getState;
    }

    public onStateReload(handler: () => void): void {
        this.#stateReloadHandlers.add(handler);
    }

    public override can<A extends MessageName>(action: A): boolean {
        if (super.can(action)) {
            return true;
        }
        return (
            this.roundStartActions.includes(action as GameActionName) &&
            this.#getState().next_actions.length === 0
        );
    }

    async #ensureAuthenticated(): Promise<void> {
        if (!this.isAuthenticated) {
            await this.authenticate();
        }
    }

    #validateBetAmounts(request: BetPayload): BetPayload & { readonly balance: number } {
        const { balance, bet_limits } = this.#getState();

        const result: Writable<BetPayload & { balance: number }> = {
            ...request,
            balance,
        };

        let totalBetAmount = 0;

        for (const bet of BaccaratBetAmounts) {
            const amount = request[bet] ?? 0;
            if (amount > 0) {
                const { min, max } = bet_limits[BetAmountToBetLimit[bet]];
                if (amount < min || amount > max) {
                    throw new GameError(
                        `Bet amount for ${bet} is out of range`,
                        'BET_AMOUNT_OUT_OF_RANGE',
                        '',
                    );
                }
                totalBetAmount += amount;
                result[bet] = amount;
            }
        }

        if (totalBetAmount > result.balance) {
            throw new GameError('Insufficient funds', 'INSUFFICIENT_FUNDS', '');
        }

        return result;
    }

    public async bet(request: RequestData<'Bet'>): Promise<void> {
        await this.#ensureAuthenticated();

        if (this.can('Bet')) {
            const { balance, ...bets } = this.#validateBetAmounts(request);
            const { currency } = this.session();

            Object.assign(request, bets);
            this.#setState((state) => ({
                ...state,
                ...this.#initHands(),
                ...BaccaratBetAmounts.reduce(
                    (acc, bet) => ({
                        ...acc,
                        [bet]: currencyToFixedString(bets[bet] ?? 0, currency),
                    }),
                    {},
                ),
                balance,
            }));
        }

        return this.#send(
            'Bet',
            request,
            (error) => !(error instanceof GameError) || error.code !== 'INSUFFICIENT_FUNDS',
        );
    }

    public async autobet(request: RequestData<'Autobet'>): Promise<void> {
        await this.#ensureAuthenticated();

        if (this.can('Autobet')) {
            this.removeIntent('CloseAutobet');

            const { balance, ...bets } = this.#validateBetAmounts(request);
            const { currency } = this.session();

            Object.assign(request, bets);
            this.#setState((state) => ({
                ...state,
                ...this.#initHands(),
                ...BaccaratBetAmounts.reduce(
                    (acc, bet) => ({
                        ...acc,
                        [bet]: currencyToFixedString(bets[bet] ?? 0, currency),
                    }),
                    {},
                ),
                balance,
                isAutobetActive: true,
                autobet_limit:
                    request.autobet_limit != undefined && Number.isFinite(request.autobet_limit)
                        ? request.autobet_limit
                        : 0,
                autobet_count: 0,
                on_win_reset: request.on_win_reset ?? true,
                on_win: (request.on_win ?? 0).toFixed(0),
                on_loss_reset: request.on_loss_reset ?? true,
                on_loss: (request.on_loss ?? 0).toFixed(0),
                stop_on_profit: currencyToFixedString(request.stop_on_profit ?? 0, currency),
                stop_on_loss: currencyToFixedString(request.stop_on_loss ?? 0, currency),
            }));
        }

        return this.#send(
            'Autobet',
            request,
            (error) => !(error instanceof GameError) || error.code !== 'INSUFFICIENT_FUNDS',
        ).catch(() => {
            this.#setState((state) => ({ ...state, isAutobetActive: false }));
        });
    }

    public nextbet(): Promise<void> {
        if (this.hasIntent('CloseAutobet')) {
            return this.closeAutobet();
        }
        if (this.can('Nextbet')) {
            const state = this.#getState();
            const { currency } = this.session();
            const { balance, ...bets } = this.#validateBetAmounts(
                BaccaratBetAmounts.reduce(
                    (bets, bet) => ({ ...bets, [bet]: Number.parseFloat(state[bet] ?? '0.00') }),
                    {} as Writable<BetPayload>,
                ),
            );
            this.#setState((state) => ({
                ...state,
                ...this.#initHands(),
                ...BaccaratBetAmounts.reduce(
                    (acc, bet) => ({
                        ...acc,
                        [bet]: currencyToFixedString(bets[bet] ?? 0, currency),
                    }),
                    {},
                ),
                balance,
            }));
        }
        return this.#send('Nextbet');
    }

    public closeAutobet(): Promise<void> {
        this.removeIntent('CloseAutobet');
        return this.#send('CloseAutobet');
    }

    public setBetAmounts(amounts: Pick<BaccaratState, BaccaratBetAmount>): void {
        this.#setState((state) => ({
            ...state,
            ...amounts,
        }));
    }

    public setAutobetState(
        autobet: Partial<AutobetState & Pick<BaccaratState, BaccaratBetAmount>>,
    ): void {
        this.#setState((state) => ({
            ...state,
            ...autobet,
        }));
    }

    public override async reload(): Promise<GameState> {
        this.#setState((state) => ({ ...state, error: null }));
        return super.reload();
    }

    public override get roundStartActions(): GameActionName[] {
        return ['Bet', 'Autobet'];
    }

    public reset(): void {
        super.reset();

        const { isAuthenticated } = this;

        this.#setState((state) => ({
            ...state,
            error: null,
            next_actions: isAuthenticated ? this.roundStartActions : [],
            isAuthenticated,
        }));
    }

    async #send(
        action: GameActionName,
        request: RequestData<GameActionName>,
        shouldRetry?: (error: Error) => boolean,
    ): Promise<void> {
        this.#setState((state) => ({
            ...state,
            error: null,
            next_actions: [],
        }));
        await this.sendAndAwait(action, request, shouldRetry);
    }

    protected override handleUpdate(state: AnyState): void {
        if (state.type === 'Authenticate') {
            this.reset();
            return;
        }

        if (state.type === 'GameState') {
            const resetState = state.data as Writable<BaccaratState>;

            if (state.round_id) {
                resetState.round_id = state.round_id;
            }

            for (const bet of BaccaratBetAmounts) {
                if (resetState[bet] === undefined) {
                    resetState[bet] = '0.00';
                } else {
                    resetState[bet] = Number.parseFloat(resetState[bet]).toFixed(2);
                }
            }

            this.allowedActions = new Set(resetState.next_actions);

            this.#setState((currentState) => ({
                ...currentState,
                ...EmptySidebetState,
                ...this.#updateHands(resetState),
                round_id: state.round_id ?? currentState.round_id,
                round_closed: resetState.round_closed == true,
                isAutobetActive: resetState.next_actions.includes('CloseAutobet'),
                isAuthenticated: true,
            }));

            if (resetState.round_closed) {
                this.signalRoundEnd();
            }

            void Promise.resolve().then(() => {
                for (const handler of this.#stateReloadHandlers) {
                    try {
                        handler();
                    } catch {
                        // ignore
                    }
                }
            });

            return;
        }

        if (!isGameUpdate(state)) {
            return;
        }

        const newState = structuredClone(this.#getState()) as Writable<BaccaratState>;
        let isNewRound = false;

        if (state.round_id) {
            isNewRound = state.round_id !== newState.round_id;
            newState.round_id = state.round_id;
        }

        newState.action = state.action;
        newState.isAuthenticated = this.isAuthenticated;
        newState.next_actions = state.data.next_actions;
        this.allowedActions = new Set(newState.next_actions);

        if (isNewRound) {
            this.#setState({
                ...newState,
                ...this.#initHands(),
                ...EmptySidebetState,
                round_closed: false,
            });
            // N.B. due to removal of Open step, we have to split state update
            // into two parts to allow UI to properly clean up the previous round
            void Promise.resolve().then(() => {
                this.handleUpdate(state);
            });
            return;
        }

        merge(
            newState,
            pick(
                state.data,
                ...BaccaratSidebets,
                ...BaccaratBetAmounts,
                'banker_hand',
                'player_hand',
                'autobet_count',
                'round_closed',
                'total_payout',
            ),
        );

        if (
            (state.action === 'Autobet' || state.action === 'Nextbet') &&
            (newState.autobet_limit ?? 0) > 0
        ) {
            (newState.autobet_limit as number)--;
        }

        this.#updateHands(newState);

        if (state.data.round_closed) {
            const total_payout = Number.parseFloat(state.data.total_payout ?? '0.00');

            newState.isAutobetActive = newState.next_actions.includes('CloseAutobet');
            newState.balance += total_payout;
        }

        if (newState.round_closed || newState.isAutobetActive) {
            this.signalRoundEnd();
        }

        this.#setState(newState);
    }

    #initHands() {
        return {
            banker_hand: {
                id: uuid(),
                cards: [],
                hand_value: 0,
                status: 'dealing',
            },
            player_hand: {
                id: uuid(),
                cards: [],
                hand_value: 0,
                status: 'dealing',
            },
        } as const satisfies Partial<BaccaratState>;
    }

    #updateHands<T extends Partial<BaccaratState>>(state: T): T {
        for (const hand of ['banker_hand', 'player_hand'] as const) {
            if (!state[hand]) {
                continue;
            }
            if (state[hand].cards) {
                const cards: Card[] = [];
                for (const card of state[hand].cards) {
                    if (card) {
                        if (!card.id) {
                            writable(card).id = uuid();
                        }
                        cards.push(card);
                    }
                }
                writable(state[hand]).cards = cards;
            } else {
                writable(state[hand]).cards = [];
            }
        }

        if (state.player_hand && state.banker_hand) {
            if (state.player_hand.hand_value > state.banker_hand.hand_value) {
                writable(state.player_hand).status = 'win';
                writable(state.banker_hand).status = 'lose';
            } else if (state.player_hand.hand_value < state.banker_hand.hand_value) {
                writable(state.player_hand).status = 'lose';
                writable(state.banker_hand).status = 'win';
            } else {
                writable(state.player_hand).status = 'tie';
                writable(state.banker_hand).status = 'tie';
            }
        }

        return state;
    }

    #createUpdater<T extends keyof BaccaratState>(
        key: T,
        updater: () => Promise<
            BaccaratState[T] | ((previousValue: BaccaratState[T]) => BaccaratState[T])
        >,
    ): () => Promise<void> {
        let isFetching = false;
        return async () => {
            if (isFetching) {
                return;
            }

            isFetching = true;
            try {
                const value = await updater();
                this.#setState((state) => ({
                    ...state,
                    [key]: typeof value === 'function' ? value(state[key]) : value,
                }));
            } catch (error) {
                logDevError(`Failed to fetch ${key}:`, error);
            } finally {
                isFetching = false;
            }
        };
    }

    protected validateSeedVerificationData(data: unknown): boolean {
        if (
            !validateObject<FinalState>(data, {
                banker_hand: 'object',
                player_hand: 'object',
            })
        ) {
            return false;
        }

        const handSchema: TypeSpec<HandInfo> = {
            cards: 'array',
            hand_value: 'number',
        };

        const cardSchema: TypeSpec<CardInfo> = {
            value: 'number',
            suit: 'number',
            index: 'number',
        };

        for (const hand of ['banker_hand', 'player_hand'] as const) {
            if (
                !validateObject<HandInfo>(data[hand], handSchema) ||
                !data[hand].cards.every((card) => validateObject<CardInfo>(card, cardSchema))
            ) {
                return false;
            }
        }
        return true;
    }

    public async verifySeeds(params: VerifySeedParams): Promise<FinalState> {
        const state = (await this._validateSeeds(params)) as FinalState;
        return this.#updateHands(state);
    }
}
