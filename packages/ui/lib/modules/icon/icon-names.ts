import type { IconName } from './icon';

const iconNames: <PERSON><PERSON><PERSON><PERSON><PERSON>y<IconName> = [
    'arrow-down',
    'arrow-left',
    'arrow-right',
    'arrow-up',
    'ask',
    'bolt',
    'cards',
    'chevron-l',
    'chevron-r',
    'chevron',
    'chips',
    'circle-dollar',
    'clear',
    'clockwise',
    'close',
    'coins',
    'copy',
    'double-check',
    'error',
    'flag',
    'hand',
    'infinity',
    'info',
    'keyboard',
    'lock',
    'multiply',
    'music',
    'notepad',
    'plus',
    'raise',
    'risk',
    'rows',
    'sound',
    'stop',
    'switch',
    'undo',
    'x',
];

export default iconNames;
