import {
    create,
    defineComponent,
    forwardRef,
    html,
    type Props,
    type RefObject,
    type Renderable,
} from '../../component';
import type { Size } from '../../layouts';
import { mergeClasses, withNamespace } from '../../utilities/classes';
import { Frame } from '../frame/frame';
import { Icon } from '../icon/icon';
import { Label } from '../label/label';

export interface ModalControls {
    close(this: void): void;
    open(this: void): void;
    isOpen(this: void): boolean;
}

export interface ModalProps extends Omit<Props<HTMLDialogElement>, 'title'> {
    readonly title?: Renderable;
    readonly controls?: (controls: ModalControls) => void;
    readonly onOpen?: () => void;
    readonly onClose?: () => void;
    readonly gap?: Size;
    readonly wide?: boolean;
}

const isOpenClassName = withNamespace('is-open');

export const Modal = defineComponent(
    'Modal',
    () =>
        ({
            title,
            controls,
            onOpen,
            onClose,
            children = [],
            gap,
            wide = false,
            ...props
        }: ModalProps): HTMLDialogElement => {
            const ref: RefObject<HTMLDialogElement> = { current: null };

            function close(this: void): void {
                if (ref.current) {
                    ref.current.close();
                    ref.current.classList.remove(isOpenClassName);
                    if (onClose) {
                        onClose();
                    }
                }
            }

            if (controls) {
                controls({
                    close,
                    open: () => {
                        if (ref.current) {
                            ref.current.showModal();
                            ref.current.classList.add(isOpenClassName);
                            if (onOpen) {
                                onOpen();
                            }
                        }
                    },
                    isOpen: () => {
                        return ref.current?.classList.contains(isOpenClassName) ?? false;
                    },
                });
            }

            return create(
                'dialog',
                {
                    props: {
                        ...props,
                        ref: forwardRef(props.ref, ref),
                        className: mergeClasses(props.className, {
                            'm-modal': true,
                            'm-modal--wide': wide,
                            [isOpenClassName]: props.open ?? false,
                        }),
                    },
                },
                Frame(
                    { gap },
                    html('div')(
                        { className: { 'l-cluster': true } },
                        title && Label({ variant: 'big' }, title),
                        html('button')(
                            {
                                className: { 'm-modal__close': true, 'l-cluster__split': true },
                                type: 'button',
                                ariaLabel: 'Close',
                                onClick: close,
                            },
                            Icon({ name: 'close' }),
                        ),
                    ),
                    ...children,
                ),
            );
        },
);
