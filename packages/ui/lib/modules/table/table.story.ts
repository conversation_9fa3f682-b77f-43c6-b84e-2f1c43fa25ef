import type { Meta, StoryObj } from '@storybook/html';
import { Table } from './table';
import { html } from '../../component';

const meta: Meta = {
    title: 'Modules/Table',
};
export default meta;

export const Default: StoryObj = {
    render: () =>
        html('div')(
            {},

            Table({
                rows: [
                    { label: 'Bet', value: '0.20000000' },
                    { label: 'Multiplier', value: '100,000,000x' },
                    { label: 'Payout', value: '0.20000000' },
                ],
                className: 'bet-summary-table',
            }),
        ).render(),
};

export const Empty: StoryObj = {
    render: () =>
        html('div')({}, Table({ rows: [], className: 'empty-table' })).render(),
};
