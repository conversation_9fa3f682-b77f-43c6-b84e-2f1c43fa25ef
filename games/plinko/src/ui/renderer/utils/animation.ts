import { logDevError } from '@monkey-tilt/utils';
import type { DynamicRenderable } from '../types';
import { linear, type EasingFunction } from './easing';
import { lerp } from './lerp';

export type KeyframeData = [offset: number, value: number];
export type Keyframes = [
    start: number | KeyframeData,
    ...KeyframeData[],
    end: number | KeyframeData,
];

export type PropKeyframes<T extends string> = Record<T, Keyframes>;

export interface KeyframeAnimationOptions<T extends string> {
    /**
     * The duration of the animation in seconds.
     */
    readonly duration: number;

    /**
     * The properties to animate.
     *
     * Each prop is defined as an array of (at least two) keyframes.
     */
    readonly props: PropKeyframes<T>;

    /**
     * The optional easing function to use for the animation. Defaults to a linear easing function.
     */
    readonly easing?: EasingFunction | undefined;
}

export abstract class KeyframeAnimation<T extends string> implements DynamicRenderable {
    readonly #duration: number;
    readonly #easing: EasingFunction;
    readonly #keyframes: [prop: string, keyframes: KeyframeData[]][];
    readonly #propValues: Record<string, number>;

    #elapsedTime = 0;

    public constructor({ duration, props, easing = linear }: KeyframeAnimationOptions<T>) {
        this.#duration = duration;
        this.#easing = easing;

        this.#keyframes = [];
        this.#propValues = {};

        for (const [key, value] of Object.entries(props) as [string, Keyframes][]) {
            const propKeyframes: [string, KeyframeData[]] = [
                key,
                value.map((data, i) => (Array.isArray(data) ? data : [i === 0 ? 0 : 1, data])),
            ];

            if (propKeyframes[1].length < 2) {
                logDevError(`Invalid keyframes for prop "${key}"`, value);
            }

            this.#keyframes.push(propKeyframes);
            this.#propValues[key] = propKeyframes[1][0]![1];
        }
    }

    public get progress(): number {
        return this.#elapsedTime / this.#duration;
    }

    protected get props(): Record<T, number> {
        return this.#propValues as Record<T, number>;
    }

    public update(dt: number): void {
        this.#elapsedTime += dt;

        if (this.#elapsedTime > this.#duration) {
            this.#elapsedTime = this.#duration;
        }

        const t = this.#easing(this.progress);

        for (const [prop, keyframes] of this.#keyframes) {
            const startIndex = keyframes.findLastIndex(([offset]) => offset < t);

            if (startIndex === -1) {
                continue;
            }

            const [offsetA, valueA] = keyframes[startIndex]!;

            if (startIndex === keyframes.length - 1) {
                this.#propValues[prop] = valueA;
                continue;
            }

            const [offsetB, valueB] = keyframes[startIndex + 1]!;
            const span = offsetB - offsetA;

            this.#propValues[prop] = span > 0 ? lerp(valueA, valueB, (t - offsetA) / span) : valueA;
        }
    }

    public abstract render(context: CanvasRenderingContext2D, alpha: number): void;
}
