@use './util';

$-font-weights: (
    thin: 100,
    hairline: 100,
    extra-light: 200,
    ultra-light: 200,
    light: 300,
    regular: 400,
    normal: 400,
    medium: 500,
    semi-bold: 600,
    demi-bold: 600,
    bold: 700,
    extra-bold: 800,
    ultra-bold: 800,
    black: 900,
    heavy: 900,
);

///
/// Get the font weight value of a common font weight name.
///
@function get($weight) {
    @return util.get($-font-weights, $weight, $weight);
}

///
/// Set the font weight.
///
@mixin set($weight) {
    font-weight: get($weight);
}
