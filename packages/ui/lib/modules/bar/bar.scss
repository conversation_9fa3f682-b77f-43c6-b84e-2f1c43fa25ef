@use '../../styles/common/bem';
@use '../../styles/common/prop';
@use '../../styles/common/respond';
@use '../../styles/common/type-style' as *;
@use '../../styles/common/util';
@use '@monkey-tilt/ui/scss/config/metrics';

$-border-radius: util.to-rem(12px);
$-tooltip-border: util.to-rem(1px) solid prop.get(color-zinc-700);
$-tooltip-padding: util.to-rem(20px) util.to-rem(12px) util.to-rem(13px) util.to-rem(12px);
$-tooltip-radius: util.to-rem(18px);
$-tooltip-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
$-transition-duration: 0.3s;

@mixin styles {
    @include bem.module('bar') {
        display: flex;
        flex-wrap: nowrap;
        justify-content: space-between;
        border-radius: $-border-radius;
        background: prop.get(color-zinc-900);
        position: relative;

        @include respond.until(l) {
            border-radius: util.to-rem(6px);
        }

        &__empty {
            flex-grow: 1;
            text-align: center;
            padding: util.to-rem(24px);
            color: prop.get(color-white);
            font-size: clamp(0.875rem, 2.5vw, 1.125rem);
            font-weight: 500;
        }

        &__item {
            display: flex;
            flex-grow: 1;
            flex-shrink: 1;
            flex-basis: 0;
            min-inline-size: 0;

            align-items: center;
            justify-content: center;
            gap: util.to-rem(2px);
            padding: util.to-rem(12px) util.to-rem(2px);
            transition: background $-transition-duration;

            &:first-of-type {
                border-top-left-radius: $-border-radius;
                border-bottom-left-radius: $-border-radius;
            }

            &:last-of-type {
                border-top-right-radius: $-border-radius;
                border-bottom-right-radius: $-border-radius;
            }

            &:hover {
                background: prop.get(color-zinc-800);
                cursor: default;

                .-MT-m-bar__tooltip {
                    display: flex;
                }
                .-MT-m-bar__caret::before {
                    display: block;
                }
                .-MT-m-bar__caret::after {
                    display: block;
                }
            }
        }

        &__caret {
            position: relative;

            &::before {
                display: none;
                content: '';
                position: absolute;
                inset-block-end: util.to-rem(16px);
                border: solid transparent;
                border-width: util.to-rem(10px);
                border-top-color: prop.get(color-zinc-900);
                z-index: 1000;
            }

            &::after {
                display: none;
                content: '';
                position: absolute;
                inset-block-end: util.to-rem(15px);
                border: solid transparent;
                border-width: util.to-rem(10px);
                border-top-color: prop.get(color-zinc-600);
                z-index: 999;
            }
        }

        &__number {
            color: prop.get(color-white);
        }

        &__icon {
            svg {
                @include respond.until(l) {
                    inline-size: util.to-rem(12px);
                    block-size: util.to-rem(12px);
                }
            }
        }

        &__tooltip {
            display: none;
            pointer-events: none;

            position: absolute;
            z-index: 999;
            bottom: calc(100% + util.to-rem(9px));
            inset-inline-start: 0;
            inset-inline-end: 0;
            margin-inline: auto;
            inline-size: 100%;

            color: prop.get(color-zinc-300);
            text-align: center;
            padding: $-tooltip-padding;
            border: $-tooltip-border;
            border-radius: $-tooltip-radius;
            background-color: prop.get(color-zinc-900);
            box-shadow: $-tooltip-shadow;

            gap: 1rem;
            align-items: center;
            justify-content: space-around;

            transition: opacity $-transition-duration;

            @include type-style('body', $with: (font-weight: normal, line-height: 1.2));

            &-item {
                @include type-style('title', $with: (font-weight: 500));
                display: flex;
                justify-content: space-between;
                flex-direction: column;
                align-items: flex-start;
                gap: util.to-rem(10px);
                inline-size: 100%;
            }

            &-input {
                &#{bem.namespaced-selector(m-input)} {
                    background: prop.get(color-zinc-800);
                    border-color: prop.get(color-zinc-700);
                    font-size: metrics.fluid-size(10, 18);
                }
            }
        }
    }
}
