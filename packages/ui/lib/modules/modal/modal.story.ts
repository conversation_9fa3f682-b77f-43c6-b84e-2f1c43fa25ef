import type { <PERSON>a, StoryObj } from '@storybook/html';
import { fn } from '@storybook/test';
import { html } from '../../component';
import { Button } from '../button/button';
import { Frame } from '../frame/frame';
import { Label } from '../label/label';
import { Toggle } from '../toggle/toggle';
import { Modal, type ModalProps } from './modal';
import { Icon } from '../icon/icon';
import { Input } from '../input/input';
import { namespaced } from '../../utilities/classes';

const meta: Meta = {
    title: 'Modules/Modal',
};
export default meta;

//Default Modal — Hotkeys
export const Default: StoryObj = (props: ModalProps) => {
    let open: () => void;
    return html('div')(
        { className: { 'l-box': true, 'u-gap--xl': true } },
        Modal(
            {
                ...props,
                title: 'Hotkeys',
                controls: ({ open: openModal }) => {
                    open = openModal;
                },
            },
            html('dl')(
                { role: 'list' },
                ...[
                    { key: 'Space', description: 'Place bet' },
                    { key: 'W', description: 'Double bet amount' },
                    { key: 'S', description: 'Halve bet amount' },
                    { key: 'A', description: 'Split' },
                    { key: 'D', description: 'Double' },
                    { key: 'Q', description: 'Hit' },
                    { key: 'E', description: 'Stand' },
                    { key: 'Z', description: 'Accept insurance' },
                    { key: 'X', description: 'Decline insurance' },
                ].map(({ key, description }) =>
                    html('div')(
                        null,
                        html('dt')(null, description),
                        html('dd')(null, html('kbd')(null, key)),
                    ),
                ),
            ),
            Frame(
                { variant: 'medium' },
                html('div')(
                    { className: { 'l-cluster': true } },
                    Label({ variant: 'light', htmlFor: 'hotkeys' }, 'Enable Hotkeys'),
                    Toggle({
                        id: 'hotkeys',
                        autofocus: true,
                        className: { 'l-cluster__split': true },
                    }),
                ),
                Label(
                    { variant: 'subtle', className: { 'l-cluster': false } },
                    Icon({
                        name: 'info',
                        description: 'Hint',
                        css: { 'align-self': 'flex-start' },
                    }),
                    html('span')(
                        null,
                        'When the hotkeys are enabled, they will remain on for all games until disabled. ' +
                            "Despite some games sharing similar key binds, it's always advised to confirm " +
                            'what key interactions are set for each game.',
                    ),
                ),
            ),
        ),
        Button({
            label: 'Show Modal',
            onClick: () => {
                open();
            },
        }),
    ).render();
};
Default.args = {
    onOpen: fn(),
    onClose: fn(),
};

//Dice Modal — Max Profits
export const Dice_MaxProfits: StoryObj = () => {
    let open: () => void;

    return html('div')(
        { className: namespaced('l-box', 'u-gap--xl') },

        Modal(
            {
                title: 'Max Profits',
                controls: ({ open: openModal }) => {
                    open = openModal;
                },
            },

            html('div')({
                className: namespaced('m-modal__note'),
                textContent:
                    'When your betting profit exceeds the maximum profit, the system will automatically settle to the maximum profit.',
            }),

            html('p')(null, 'Different currencies have different max profits as follows:'),

            html('table')({
                className: namespaced('m-modal__table'),
                children: [
                    html('thead')(
                        null,
                        html('tr')(
                            null,
                            html('th')(null, 'Currency'),
                            html('th')(null, 'Max Bet'),
                            html('th')(null, 'Max Profit'),
                        ),
                    ),
                    html('tbody')(
                        null,
                        [
                            ['MXN', '2000', '200000'],
                            ['GTQ', '775', '200000'],
                            ['CLP', '93000', '9300000'],
                        ].map(([currency, bet, profit]) =>
                            html('tr')(
                                null,
                                html('td')(null, currency),
                                html('td')(null, bet),
                                html('td')(null, profit),
                            ),
                        ),
                    ),
                ],
            }),
        ),

        Button({
            label: 'Show Max Profits',
            onClick: () => open(),
        }),
    ).render();
};
Dice_MaxProfits.storyName = 'Dice – Max Profits';

// Dice Modal — Fairness
export const Dice_Fairness: StoryObj = () => {
    let open: () => void;

    return html('div')(
        { className: namespaced('l-box', 'u-gap--xl') },

        Modal(
            {
                title: 'Fairness',
                controls: ({ open: openModal }) => {
                    open = openModal;
                },
            },

            html('div')({
                className: namespaced('m-modal__note'),
                textContent:
                    'A provably fair system based on the SHA256 algorithm and includes a random number = a combination of a random winning number and the initial number of the server. Each round has its own hash, consisting of random wins that are uniquely generated for each bet.',
            }),

            Label({ variant: 'medium', text: 'Hash round' }),

            Input({
                value: '6fa2ff6474812773330cbf57c34836da6cdb29b0df92cc8',
                readOnly: true,
                css: { 'font-weight': 'normal' },
                action: Button({
                    className: { 'm-stepper': true },
                    children: [Icon({ name: 'copy', description: 'Copy to clipboard' })],
                    onClick: () =>
                        navigator.clipboard.writeText(
                            '6fa2ff6474812773330cbf57c34836da6cdb29b0df92cc8',
                        ),
                }),
            }),

            Label({ htmlFor: 'history-select', variant: 'light' }, 'Previous rounds history'),

            html('select')({
                id: 'history-select',
                className: namespaced('m-modal__input'),
                children: [
                    html('option')({ value: 'plinko', textContent: 'Plinko' }),
                    html('option')({ value: 'dice', textContent: 'Dice' }),
                    html('option')({ value: 'limbo', textContent: 'Limbo' }),
                    html('option')({ value: 'mines', textContent: 'Mines' }),
                ],
            }),

            html('div')({
                textContent: 'No Information',
                className: namespaced('u-text--center'),
                css: { padding: '40px' },
            }),

            html('div')({
                className: namespaced('l-cluster', 'u-gap--xl'),
                children: [
                    Button({
                        label: 'Check',
                        variant: 'primary',
                        css: { 'justify-content': 'center' },
                    }),
                    Button({
                        label: 'How it works',
                        variant: 'accent',
                    }),
                ],
            }),
        ),

        Button({
            label: 'Show Fairness Modal',
            onClick: () => open(),
        }),
    ).render();
};
Dice_Fairness.storyName = 'Dice – Fairness';

// Dice Modal — How to Play
export const Dice_HowToPlay: StoryObj = () => {
    let open: () => void;

    return html('div')(
        { className: namespaced('l-box', 'u-gap--xl') },

        Modal(
            {
                title: 'How to play',
                controls: ({ open: openModal }) => {
                    open = openModal;
                },
            },
            html('p')({
                textContent:
                    'Plinko at MiniGame+ is the best in today’s crypto gambling scene, offering a spin on the quirky and fun game of chance that involves players dropping balls onto a vertical board that is filled with rows of pegs that form a straight pyramid on the surface. Originally, the game is inspired by “Pachinko,” a Japanese mechanical game. Mechanics are almost the same in that the players drop a ball from the very top of the pyramid so that they can discover the winning routes that provide a respective multiplier.',
                className: namespaced('m-modal__description'),
            }),
        ),

        Button({
            label: 'Show How to Play',
            onClick: () => open(),
        }),
    ).render();
};
Dice_HowToPlay.storyName = 'Dice – How to Play';

// Dice Modal — How It Works
export const Dice_HowItWorks: StoryObj = () => {
    let open: () => void;

    return html('div')(
        { className: namespaced('l-box', 'u-gap--xl') },

        Modal(
            {
                title: 'How does it work',
                controls: ({ open: openModal }) => {
                    open = openModal;
                },
            },

            html('div')({
                className: namespaced('m-modal__note'),
                textContent:
                    'The provably fair principle is based on SHA256 technology the same as Bitcoin. The procedure of checking bets result allows you to verify the fairness of each bet.',
            }),

            html('ol')({
                className: namespaced('m-modal__steps'),
                children: [
                    {
                        title: 'PRESS FAIRNESS',
                        description:
                            'Before placing a bet you may check the hash of the next round number in the "Fairness" pop-up. Hash is a random result, generated and mixed with random Server seed. Every round has its own hash. No-one knows the result before placing a bet.',
                    },
                    {
                        title: 'PLACE A BET',
                        description:
                            'During the bet, the result is fixed in a special "Random Seed" field. By clicking "Fairness" you can find the current round hash (encrypted winning result) and history of completed bets with all the details.',
                    },
                    {
                        title: 'CHECK HASH',
                        description:
                            'Click "Check". Then in the opened window paste Random Seed you\'ve copied. The issued hash below must match that fixed on BetFury.',
                    },
                ].map(({ title, description }, index) =>
                    html('li')(
                        { className: namespaced('m-modal__step') },
                        html('span')({
                            className: namespaced('m-modal__step-number'),
                            textContent: String(index + 1).padStart(2, '0'),
                        }),
                        html('div')(
                            null,
                            html('strong')({ textContent: title }),
                            html('p')({
                                textContent: description,
                                className: namespaced('m-modal__description'),
                            }),
                        ),
                    ),
                ),
            }),
        ),

        Button({
            label: 'Show How It Works',
            onClick: () => open(),
        }),
    ).render();
};

Dice_HowItWorks.storyName = 'Dice – How It Works';

// Dice Modal — Game Limits
export const Dice_GameLimits: StoryObj = () => {
    let open: () => void;

    return html('div')(
        { className: namespaced('l-box', 'u-gap--xl') },

        Modal(
            {
                title: 'Game Limits',
                controls: ({ open: openModal }) => {
                    open = openModal;
                },
            },

            html('div')({
                className: namespaced('m-modal__limits'),
                children: [
                    Label.Group({
                        text: Label({ variant: 'light' }, 'Min Bet (USDT)'),
                        value: Label({ variant: 'highlight' }, '0.1'),
                    }),
                    Label.Group({
                        text: Label({ variant: 'light' }, 'Max Bet (USDT)'),
                        value: Label({ variant: 'highlight' }, '100.00'),
                    }),
                    Label.Group({
                        text: Label({ variant: 'light' }, 'Max win for one bet (USDT)'),
                        value: Label({ variant: 'highlight' }, '10,000.00'),
                    }),
                ],
            }),
        ),

        Button({
            label: 'Show Game Limits',
            onClick: () => open(),
        }),
    ).render();
};
Dice_GameLimits.storyName = 'Dice – Game Limits';

// Dice – Bet History
export const Dice_BetHistory: StoryObj = () => {
    let open: () => void;

    return html('div')(
        { className: namespaced('l-box', 'u-gap--xl') },

        Modal(
            {
                title: 'My bet history',
                controls: ({ open: openModal }) => {
                    open = openModal;
                },
            },

            html('div')({
                className: namespaced('m-modal__history-header'),
                children: [
                    Label({ variant: 'subtle' }, 'Date'),
                    Label({ variant: 'subtle' }, 'Bet'),
                    Label({ variant: 'subtle' }, 'Payout'),
                    Label({ variant: 'subtle' }, 'Cashout'),
                ],
            }),
        ),

        Button({
            label: 'Show Bet History',
            onClick: () => open(),
        }),
    ).render();
};
Dice_BetHistory.storyName = 'Dice – Bet History';
