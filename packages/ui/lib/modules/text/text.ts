import {
    defineComponent,
    html,
    type Component,
    type Props,
    type Renderable,
} from '../../component';

import { mergeClasses } from '../../utilities';

export const TypeStyles = [
    'body',
    'body2',
    'title',
    'alert',
    'alert-xl',
    'heading',
    'c1',
    'c2',
    'c3',
    'c4',
    'caption',
    'cta',
    'caps',
    'multiplier',
] as const;

export type TypeStyle = (typeof TypeStyles)[number];

export type TextProps<T extends keyof HTMLElementTagNameMap = 'span'> = Omit<
    Props<HTMLElementTagNameMap[T]>,
    'style'
> & {
    readonly as?: T;
    readonly style: TypeStyle;
    readonly suffix?: string;
};

export const Text = defineComponent(
    'Text',
    () =>
        <T extends keyof HTMLElementTagNameMap = 'span'>({
            as: tagName = 'span' as T,
            style,
            suffix,
            ...props
        }: TextProps<T>): Component<HTMLElementTagNameMap[T]> => {
            return html(tagName)({
                ...(props as unknown as Props<HTMLElementTagNameMap[T]>),
                'data-suffix': suffix,
                className: mergeClasses(props.className, {
                    [`u-text--${style}`]: true,
                    'u-text--suffix': suffix !== undefined,
                }),
            });
        },
) as <T extends keyof HTMLElementTagNameMap = 'span'>(
    props: TextProps<T>,
    ...children: Renderable[]
) => Component<HTMLElementTagNameMap[T]>;
