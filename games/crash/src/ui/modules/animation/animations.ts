import * as <PERSON><PERSON><PERSON> from 'pixi.js';
import { 
    ENABLE_WIN_ANIMATION, 
    ENABLE_CRASH_ANIMATION, 
    WIN_ANIMATION_DURATION, 
    CRASH_ANIMATION_DURATION,
    ANIMATION_CONFIG,
    TIMING_CONFIG,
    PROP<PERSON>LER_INTERVAL,
    TARGET_MONKEY_WIDTH
} from './constants';
import { type PositionData } from './positioning';

export interface AnimationContext {
    monkeyHeadContainer: PIXI.Container | null;
    monkeyHeadSvg2Url?: string | undefined;
    originalMonkeySvgUrl?: string | undefined;
    isRunning: boolean;
    hasCrashed: boolean;
    data: Array<PositionData>;
    loadSvgAsHiResTexture: (svgUrl: string) => Promise<PIXI.Texture>;
    switchMonkeyTexture: (svgUrl: string) => Promise<void>;
}

export class AnimationHelper {
    private winAnimationRunning: boolean = false;
    private propellerAnimationInterval: ReturnType<typeof setInterval> | null = null;

    constructor(private context: AnimationContext) {}

    public updateContext(updates: Partial<AnimationContext>): void {
        this.context = { ...this.context, ...updates };
    }

    public startWinAnimation(): void {
        if (!ENABLE_WIN_ANIMATION) {
            console.log('Win animation disabled by flag');
            return;
        }

        console.log('startWinAnimation called');
        
        // Set the flag to indicate win animation is running
        this.winAnimationRunning = true;
        
        if (!this.context.monkeyHeadContainer) {
            console.log('No monkey head container - waiting for it to be created...');
            // Wait a bit for the SVG to load and container to be created
            setTimeout(() => {
                if (this.context.monkeyHeadContainer && this.winAnimationRunning) {
                    console.log('Monkey head container found after waiting, starting animation');
                    this.startWinAnimation();
                } else {
                    console.log('Still no monkey head container after waiting or animation was cancelled');
                    this.winAnimationRunning = false;
                }
            }, TIMING_CONFIG.SVG_LOAD_TIMEOUT);
            return;
        }
        console.log('Starting win animation with container:', this.context.monkeyHeadContainer);
        
        // Store initial values
        const initialScale = this.context.monkeyHeadContainer.scale.x;
        const initialY = this.context.monkeyHeadContainer.y;
        const initialX = this.context.monkeyHeadContainer.x;
        const initialRotation = this.context.monkeyHeadContainer.rotation;
        let animationTime = 0;
        
        // Ensure opacity is always 1
        this.context.monkeyHeadContainer.alpha = 1;
        
        console.log('Initial values:', { initialScale, initialY, initialX, initialRotation });
        
        const animate = () => {
            // Check if animation should stop (reset was called)
            if (!this.winAnimationRunning) {
                console.log('Win animation stopped by reset');
                return;
            }
            
            if (!this.context.monkeyHeadContainer) {
                console.log('Monkey head container disappeared during animation');
                this.winAnimationRunning = false;
                return;
            }
            
            animationTime += TIMING_CONFIG.ANIMATION_FRAME_TIME;
            const progress = Math.min(animationTime / WIN_ANIMATION_DURATION, 1);
            
            // Rising animation - move up by 40px
            const yOffset = ANIMATION_CONFIG.WIN_Y_OFFSET * progress;
            this.context.monkeyHeadContainer.y = initialY - yOffset;
            
            // Sway animation - oscillating left/right movement
            const swayOffset = Math.sin(animationTime * ANIMATION_CONFIG.WIN_SWAY_FREQUENCY) * 
                              ANIMATION_CONFIG.WIN_SWAY_AMPLITUDE;
            this.context.monkeyHeadContainer.x = initialX + swayOffset;

            // Rotation animation
            const rotationOffset = Math.sin(animationTime * ANIMATION_CONFIG.WIN_SWAY_FREQUENCY) *
                                   ANIMATION_CONFIG.WIN_ROTATION_AMPLITUDE;
            this.context.monkeyHeadContainer.rotation = initialRotation + rotationOffset;
            
            // Keep opacity at 1 always
            this.context.monkeyHeadContainer.alpha = 1;
            
            // Log progress occasionally
            if (Math.floor(progress * 10) !== Math.floor((progress - 0.016) * 10)) {
                console.log('Animation progress:', Math.floor(progress * 100) + '%', {
                    yOffset: yOffset.toFixed(1),
                    swayOffset: swayOffset.toFixed(1),
                    rotation: this.context.monkeyHeadContainer.rotation.toFixed(2)
                });
            }
            
            // Continue animation if not complete and not cancelled
            if (progress < 1 && this.winAnimationRunning) {
                requestAnimationFrame(animate);
            } else {
                console.log('Win animation completed');
                this.winAnimationRunning = false;
            }
        };
        
        // Start the animation
        console.log('Starting animation loop...');
        requestAnimationFrame(animate);
    }

    public startCrashAnimation(): void {
        if (!ENABLE_CRASH_ANIMATION) {
            console.log('Crash animation disabled by flag');
            return;
        }

        console.log('startCrashAnimation called');
        
        if (!this.context.monkeyHeadContainer) {
            console.log('No monkey head container for crash animation - waiting...');
            setTimeout(() => {
                if (this.context.monkeyHeadContainer) {
                    console.log('Monkey head container found after waiting, starting crash animation');
                    this.startCrashAnimation();
                } else {
                    console.log('Still no monkey head container for crash animation');
                }
            }, TIMING_CONFIG.SVG_LOAD_TIMEOUT);
            return;
        }

        console.log('Starting crash animation with container:', this.context.monkeyHeadContainer);
        
        // Store initial values
        const initialScale = this.context.monkeyHeadContainer.scale.x;
        const initialY = this.context.monkeyHeadContainer.y;
        const initialRotation = this.context.monkeyHeadContainer.rotation;
        let animationTime = 0;
        
        // Ensure opacity is always 1
        this.context.monkeyHeadContainer.alpha = 1;
        
        console.log('Crash animation initial values:', { initialScale, initialY, initialRotation });
        
        const animate = () => {
            if (!this.context.monkeyHeadContainer) {
                console.log('Monkey head container disappeared during crash animation');
                return;
            }
            
            animationTime += TIMING_CONFIG.ANIMATION_FRAME_TIME;
            const progress = Math.min(animationTime / CRASH_ANIMATION_DURATION, 1);
            
            // Falling animation - move down by the full background height
            const yOffset = ANIMATION_CONFIG.CRASH_Y_OFFSET * progress;
            this.context.monkeyHeadContainer.y = initialY + yOffset;
            
            // Rotation animation
            const rotationOffset = Math.sin(animationTime * ANIMATION_CONFIG.WIN_SWAY_FREQUENCY) *
                                   ANIMATION_CONFIG.CRASH_ROTATION_AMPLITUDE;
            this.context.monkeyHeadContainer.rotation = initialRotation + rotationOffset;
            
            // Keep opacity at 1 always
            this.context.monkeyHeadContainer.alpha = 1;
            
            // Scale up during explosion (like in HTML: transform: scale(2.5))
            const scale = initialScale + (ANIMATION_CONFIG.CRASH_SCALE_MULTIPLIER * progress);
            
            this.context.monkeyHeadContainer.scale.set(scale);
            
            // Log progress occasionally
            if (Math.floor(progress * 10) !== Math.floor((progress - 0.016) * 10)) {
                console.log('Crash animation progress:', Math.floor(progress * 100) + '%', {
                    scale: scale.toFixed(2),
                    yOffset: yOffset.toFixed(1),
                    rotation: this.context.monkeyHeadContainer.rotation.toFixed(2)
                });
            }
            
            // Continue animation if not complete
            if (progress < 1) {
                requestAnimationFrame(animate);
            } else {
                console.log('Crash animation completed');
                // Hide the container completely at the end
                this.context.monkeyHeadContainer.visible = false;
            }
        };
        
        // Start the animation
        console.log('Starting crash animation loop...');
        requestAnimationFrame(animate);
    }

    public startPropellerAnimation(): void {
        if (this.propellerAnimationInterval) {
            clearInterval(this.propellerAnimationInterval);
        }
        
        console.log('Starting propeller animation');
        
        this.propellerAnimationInterval = setInterval(() => {
            if (!this.context.isRunning || !this.context.monkeyHeadContainer || this.context.hasCrashed) {
                return;
            }
            
            // Switch between meymin.svg and meymin-2.svg for propeller effect
            const useSecondTexture = Math.random() < 0.5;
            const svgUrl = useSecondTexture && this.context.monkeyHeadSvg2Url ? 
                this.context.monkeyHeadSvg2Url : this.context.originalMonkeySvgUrl;
            
            if (svgUrl) {
                // Only switch texture, don't recreate the whole container
                void this.context.switchMonkeyTexture(svgUrl);
            }
            
            // Add subtle oscillation to position (like in the sample code)
            if (this.context.monkeyHeadContainer && this.context.data.length > 0) {
                const oscillation = (Math.random() - 0.5) * ANIMATION_CONFIG.PROPELLER_OSCILLATION;
                this.context.monkeyHeadContainer.y += oscillation;
            }
            
        }, PROPELLER_INTERVAL);
    }

    public stopPropellerAnimation(): void {
        if (this.propellerAnimationInterval) {
            console.log('Stopping propeller animation');
            clearInterval(this.propellerAnimationInterval);
            this.propellerAnimationInterval = null;
        }
    }

    public resetAnimations(): void {
        // Stop any running animations
        this.winAnimationRunning = false;
        this.stopPropellerAnimation();
        
        // Ensure opacity is always 1
        if (this.context.monkeyHeadContainer) {
            this.context.monkeyHeadContainer.alpha = 1;
        }
    }

    public resetMonkeyScale(): void {
        if (this.context.monkeyHeadContainer) {
            // Stop any running animations first
            this.winAnimationRunning = false;
            
            // Reset container scale to 1.0 and position to baseline
            this.context.monkeyHeadContainer.scale.set(1.0);
            this.context.monkeyHeadContainer.visible = true;
            this.context.monkeyHeadContainer.alpha = 1.0;
            
            console.log('Monkey scale and position reset to baseline');
        }
    }

    public get isWinAnimationRunning(): boolean {
        return this.winAnimationRunning;
    }

    public async switchMonkeyTexture(svgUrl: string): Promise<void> {
        if (!this.context.monkeyHeadContainer) return;
        
        try {
            const texture = await this.context.loadSvgAsHiResTexture(svgUrl);
            const sprite = this.context.monkeyHeadContainer.children[0] as PIXI.Sprite;
            if (sprite && sprite instanceof PIXI.Sprite) {
                sprite.texture = texture;
                // Normalize sprite width while maintaining aspect ratio
                sprite.width = TARGET_MONKEY_WIDTH;
                sprite.scale.y = sprite.scale.x;
            }
        } catch (error) {
            // Silently fail texture switches to avoid console spam
        }
    }
}

export function easeOutCubic(x: number): number {
    return 1 - Math.pow(1 - x, 3);
} 