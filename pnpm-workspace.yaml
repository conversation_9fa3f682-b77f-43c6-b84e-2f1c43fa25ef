packages:
    - "games/*"
    - "packages/*"
    - "sdk"

catalog:
    "nx": "21.1.2"
    "prettier": "3.5.3"

catalogs:
    typescript:
        "@types/node": "22.15.27"
        "tsc-alias": "1.8.16"
        "typescript": "5.8.3"

    sass:
        "sass-embedded": "1.89.0"

    postcss:
        "@hail2u/css-mqpacker": "9.0.1"
        "postcss-logical": "8.1.0"

    vite:
        "browserslist-to-esbuild": "2.1.1"
        "vite-plugin-dts": "4.5.4"
        "vite-plugin-static-copy": "3.0.0"
        "vite": "6.3.5"

    vite-images:
        "sharp": "0.34.2"
        "svgo": "3.3.2"
        "vite-plugin-image-optimizer": "1.1.8"

    eslint:
        "@eslint/js": "9.27.0"
        "eslint": "9.27.0"
        "typescript-eslint-language-service": "5.0.5"
        "typescript-eslint": "8.33.0"

    vitest:
        "@vitest/coverage-v8": "3.1.4"
        "@vitest/ui": "3.1.4"
        "vitest": "3.1.4"

    storybook:
        "@storybook/addon-a11y": "9.0.1"
        "@storybook/addon-actions": "9.0.1"
        "@storybook/addon-backgrounds": "9.0.1"
        "@storybook/addon-controls": "9.0.1"
        "@storybook/addon-measure": "9.0.1"
        "@storybook/addon-viewport": "9.0.1"
        "@storybook/html-vite": "9.0.1"
        "@storybook/html": "9.0.1"
        "@storybook/test": "9.0.0-alpha.2"
        "storybook-addon-pseudo-states": "9.0.1"
        "storybook": "9.0.1"
