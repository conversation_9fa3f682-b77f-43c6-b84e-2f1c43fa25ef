import {
    defineComponent,
    Frame,
    html,
    mergeClasses,
    type Component,
    type Props,
    type ToastControls,
} from '@monkey-tilt/ui';
import { logDevError } from '@monkey-tilt/utils';
import { Options, Hotkeys } from '@monkey-tilt/client';
import { CrashUI } from '../../crash';
import { ProvablyFair } from '../provably-fair/provably-fair';
import { Sidebar } from '../sidebar/sidebar';
import { Table } from '../table/table';

export interface GameProps extends Props<HTMLDivElement> {
    /**
     * The Crash UI state manager.
     */
    readonly ui: CrashUI;
}

export const Game = defineComponent(
    'Game',
    ({ state }) =>
        ({ ui, ...props }: GameProps): Component<HTMLDivElement> => {
            const { memo, effect, untracked } = state();

            const clientError = memo(() => {
                const error = ui.gameState().error;
                if (error) {
                    console.error('🚨 UI DETECTED ERROR:', error);
                    console.error('🚨 Full game state:', ui.gameState());
                }
                return error;
            });
            let errorToast: ToastControls | null = null;
            let previousError: string | null = null;

            effect(() => {
                const error = clientError();

                if (error) {
                    let ignore = false;
                    let message = '';

                    switch (error.code) {
                        case 'TIMEOUT':
                            message = 'Connection timed out. Please try again.';
                            ignore = untracked(ui.gameState).isLoading;
                            break;
                        case 'CLIENT_NOT_AUTHENTICATED':
                        case 'AUTH_FAILED':
                            message = 'Please log in to play.';
                            break;
                        case 'INSUFFICIENT_FUNDS':
                            message = 'Insufficient balance.';
                            break;
                        case 'ROUND_ALREADY_ACTIVE':
                        case 'GAME_ROUND_NOT_FOUND':
                        case 'BET_AMOUNT_EXCEEDS_LIMIT': // error is shown in the sidebar instead
                            ignore = true;
                            break;
                        default:
                            logDevError(error);
                            message = 'An error occurred. Please try reloading the page.';
                    }

                    if (message === previousError) {
                        return;
                    }

                    previousError = message;

                    if (errorToast) {
                        errorToast.dismiss();
                    }

                    if (ignore) {
                        return;
                    }

                    errorToast = ui.showToast({
                        variant: 'error',
                        dismiss: { after: 2 * 60 * 1000 },
                        content: message,
                    });
                } else if (errorToast) {
                    errorToast.dismiss();
                    errorToast = null;
                    previousError = null;
                }
            });
            return html('div')(
                {
                    ...props,
                    className: mergeClasses(props.className, { 'm-crash': true }),
                },
                Sidebar({ ui }),
                Table({ ui }),
                // eslint-disable-next-line @typescript-eslint/no-unsafe-argument, @typescript-eslint/no-unsafe-call
                Frame(
                    { variant: 'small', className: { 'm-crash__options-bar': true } },
                    Options({ ui, namespace: 'crash' }),
                ),
                Hotkeys({ ui }),
                ProvablyFair({ ui }),
            );
        },
);
