/// <reference types="vitest" />

import { resolve } from 'node:path';
import { defineConfig } from 'vite';
import dts from 'vite-plugin-dts';
import packageJson from './package.json' with { type: 'json' };

const globals = Object.keys(packageJson.peerDependencies ?? {})
    .filter((key) => key.startsWith('@monkey-tilt/'))
    .reduce(
        (acc, key) => {
            acc[key] = 'MT_' + key.split('/').pop()!.replace(/-/g, '_');
            return acc;
        },
        {} as Record<string, string>,
    );

export default defineConfig(() => ({
    plugins: [dts({ rollupTypes: true })],
    build: {
        target: 'esnext',
        sourcemap: true,
        lib: {
            entry: resolve(__dirname, 'lib/index.ts'),
            name: 'MT_state',
            fileName: (format) => `state.${format}.js`,
        },
        rollupOptions: {
            external: Object.keys(globals),
            output: {
                globals,
            },
        },
    },
    test: {
        reporters: process.env.GITHUB_ACTIONS ? ['dot', 'github-actions'] : ['default'],
    },
}));
