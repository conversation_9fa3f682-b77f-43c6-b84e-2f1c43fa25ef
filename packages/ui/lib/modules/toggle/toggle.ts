import {
    defineComponent,
    forwardRef,
    html,
    type Component,
    type Props,
    type RefObject,
} from '../../component';
import { mergeClasses, withNamespace } from '../../utilities/classes';

export interface ToggleControls {
    /**
     * Set the checked state of the toggle.
     *
     * @param isChecked - The new checked state of the toggle.
     */
    setChecked(this: void, isChecked: boolean): void;
}

export interface ToggleProps extends Omit<Props<HTMLInputElement>, 'value'> {
    readonly label?: string;
    readonly onCheckedChange?: (isChecked: boolean) => void;
    readonly controls?: (controls: ToggleControls) => void;
}

export const Toggle = defineComponent(
    'Toggle',
    () =>
        ({
            checked = false,
            label = 'Toggle',
            onCheckedChange,
            controls,
            ...props
        }: ToggleProps): Component<HTMLDivElement> => {
            const inputRef: RefObject<HTMLInputElement> = { current: null };

            const id = props.id || `toggle-${Math.random().toString(36).slice(2)}`;

            if (controls) {
                controls({
                    setChecked: (isChecked) => {
                        if (inputRef.current) {
                            inputRef.current.checked = isChecked;
                        }
                    },
                });
            }

            return html('div')(
                { className: mergeClasses(props.className, withNamespace('m-toggle')) },
                html('input')({
                    ...props,
                    id,
                    ref: forwardRef(props.ref, inputRef),
                    type: 'checkbox',
                    checked,
                    className: {
                        'u-hide-visually': true,
                    },
                    onChange: (e) => {
                        if (onCheckedChange) {
                            onCheckedChange(
                                e.target instanceof HTMLInputElement && e.target.checked,
                            );
                        }
                    },
                }),
                html('label')(
                    { htmlFor: id },
                    html('span')({ className: withNamespace('u-hide-visually') }, label),
                    html('span')({ className: withNamespace('m-toggle__thumb') }),
                ),
            );
        },
);
