import type { Ball } from './bodies/ball';
import { Peg } from './bodies/peg';
import { Wall } from './bodies/wall';
import { CELL_SIZE, PEG_RADIUS } from './constants';

export interface BoardOptions<P extends Peg = Peg, W extends Wall = Wall> {
    readonly rows: number;
    readonly cellSize?: number;
    readonly pegRadius?: number;
    readonly createPeg?: (x: number, y: number, radius: number, cellSize: number) => P;
    readonly createWall?: (x1: number, y1: number, x2: number, y2: number) => W;
}

export class Board<
    B extends Ball = Ball,
    P extends Peg = Peg,
    W extends Wall = Wall,
> extends EventTarget {
    #rows: number;
    #columns: number;

    #size: {
        w: number;
        h: number;
    };

    #cellSize: number;

    #pegs: (P | null)[][];
    #walls: W[];
    #balls: B[] = [];

    #hasHitEventHandlers = false;
    #hasWallCollisionEventHandlers = false;
    #hasPegCollisionEventHandlers = false;

    public readonly startRect: {
        readonly x: number;
        readonly y: number;
        readonly w: number;
        readonly h: number;
    };

    public constructor({
        rows,
        cellSize = CELL_SIZE,
        pegRadius = PEG_RADIUS,
        createPeg = (x, y, radius, cellSize) => new Peg(x, y, radius, cellSize) as P,
        createWall = (x1, y1, x2, y2) => new Wall(x1, y1, x2, y2) as W,
    }: BoardOptions<P, W>) {
        super();

        this.#rows = rows;
        this.#columns = rows + 2;

        this.#cellSize = cellSize;
        this.#size = { w: this.#columns * cellSize, h: rows * cellSize };

        const pegDiameter = pegRadius * 2;
        const center = cellSize / 2;

        this.startRect = {
            x: ((rows - 1) / 2 + 0.5) * cellSize - pegDiameter,
            y: -(cellSize * 2),
            w: (cellSize + pegDiameter) * 2,
            h: center,
        };

        const topLeft = this.startRect.x;
        const topRight = this.startRect.x + this.startRect.w;
        const bottom = this.#size.h - center - pegDiameter;

        this.#walls = [
            createWall(topLeft, 0, topLeft, center),
            createWall(topRight, 0, topRight, center),
            createWall(topLeft, center, center - pegDiameter, bottom),
            createWall(topRight, center, this.#size.w - center + pegDiameter, bottom),
        ];

        this.#pegs = Array.from({ length: this.#rows }, () => Array(this.#columns).fill(null));

        for (let y = 0; y < this.#rows; y++) {
            for (let i = 0; i < y + 3; i++) {
                const x = i + (this.#rows - y - 1) / 2;
                this.#pegs[y]![Math.ceil(x)] = createPeg(x, y, pegRadius, this.#cellSize);
            }
        }
    }

    public get rows(): number {
        return this.#rows;
    }

    public get columns(): number {
        return this.#columns;
    }

    public get width(): number {
        return this.#size.w;
    }

    public get height(): number {
        return this.#size.h;
    }

    public get cellSize(): number {
        return this.#cellSize;
    }

    public get balls(): ReadonlyArray<B> {
        return this.#balls;
    }

    public get pegs(): ReadonlyArray<ReadonlyArray<P | null>> {
        return this.#pegs;
    }

    public addBall(ball: B): void {
        if (ball.isAlive) {
            // const left = ball.x - ball.radius;
            // const right = ball.x + ball.radius;
            // const top = ball.y - ball.radius;
            // const bottom = ball.y + ball.radius;

            // const { x, y, w, h } = this.startRect;

            // if (left < x || right > x + w || top < y || bottom > y + h) {
            //     throw new Error('Ball is out of bounds');
            // }

            this.#balls.push(ball);
            this.dispatchEvent(new CustomEvent('ballAdded', { detail: { ball } }));
        }
    }

    public update(dt: number): void {
        const ballCount = this.#balls.length;
        for (let i = ballCount - 1; i >= 0; i--) {
            const ball = this.#balls[i]!;

            ball.update(dt);

            const row = Math.max(0, Math.floor((ball.y + ball.radius / 2) / this.#cellSize));

            let outOfBounds = false;

            if (ball.x < 0 || ball.x > this.#size.w) {
                outOfBounds = true;
            } else if (row < this.rows) {
                for (const peg of this.#pegs[row]!) {
                    if (
                        peg !== null &&
                        peg.checkForCollision(ball) &&
                        this.#hasPegCollisionEventHandlers
                    ) {
                        this.dispatchEvent(
                            new CustomEvent('pegCollision', {
                                detail: { ball, peg },
                            }),
                        );
                    }
                }

                for (const wall of this.#walls) {
                    if (wall.checkForCollision(ball) && this.#hasWallCollisionEventHandlers) {
                        this.dispatchEvent(
                            new CustomEvent('wallCollision', {
                                detail: { ball, wall },
                            }),
                        );
                    }
                }
            }

            ball.checkBuckets(this);

            if (ball.bucketNumber !== undefined && this.#hasHitEventHandlers) {
                void Promise.resolve().then(() => {
                    this.dispatchEvent(
                        new CustomEvent('hit', {
                            detail: { ball, bucketNumber: ball.bucketNumber },
                        }),
                    );
                });
            }

            if (outOfBounds || !ball.isAlive) {
                this.#balls.splice(i, 1);
            }
        }

        if (ballCount > 0 && this.#balls.length === 0) {
            void Promise.resolve().then(() => {
                this.dispatchEvent(new Event('boardEmpty'));
            });
        }
    }

    public override addEventListener(
        type: 'boardEmpty',
        callback: (event: Event) => void,
        options?: AddEventListenerOptions | boolean,
    ): void;

    public override addEventListener(
        type: 'ballAdded',
        callback: (event: CustomEvent<{ ball: B }>) => void,
        options?: AddEventListenerOptions | boolean,
    ): void;

    public override addEventListener(
        type: 'hit',
        callback: (event: CustomEvent<{ ball: B; bucketNumber: number }>) => void,
        options?: AddEventListenerOptions | boolean,
    ): void;

    public override addEventListener(
        type: 'pegCollision',
        callback: (event: CustomEvent<{ ball: B; peg: P }>) => void,
        options?: AddEventListenerOptions | boolean,
    ): void;

    public override addEventListener(
        type: 'wallCollision',
        callback: (event: CustomEvent<{ ball: B; wall: W }>) => void,
        options?: AddEventListenerOptions | boolean,
    ): void;

    public override addEventListener(
        type: string,
        callback: EventListener | null,
        options?: AddEventListenerOptions | boolean,
    ): void;

    public override addEventListener(
        type: string,
        callback: (...args: any[]) => void | null,
        options?: AddEventListenerOptions | boolean,
    ): void {
        if (type === 'hit') {
            this.#hasHitEventHandlers = true;
        } else if (type === 'pegCollision') {
            this.#hasPegCollisionEventHandlers = true;
        } else if (type === 'wallCollision') {
            this.#hasWallCollisionEventHandlers = true;
        }
        super.addEventListener(type, callback, options);
    }
}
