@use './common/bem';

@use './config';
@use './defaults';
@use './elements';
@use './layouts';
@use './utilities';

@use '../modules/badge/badge';
@use '../modules/bar/bar';
@use '../modules/bucket/bucket';
@use '../modules/button/button';
@use '../modules/chip/chip';
@use '../modules/die/die';
@use '../modules/frame/frame';
@use '../modules/icon/icon';
@use '../modules/input/input';
@use '../modules/label/label';
@use '../modules/modal/modal';
@use '../modules/multiplier/multiplier';
@use '../modules/radio/radio';
@use '../modules/slider/slider';
@use '../modules/stats/stats';
@use '../modules/stepper/stepper';
@use '../modules/table/table';
@use '../modules/tabs/tabs';
@use '../modules/tile/tile';
@use '../modules/toast/toast';
@use '../modules/toggle/toggle';
@use '../modules/tooltip/tooltip';

@include bem.scope('app') {
    @include config.styles;
    @include defaults.styles;
    @include elements.styles;
}

@include layouts.styles;

// modules:
@include badge.styles;
@include bar.styles;
@include bucket.styles;
@include button.styles;
@include chip.styles;
@include die.styles;
@include frame.styles;
@include icon.styles;
@include input.styles;
@include label.styles;
@include modal.styles;
@include multiplier.styles;
@include radio.styles;
@include slider.styles;
@include stats.styles;
@include stepper.styles;
@include table.styles;
@include tabs.styles;
@include tile.styles;
@include toast.styles;
@include toggle.styles;
@include tooltip.styles;
// --

@include utilities.styles;
