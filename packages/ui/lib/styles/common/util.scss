@use 'sass:list';
@use 'sass:map';
@use 'sass:math';
@use 'sass:meta';
@use 'sass:string';

///
/// Concatenates the elements of a list into a string inserting the provided $glue between elements.
///
@function concat($list, $glue: ', ', $skip-empty: false) {
    $result: '';
    $length: list.length($list);
    $i: 1;
    @each $item in $list {
        $item: '#{$item}';
        @if not $skip-empty or string.length($item) > 0 {
            $result: $result + $item;
            @if $i < $length {
                $result: $result + $glue;
            }
        }
        $i: $i + 1;
    }
    @return $result;
}

///
/// Returns the value of a key in a map, or a default value if the key is not found or the value is null.
///
/// If the key is a list, the function will recursively get the value of the nested keys.
///
/// @param {Map} $map - The map to get the value from.
/// @param {List|String} $key - The key to get the value for.
/// @param {*} $default - The default value to return if the key is not found or the value is null.
///
@function get($map, $key, $default: null) {
    $value: null;
    @if meta.type-of($key) == 'list' {
        $value: map.get($map, $key...);
    } @else if meta.type-of($map) == 'map' and map.has-key($map, $key) {
        $value: map.get($map, $key);
    }
    @return if(meta.type-of($value) != 'null', $value, $default);
}

///
/// Returns the non-null value of a first found key from $keys list, or $default if no key from the $keys is found in the $map.
///
@function get-first($map, $keys, $default: null) {
    @each $key in ensure-list($keys) {
        $value: get($map, $key);
        @if meta.type-of($value) != 'null' {
            @return $value;
        }
    }
    @return $default;
}

///
/// Returns an empty map.
///
@function empty-map() {
    // prettier-ignore
    @return map.remove((_: _), _);
}

///
/// Ensures the $value is a map.
///
/// If $value is a list, it is converted to a map with keys as indexes.
/// If $value is not a list or a map, an empty map is returned.
///
/// @param {*} $value - The value to convert to a map.
///
@function ensure-map($value) {
    @if meta.type-of($value) != 'map' {
        $result: empty-map();
        @if meta.type-of($value) == 'list' {
            @for $i from 1 through list.length($value) {
                $result: map.set($result, $i, list.nth($value, $i));
            }
        }
        @return $result;
    }
    @return $value;
}

///
/// Returns a new map with keys prefixed and/or suffixed with the specified strings.
///
/// @param {Map} $map - The map to affix keys for.
/// @param {KeywordArgs} $args - The arguments to control the affixing.
///                             - $prefix: The string to prefix the keys with.
///                             - $suffix: The string to suffix the keys with.
///
@function affix-keys($map, $args...) {
    $args: meta.keywords($args);
    $prefix: get($args, 'prefix', '');
    $suffix: get($args, 'suffix', '');

    $result: empty-map();
    @each $key, $value in $map {
        $result: map.set($result, $prefix + $key + $suffix, $value);
    }

    @return $result;
}

///
/// Returns a new container with values of the specified key from element values.
///
/// If the key is not found in an element, the default value is used.
///
/// A container can be a list or a map.
///
/// @param {List|Map} $container - The container to pluck values from.
/// @param {List|String} $keys - The key or a list of keys (to try, in order) to pluck from the elements.
/// @param {*} $default - The default value to use if the key is not found.
///
@function pluck($container, $keys, $default: null) {
    @if meta.type-of($container) == 'map' {
        $result: empty-map();

        @each $key, $value in $container {
            $result: map.set($result, $key, get-first($value, $keys, $default));
        }

        @return $result;
    }

    $list: ensure-list($container);
    $result: empty-list(list.separator($list));

    @each $item in $list {
        $result: list.append($result, get-first($item, $keys, $default));
    }

    @return $result;
}

///
/// Returns the number of items in a list, map, or arglist.
///
/// If the value is null, 0 is returned.
/// Any other value is considered a single-element list.
///
@function size($value) {
    $type: meta.type-of($value);
    @if $type == 'list' or $type == 'arglist' {
        @return list.length($value);
    }
    @if $type == 'map' {
        @return list.length(map.keys($value));
    }
    @return if($value == null, 0, 1);
}

///
/// Returns an empty list with the given $separator (defaults to comma).
///
@function empty-list($separator: comma) {
    @return list.join((), (), $separator);
}

///
/// Ensures the $value is a list (i.e., if it is not already a list it is converted to a single-element list).
/// If $value is null, it is returned as null.
/// If $value is a string, and $separator is provided, it is split into a list using the separator.
///
@function ensure-list($value, $separator: null) {
    @if $value != null and meta.type-of($value) != 'list' and meta.type-of($value) != 'arglist' {
        @if meta.type-of($value) == 'string' and $separator {
            $value: string.split($value, $separator);
        } @else {
            $value: ($value);
        }
    }
    @return $value;
}

///
/// Returns the first element in the list.
///
/// If the list is empty, null is returned.
///
/// @param {List} $list - The list to get the first element from.
///
@function head($list) {
    @return list.nth($list, 1);
}

///
/// Returns the last element in the list.
///
/// If the list contains less than 2 elements, null is returned.
///
/// @param {List} $list - The list to get the last element from.
///
@function tail($list) {
    @if list.length($list) < 2 {
        @return null;
    }
    @return list.nth($list, list.length($list));
}

///
/// Wrapper around meta.keywords() that also handles positional arguments.
///
/// If a map is given instead of an arglist, it is returned as is.
///
/// If only a single positional argument is given, and it is a map, it is
/// returned as a keyword map, unless $coerce-single-map is set to false.
///
// stylelint-disable-next-line scss/no-global-function-names
@function keywords($value, $coerce-single-map: true) {
    @if meta.type-of($value) == 'map' {
        @return $value;
    }

    $keywords: empty-map();
    $value: ensure-list($value);
    $num-positional: list.length($value);

    @if meta.type-of($value) == 'arglist' {
        $keywords: meta.keywords($value);

        @if $coerce-single-map and
            (size($keywords) == 0) and
            ($num-positional == 1) and
            (meta.type-of(head($value)) == 'map')
        {
            @return head($value);
        }
    }

    @if $num-positional > 0 {
        @for $i from 1 through $num-positional {
            $keywords: map.set($keywords, $i, list.nth($value, $i));
        }
    }

    @return $keywords;
}

///
/// Returns the number without its unit.
///
/// @param {Number} $number - The number to strip the unit from.
///
@function strip-unit($number) {
    @if meta.type-of($number) == 'number' and not math.is-unitless($number) {
        @return math.div($number, ($number * 0 + 1));
    }
    @return $number;
}

$-digits: ('0', '1', '2', '3', '4', '5', '6', '7', '8', '9');

///
/// Parses a number and returns a list with the unitless number and its unit.
/// If the value is not a number, nor a numeric string, returns null.
///
/// If $allowUnitOnly is true, the function will return the value as 1 and the unit as the input value.
///
/// If $allowDecimal is false, the function will round the number to the nearest integer.
///
/// @param {Number|String} $value - The value to be parsed.
/// @param {Boolean} $allowUnitOnly - Whether to allow only unit values.
/// @param {Boolean} $allowDecimal - Whether to allow decimal values.
///
@function parse-number($value, $allowUnitOnly: false, $allowDecimal: true) {
    @if meta.type-of($value) == 'number' {
        $unit: math.unit($value);
        $value: strip-unit($value);
        @if not $allowDecimal {
            $value: math.floor($value);
        }
        @return ($value, $unit);
    }

    $value: #{$value};
    $sign: 1;
    $number: 0;

    @if string.length($value) == 0 or $value == '-' {
        @return null;
    }

    @if string.slice($value, 1, 1) == '-' {
        $sign: -1;
        $value: string.slice($value, 2);
    }

    $is-valid: false;
    $decimal-digits: 0;
    $is-decimal: false;

    @for $i from 1 through string.length($value) {
        $ch: string.slice($value, $i, $i);
        @if $ch == '.' {
            @if not $allowDecimal or $is-decimal {
                @return if(
                    not $is-valid,
                    if($allowUnitOnly, (1, $value), null),
                    (
                        math.div($number * $sign, math.pow(10, $decimal-digits)),
                        string.slice($value, $i)
                    )
                );
            } @else {
                $is-decimal: true;
            }
        } @else {
            $index: list.index($-digits, $ch);
            @if not $index {
                @return if(
                    not $is-valid,
                    if($allowUnitOnly, (1, $value), null),
                    (
                        math.div($number * $sign, math.pow(10, $decimal-digits)),
                        string.slice($value, $i)
                    )
                );
            }
            $is-valid: true;
            @if $is-decimal {
                $decimal-digits: $decimal-digits + 1;
            }
            $number: $number * 10 + ($index - 1);
        }
    }

    @return (math.div($number * $sign, math.pow(10, $decimal-digits)), '');
}

///
/// Converts a value to a number.
///
@function to-number($value, $strict: true) {
    $result: parse-number($value);
    @if not $result {
        @return if($strict, null, 0);
    }
    @return if($strict and tail($result) != '', null, head($result));
}

// prettier-ignore
$-known-units: (
    '': 1,
    '%':    1%,    'Q':    1Q,    's':    1s,    'x':     1x,     'ch':    1ch,
    'cm':   1cm,   'em':   1em,   'ex':   1ex,   'fr':    1fr,    'Hz':    1Hz,
    'ic':   1ic,   'in':   1in,   'lh':   1lh,   'mm':    1mm,    'ms':    1ms,
    'pc':   1pc,   'pt':   1pt,   'px':   1px,   'vb':    1vb,    'vh':    1vh,
    'vi':   1vi,   'vw':   1vw,   'cap':  1cap,  'cqb':   1cqb,   'cqh':   1cqh,   
    'cqi':  1cqi,  'cqw':  1cqw,  'deg':  1deg,  'dpi':   1dpi,   'kHz':   1kHz,
    'rad':  1rad,  'rch':  1rch,  'rem':  1rem,  'rex':   1rex,   'ric':   1ric,
    'rlh':  1rlh,  'dpcm': 1dpcm, 'dppx': 1dppx, 'grad':  1grad,  'rcap':  1rcap,
    'turn': 1turn, 'vmax': 1vmax, 'vmin': 1vmin, 'cqmax': 1cqmax, 'cqmin': 1cqmin,
);

///
/// The number of pixels in a rem unit.
///
$rem: 16;

// prettier-ignore
$-factors: (
    'rem':  ( 'em':  1, 'px': $rem, 'rex': 2, 'rch': 2, 'ric': 1, 'rlh': 0.7692, 'rcap': 1.3333),
    'ch':   ( 'rch': 1 ),
    'ex':   ('rex': 1 ),
    'ic':   ('ric': 1),
    'lh':   ( 'rlh': 1 ),
    'cap':  ( 'rcap': 1 ),
    'cm':   ( 'mm':  10,   'Q':   40 ),
    'in':   ( 'cm':  2.54, 'pc':  6, 'pt': 72, 'px': 96 ),
    'vb':   ( 'vh':  1 ), 
    'vi':   ( 'vw':  1 ),
    'cqb':  ( 'cqh': 1 ), 
    'cqi':  ( 'cqw': 1 ),
    'dpcm': ( 'dpi': 2.54 ),
    'dppx': ( 'dpi': 96,   'x': 1 ),
);

///
/// Builds a lookup table of conversion factors between units.
///
@function -build-factor-table($factors) {
    $result: empty-map();
    @each $from, $to-factors in $factors {
        @each $res-from, $res-to in $result {
            @if map.has-key($to-factors, $res-from) {
                $factor: map.get($to-factors, $res-from);
                @each $to, $f in $res-to {
                    @if not map.has-key($to-factors, $to) {
                        $to-factors: map.set($to-factors, $to, $factor * $f);
                    }
                }
            }
        }
        $result: map.set($result, $from, $to-factors);
        @each $to, $factor in $to-factors {
            $result: map.set(
                $result,
                $to,
                map.set(ensure-map(map.get($result, $to)), $from, math.div(1, $factor))
            );
            @each $to2, $factor2 in $to-factors {
                @if $to != $to2 {
                    $result: map.set(
                        $result,
                        $to,
                        map.set(
                            ensure-map(map.get($result, $to)),
                            $to2,
                            math.div(1, $factor) * $factor2
                        )
                    );
                }
            }
        }
    }
    @return $result;
}

$-factors: -build-factor-table($-factors);

///
/// Returns the $value converted to the $unit.
///
/// If $value is a string, it is parsed to a number. If parsing fails, an error is thrown.
///
/// If $value unit and $unit are compatible, the value is converted to the new unit.
///
/// The normally incompatible px and rem units are considered compatible by this function,
/// they are converted using the base.$rem value (the em unit is treated identical to rem).
///
/// If $value unit and $unit are incompatible, an error is thrown by default.
/// This can be changed to 'ignore' to return the value in the new unit,
/// or 'abort' to return the value in the original unit.
///
/// If $from is provided, the value is first converted to the $from unit.
/// This is useful when the value is unitless but you want to treat it as a specific unit.
///
/// @param {Number|String} $value - The value to be converted.
/// @param {String} $unit - The unit to convert the value to.
/// @param {String} $incompatible - The behavior when the units are incompatible:
///                                 'error', 'ignore', or 'abort'.
/// @param {String} $from - The unit to convert the value from.
///
@function to-unit($value, $unit, $incompatible: error, $from: null) {
    @if meta.type-of($unit) == 'number' {
        $unit: math.unit($unit);
    }

    @if $from {
        $value: to-unit(
            $value,
            if(meta.type-of($from) == 'number', math.unit($from), $from),
            $incompatible
        );
    }

    @if $unit == '%' {
        @return to-percentage($value);
    }

    $result: parse-number($value);

    @if not $result {
        @error 'Invalid value: #{$value}';
    }

    $number: head($result);
    $from-unit: tail($result);

    @if $from-unit == '' or $unit == $from-unit {
        @return $number * map.get($-known-units, $unit);
    }

    $factor: map.get($-factors, $from-unit, $unit);

    @if not $factor {
        $from: get($-known-units, $from-unit, 1);
        $to: get($-known-units, $unit, 1);

        @if math.compatible($from, $to) {
            $factor: math.div($from, $to);
        } @else if $incompatible == 'ignore' {
            $factor: 1;
        } @else if $incompatible == 'abort' {
            @return $number * $from;
        } @else {
            @error 'Incompatible units: #{$from-unit} and #{$unit}';
        }
    }

    @return $number * $factor * map.get($-known-units, $unit);
}

///
/// Converts a value to percentage.
///
/// @param {Number} $value - The value to be converted to percentage.
///
@function to-percentage($value) {
    $result: parse-number($value);
    @if not $result {
        @return 0%;
    }
    $number: head($result);
    $unit: tail($result);
    @if $unit == '%' or math.floor($number) == $number {
        @return $number * 1%;
    }
    @return math.percentage($number);
}

///
/// Turns a percentage into an unitless factor. E.g. to-factor(50%) -> 0.5
///
@function to-factor($percentage) {
    @return math.div($percentage, 100%);
}

///
/// Return true if $value is a valid <percentage> CSS data type (i.e., it is a number with % unit).
///
@function is-percentage($value) {
    @return meta.type-of($value) == 'number' and math.unit($value) == '%';
}

///
/// Converts a given value to rem units.
///
/// @param {Number} $value - The value to be converted to rem units.
///
@function to-rem($value, $from: null) {
    @return to-unit($value, rem, error, $from);
}
