@use '../../styles/common/bem';
@use '../../styles/common/font-weight';
@use '../../styles/common/gradient-border' as *;
@use '../../styles/common/prop';
@use '../../styles/common/respond';
@use '../../styles/common/type-style' as *;
@use '../../styles/common/util';
@use '../../styles/config/metrics';

$-block-size: 40px;
$-padding-desktop: util.to-rem(14px) util.to-rem(12px);
$-padding-desktop--highlight: util.to-rem(6px) util.to-rem(12px);
$-padding-mobile--highlight: util.to-rem(2px) util.to-rem(10px);
$-radius: $-block-size * 0.25;
$-radius-desktop--highlight: util.to-rem(8px);
$-radius-mobile--highlight: util.to-rem(4px);

@mixin styles {
    @include bem.module(label) {
        display: inline-flex;
        flex-direction: row;
        align-items: center;
        gap: 0.3em;
        color: prop.get(color-white);

        @include type-style(body);

        @include prop.set(icon-size, prop.get(size-xl));

        #{bem.selector('l-flex')} > & {
            max-width: fit-content;
        }

        @include respond.until(m) {
            @include font-weight.set(medium);
        }

        @include bem.module(icon) {
            cursor: pointer;
            margin-inline-end: 0.2em;
        }

        &__tooltip {
            @include prop.set(icon-size, prop.get(size-m));
        }

        &--action {
            user-select: none;
            cursor: pointer;

            &:hover {
                color: prop.get(color-mt-400);
            }
        }

        &--subtle,
        &--error {
            color: prop.get(color-zinc-400);
            line-height: util.to-rem(16px);

            @include font-weight.set(normal);
        }

        &--error {
            color: prop.get(color-red-400);
        }

        &--medium {
            @include type-style(c3);
        }

        &--big {
            @include type-style(heading);
        }

        &--light {
            @include type-style(body2);
        }

        &--caption {
            @include type-style(caption);
        }

        $-ribbon-cut-size: 1em;
        $-ribbon-padding-block: prop.get(size-3xs-s);
        $-ribbon-padding-inline: calc($-ribbon-cut-size + prop.get(size-m-xl));

        &--ribbon-l,
        &--ribbon-r {
            background: prop.get(color-zinc-800);
            color: prop.get(color-zinc-350);
            padding: $-ribbon-padding-block $-ribbon-padding-inline;
            justify-content: center;
            text-align: center;
            text-wrap: balance;

            @include type-style(c4);
        }

        &--ribbon-l {
            clip-path: polygon(0 0, 100% 0, 100% 100%, 0 100%, $-ribbon-cut-size 50%);
        }

        &--ribbon-r {
            clip-path: polygon(0 0, 100% 0, calc(100% - $-ribbon-cut-size) 50%, 100% 100%, 0 100%);
        }

        &--box,
        &--small-box {
            background: prop.get(color-zinc-800);
            color: prop.get(color-zinc-50);
            border-radius: util.to-rem(8px);
            border: util.to-rem(1px) solid prop.get(color-zinc-700);
            padding: prop.get(size-xs) prop.get(size-m-l);
            flex-grow: 0;

            @include type-style(
                c1,
                $with: (font-weight: bold, font-size: metrics.fluid-size(12, 20), line-height: 1)
            );

            @include respond.until(m) {
                @include font-weight.set(bold);
            }
        }

        &--small-box {
            padding: metrics.fluid-size(4, 8) metrics.fluid-size(10, 16);
            border-radius: util.to-rem(12px);
            font-size: metrics.fluid-size(12, 14);
            line-height: metrics.fluid-size(16, 20);
            font-weight: 600;

            @include respond.until(m) {
                font-weight: 500;
            }
        }

        &--bubble {
            border-radius: util.to-rem(32px);
            background: prop.get(color-mt-950);
            color: prop.get(color-mt-400);
            padding: util.to-rem(2px) util.to-rem(6px);

            @include type-style(caps, $with: (text-transform: none));
        }

        &--highlight {
            background-color: prop.get(color-mt-950);
            color: prop.get(color-mt-400);
            border-radius: $-radius-desktop--highlight;
            padding: $-padding-desktop--highlight;

            @include type-style(body2);

            @include respond.until(s) {
                border-radius: $-radius-mobile--highlight;
                padding: $-padding-mobile--highlight;
            }
        }
    }

    @include bem.module(label-group) {
        display: flex;
        align-items: center;
        justify-content: space-between;

        padding: $-padding-desktop;
        border-radius: $-radius;

        background-color: prop.get(color-zinc-800);
        color: prop.get(color-white);

        &:has(> * > #{bem.namespaced-selector('m-label--highlight')}) {
            padding-block: 0;

            @include respond.until(m) {
                padding-block: util.to-rem(8px);
            }
        }

        &--nowrap {
            flex-wrap: nowrap;
        }

        &:not(#{bem.namespaced-selector('m-label-group--nowrap')}) {
            @include respond.until(s) {
                flex-direction: column;
                gap: util.to-rem(6px);
            }
        }

        &__text {
            @include type-style(body2, $with: (line-height: 0));
            color: prop.get(color-zinc-400);
        }

        &__value {
            @include type-style(body2, $with: (line-height: 0));
            color: prop.get(color-mt-200);
        }

        &--box {
            background: linear-gradient(135deg, #212122 0%, #101012 100%);
            border: none;
            padding: util.to-rem(12px);
            border-radius: util.to-rem(12px);
            box-shadow: inset 0 0 0 util.to-rem(1px) rgba(200, 200, 200, 0.2);
        }
    }
}
