export interface FontSpec {
    family: string | string[];
    display?: 'auto' | 'block' | 'swap' | 'fallback' | 'optional';
    text?: string;
}

export function loadFonts({ family, display = 'swap', text }: FontSpec) {
    const params = [
        'family=' +
            (Array.isArray(family)
                ? family.map(encodeURIComponent).join('&family=')
                : encodeURIComponent(family)),
        `display=${encodeURIComponent(display)}`,
    ];

    if (text) {
        params.push(`text=${encodeURIComponent(text)}`);
    }

    const link = document.createElement('link');
    link.rel = 'stylesheet';
    link.href = `https://fonts.googleapis.com/css2?${params.join('&')}`;
    document.head.appendChild(link);
}
