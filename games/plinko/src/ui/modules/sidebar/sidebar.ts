import { format<PERSON>ur<PERSON>cy, BetAmount, Options } from '@monkey-tilt/client';
import {
    Button,
    defineComponent,
    Flex,
    Frame,
    html,
    Label,
    mergeClasses,
    namespaced,
    Slider,
    Stack,
    Tabs,
    withNamespace,
    type Component,
    type Props,
    type TabControls,
} from '@monkey-tilt/ui';
import { type PlinkoUI } from '../../plinko';
import { Autobet } from './autobet';

export interface SidebarProps extends Props<HTMLDivElement> {
    /**
     * The Plinko UI state manager.
     */
    readonly ui: PlinkoUI;
}

export const Sidebar = defineComponent(
    'Sidebar',
    ({ state }) =>
        ({ ui, ...props }: SidebarProps): Component<HTMLDivElement> => {
            const { effect, memo } = state();

            const div = html('div');

            const actionState = memo(() => {
                const {
                    next_actions = [],
                    round_closed = true,
                    readyState = 'closed',
                    isAuthenticated = false,
                    isLoading,
                    isAnimatingState,
                } = ui.gameState();
                return {
                    next_actions,
                    round_closed,
                    canPlay:
                        !isLoading &&
                        !isAnimatingState &&
                        readyState === 'ready' &&
                        isAuthenticated,
                };
            });

            const isAutobetActive = memo(() => ui.gameState().isAutobetActive);

            const limits = memo(() => ui.gameState().bet_limits.bet);

            const betButton = Button({
                cta: true,
                label: memo(() => (isAutobetActive() ? 'Stop' : 'Bet')),
                icon: 'plus',
                className: { 'm-plinko__bet-button': true },
                ref: (element: HTMLButtonElement) => {
                    effect(() => {
                        element.disabled =
                            !ui.isBetAllowed() || ui.isBettingLocked() || ui.shouldCloseAutobet();
                    });
                },
                onClick() {
                    if (!isAutobetActive()) {
                        void ui.playSound('bet');
                    }
                    ui.triggerAction('BetOrStop');
                },
            });

            const handleRiskButton = (n: number) => (element: HTMLButtonElement) => {
                effect(() => {
                    element.classList.toggle('pressed', ui.risk.read() == n);
                });
                effect(() => {
                    const isDisabled = !ui.isBoardEditingAllowed();
                    if (element && 'disabled' in element) {
                        element.disabled = isDisabled;
                    }
                });
            };

            const hideOnMobile = withNamespace('u-hide@m');
            const showOnMobile = withNamespace('u-show@m');

            return Frame(
                { ...props, className: { 'm-plinko__sidebar': true } },

                Frame.Section(
                    {
                        variant: 'subtle',
                        className: { 'm-plinko__tabs': true },
                        ref: (element: HTMLDivElement) => {
                            effect(() => {
                                element.classList.toggle(
                                    hideOnMobile,
                                    ui.isBettingLocked() || isAutobetActive(),
                                );
                            });
                        },
                    },
                    Tabs({
                        ariaLabel: 'Bet type',
                        value: ui.activeTab.read(),
                        tabs: [
                            { label: 'Manual', value: 'manual' },
                            { label: 'Auto', value: 'auto' },
                        ],
                        onChange(value: 'manual' | 'auto') {
                            ui.activeTab.update(value);
                        },
                        controls({ selectTab, setEnabled }: TabControls) {
                            effect(() => {
                                selectTab(ui.activeTab.read());
                            });
                            effect(() => {
                                setEnabled(!ui.isBettingLocked() && !isAutobetActive());
                            });
                        },
                    }),
                ),

                Frame.Section(
                    {
                        variant: 'subtle',
                        className: mergeClasses(showOnMobile, { 'l-flex': true }),
                    },
                    betButton,
                ),

                Frame.Section(
                    {
                        variant: 'subtle',
                        className: { 'u-gap--l': true, 'm-plinko__controls': true },
                    },

                    Stack(
                        { gap: 'm' },
                        Stack(
                            { scroll: true, css: { padding: '4px' } },
                            Stack(
                                {
                                    gap: 'l',
                                },

                                BetAmount({
                                    label: 'Bet Amount',
                                    value: ui.betAmount,
                                    disabled: () => ui.isBettingLocked() || isAutobetActive(),
                                    limits,
                                    step: 0.01,
                                    presets: [0.1, 1.0, 10.0, 100.0],
                                    currency: ui.currency,
                                    conversionFactor: ui.usdRate,
                                    onMultiply: (factor) => {
                                        ui.triggerAction('MultiplyBetAmount', factor);
                                    },
                                }),
                                Label({
                                    variant: 'error',
                                    className: { 'u-hide': true },
                                    ref: (element: HTMLSpanElement) => {
                                        const hide = withNamespace('u-hide');
                                        effect(() => {
                                            element.classList.toggle(hide, ui.isBetAllowed());
                                        });
                                    },
                                    text: memo(() => [
                                        'The maximum bet amount is ',
                                        formatCurrency(ui.maxBetAmount(), ui.currency()) + '.',
                                        html('br')(),
                                        ui.defaultMaxBetAmount() > ui.maxBetAmount() &&
                                        ui.risk.read() > 0
                                            ? 'Lower the Risk to increase maximum bet.'
                                            : ui.number_of_rows.read() > 8
                                              ? 'Lower the number of rows to increase maximum bet.'
                                              : null,
                                    ]),
                                }),
                                Stack(
                                    {
                                        gap: 'm',
                                        className: { 'm-plinko__risk': true },
                                    },
                                    Stack(
                                        { gap: 'xs' },
                                        Label(
                                            { icon: '' },
                                            html('span')(
                                                {
                                                    className: {
                                                        'm-label': true,
                                                    },
                                                },
                                                'Risk',
                                            ),
                                        ),
                                        Flex(
                                            { wrap: false, gap: 'xs', justify: 'space-evenly' },
                                            Button({
                                                label: 'Low',
                                                variant: 'risk--low',
                                                css: { flex: '1', padding: '10px 8px' },
                                                ref: handleRiskButton(0),
                                                onClick: () => ui.triggerAction('SetRisk', 0),
                                            }),
                                            Button({
                                                css: { flex: '1', padding: '10px 8px' },
                                                label: 'Medium',
                                                variant: 'risk--medium',
                                                ref: handleRiskButton(1),
                                                onClick: () => ui.triggerAction('SetRisk', 1),
                                            }),
                                            Button({
                                                css: { flex: '1', padding: '10px 8px' },
                                                label: 'High',
                                                variant: 'risk--high',
                                                ref: handleRiskButton(2),
                                                onClick: () => ui.triggerAction('SetRisk', 2),
                                            }),
                                        ),
                                    ),
                                    Stack(
                                        { gap: 'xs' },
                                        Label(
                                            {
                                                icon: '',
                                            },
                                            html('span')(
                                                {
                                                    className: {
                                                        'm-label': true,
                                                    },
                                                },
                                                'Rows',
                                            ),
                                        ),
                                        html('div')(
                                            {
                                                css: {
                                                    padding: '20px',
                                                },
                                                ref: (element: HTMLButtonElement) => {
                                                    effect(() => {
                                                        element.style.opacity =
                                                            ui.isBoardEditingAllowed()
                                                                ? '1'
                                                                : '0.7';
                                                    });
                                                },
                                            },
                                            html('div')({
                                                css: {
                                                    display: 'flex',
                                                    'align-items': 'center',
                                                    'justify-content': 'space-between',
                                                    width: '100%',
                                                    'font-size': 'clamp(1rem, 1.2vw, 1.2rem)',
                                                    color: 'white',
                                                },
                                                children: [
                                                    html('span')({ textContent: '08' }),
                                                    Slider({
                                                        variant: 'plinko',
                                                        value: memo(() =>
                                                            Number(ui.number_of_rows.read()),
                                                        ),
                                                        min: () => 8,
                                                        max: () => 16,
                                                        step: 1,
                                                        disabled: () => !ui.isBoardEditingAllowed(),
                                                        onChange: (value: number) => {
                                                            void Promise.resolve().then(() => {
                                                                ui.triggerAction(
                                                                    'SetNumberOfRows',
                                                                    value,
                                                                );
                                                            });
                                                        },
                                                    }),
                                                    html('span')({ textContent: '16' }),
                                                ],
                                            }),
                                        ),
                                    ),
                                ),
                            ),

                            Autobet({
                                ui,
                                actionState,
                                ref(element: HTMLDivElement) {
                                    const hide = withNamespace('u-hide');
                                    effect(() => {
                                        element.classList.toggle(
                                            hide,
                                            ui.activeTab.read() !== 'auto',
                                        );
                                    });
                                },
                            }),
                        ),
                        div(
                            { className: mergeClasses(hideOnMobile, { 'l-flex': true }) },
                            betButton,
                        ),
                    ),
                ),

                Frame.Section(
                    { className: { 'l-stack__split': true, 'l-stack': true, 'u-gap--s': true } },
                    Options({ ui, namespace: 'plinko' }),
                    html('button')(
                        {
                            onClick(e) {
                                e.preventDefault();
                                ui.provablyFairModalControlsRef.current?.open();
                            },
                            className: { 'u-text--center': true },
                            css: { 'border-width': '0' },
                        },
                        Label(
                            { variant: 'subtle', icon: 'double-check' },
                            html('span')({ className: namespaced('u-hide@m') }, 'This Game is'),
                            'Provably Fair',
                        ),
                    ),
                ),
            );
        },
);
