import type { Reactive } from '../../state';

/**
 * The available chip denominations.
 */
export const ChipValues = ['1', '10', '100', '1K', '10K', '100K', '1M', '10M', '100M'] as const;

/**
 * The available chip denominations.
 */
export type ChipValue = (typeof ChipValues)[number];

/**
 * The available icon colors.
 */
export type ChipColor =
    | 'red'
    | 'black'
    | 'blue'
    | 'orange'
    | 'violet'
    | 'indigo'
    | 'dark-cyan'
    | 'cyan'
    | 'green';

export interface ChipProps {
    /**
     * The chip denomination value.
     */
    readonly value: ChipValue;

    /**
     * The optional chip text. If not specified, the chip value will be used.
     */
    readonly text?: string | undefined;

    /**
     * The optional chip color. If not specified, a default value for the given denomination will be used.
     */
    readonly color?: ChipColor | undefined;

    /**
     * Whether the chip is selected. Defaults to `false`.
     */
    readonly isSelected?: Reactive<boolean> | undefined;
}

export const defaultColors: Record<ChipValue, ChipColor> = {
    '1': 'red',
    '10': 'black',
    '100': 'blue',
    '1K': 'orange',
    '10K': 'violet',
    '100K': 'indigo',
    '1M': 'dark-cyan',
    '10M': 'cyan',
    '100M': 'green',
};

