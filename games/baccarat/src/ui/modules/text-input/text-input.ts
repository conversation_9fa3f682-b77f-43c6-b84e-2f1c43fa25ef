import {
    Button,
    defineComponent,
    Flex,
    Input,
    Label,
    Stack,
    withNamespace,
    type Component,
    type InputControls,
    type InputProps,
    type LabelControls,
    type RefObject,
    type Signal,
    type SignalReader,
} from '@monkey-tilt/ui';

export interface TextInputProps extends Omit<InputProps, 'value'> {
    /**
     * The signal representing the current bet amount value.
     */
    readonly value: Signal<string>;

    /**
     * The signal reader representing the current error message.
     */
    readonly error?: SignalReader<string>;

    /**
     * The label to be displayed above the input.
     */
    readonly label: string;

    /**
     * Whether to render the copy-to-clipboard button.
     */
    readonly withCopy?: boolean;

    /**
     * Optional button to render to the right of the input.
     */
    readonly button?: Component;
}

export const TextInput = defineComponent(
    'TextInput',
    ({ state }) =>
        ({
            value: valueSignal,
            error,
            label,
            withCopy = false,
            button,
            ...props
        }: TextInputProps): Component<HTMLDivElement> => {
            const { effect } = state();

            const inputWrapper: RefObject<HTMLDivElement> = { current: null };
            const input: RefObject<InputControls> = { current: null };

            const id = props.id ?? `text-input-${Math.random().toString(36).slice(2)}`;

            effect(() => {
                const value = valueSignal.read();
                if (input.current) {
                    input.current.update(value);
                }
            });

            const fallbackCopy = () => {
                if (input.current) {
                    input.current.ref()?.select();
                    document.execCommand('copy');
                }
            };

            const inputComponent = Input({
                ref(element: HTMLDivElement) {
                    inputWrapper.current = element;
                },

                id,
                ...props,

                value: valueSignal.read(),
                onChange(event: InputEvent) {
                    props.onChange?.(event);
                    if (event.target instanceof HTMLInputElement) {
                        valueSignal.update(event.target.value);
                    }
                },

                onFocus(event: InputEvent) {
                    if (event.target instanceof HTMLInputElement) {
                        event.target.select();
                    }
                },

                action: withCopy
                    ? Button({
                          icon: 'copy',
                          variant: 'secondary',
                          onClick: () => {
                              try {
                                  navigator.clipboard
                                      .writeText(valueSignal.read())
                                      .catch(fallbackCopy);
                              } catch {
                                  fallbackCopy();
                              }
                          },
                      })
                    : undefined,

                controls: (controls: InputControls) => {
                    input.current = controls;
                },
            });

            return Stack(
                { gap: 's' },
                Label({ text: label, htmlFor: id }),
                button
                    ? Flex({ gap: 's' }, inputComponent, button.with({ css: { 'flex-grow': '0' } }))
                    : inputComponent,
                Label({
                    variant: 'error',
                    className: { 'u-hide': !error || !error() },
                    controls: ({ setText, ref }: LabelControls) => {
                        if (!error) {
                            return;
                        }

                        effect(() => {
                            const errorText = error();

                            setText(errorText);
                            ref()?.classList.toggle(withNamespace('u-hide'), !errorText);
                        });
                    },
                }),
            );
        },
);
