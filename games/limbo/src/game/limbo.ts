import {
    APIClient,
    createWebSocketTransport,
    currencyToFixedString,
    GameClient,
    GameError,
    isGameUpdate,
    merge,
    pick,
    RetryError,
    tryParseFloat,
    validateObject,
    type AnyState,
    type BetAmountLimits,
    type GameActionName,
    type GameState,
    type MessageName,
    type RequestData,
    type VerifySeedParams,
    type Writable,
} from '@monkey-tilt/client';
import type { Root, SignalReader, SignalUpdater } from '@monkey-tilt/ui';
import { deepMerge, logDevError, RingBuffer } from '@monkey-tilt/utils';
import { multiplierForWinChance } from '../util/chance';
import {
    type AutobetState,
    type FinalState,
    type LimboBetAmountLimits,
    type LimboState,
    type RoundSummary,
} from './state';

export interface LimboClientOptions {
    readonly root: Root;
    readonly gameId: string;
    readonly gatewayUrl: string | URL;
    readonly websocketUrl?: string | URL;
    readonly gameSessionId: string;
    readonly currency: string;
}

interface LimboGameOptions extends LimboClientOptions {
    readonly apiClient: APIClient;
    readonly betAmountLimits: LimboBetAmountLimits;
}

export async function createLimboClient(options: LimboClientOptions): Promise<LimboClient> {
    const apiClient = new APIClient(options.gatewayUrl);
    apiClient.gameId = options.gameId;
    apiClient.gameSessionId = options.gameSessionId;

    return new LimboGame({
        ...options,
        apiClient,
        betAmountLimits: deepMerge<BetAmountLimits>(
            {
                bet: {
                    min: 0.01,
                    max: 25_000,
                    max_payout: 100_000,
                },
            },
            await apiClient.getBetLimits(),
        ) as LimboBetAmountLimits,
    });
}

export interface LimboClient extends GameClient<LimboState> {
    readonly state: SignalReader<LimboState>;
    onStateReload(handler: () => void): void;
    bet(request: RequestData<'Bet'>): Promise<void>;
    autobet(request: RequestData<'Autobet'>): Promise<void>;
    nextbet(): Promise<void>;
    closeAutobet(): Promise<void>;
    setBetState(amounts: Partial<Pick<LimboState, 'bet_amount' | 'multiplier'>>): void;
    setAutobetState(
        autobet: Partial<AutobetState & Pick<LimboState, 'bet_amount' | 'multiplier'>>,
    ): void;
    setTargetMultiplier(multiplier: number | ((multiplier: number) => number)): void;
    reload(): Promise<GameState>;
    verifySeeds(params: VerifySeedParams): Promise<FinalState>;
}

class LimboGame extends GameClient<LimboState> implements LimboClient {
    #getState: SignalReader<LimboState>;
    #setState: SignalUpdater<LimboState>;
    #history: RingBuffer<RoundSummary>;

    #stateReloadHandlers = new Set<() => void>();

    #updateBalance: () => Promise<void>;
    #updateBetLimits: () => Promise<void>;
    #updateMultiplierLimits: () => Promise<void>;

    public constructor({
        root,
        gameId,
        gatewayUrl,
        websocketUrl,
        gameSessionId,
        currency,
        apiClient,
        betAmountLimits,
    }: LimboGameOptions) {
        super({
            root,
            gameId,
            gameSessionId,
            currency,
            gatewayUrl,
            apiClient,
            transport: createWebSocketTransport({
                gameSessionId,
                gatewayUrl,
                websocketUrl,
            }),
            syncableActions: () => false,
            actionTimeout: 2000,
            autoRetryAttempts: 5,
        });

        const { signal, memo, effect } = this.root.store;

        [this.#getState, this.#setState] = signal<LimboState>({
            history: [],
            balance: 0,
            bet_limits: betAmountLimits,
            multiplier_limits: {
                min: 1.01,
                max: 500,
            },
            error: null,
            isLoading: true,
            readyState: 'closed',
            isAuthenticated: false,
            round_id: '',
            on_loss_reset: true,
            on_win_reset: true,
            autobet_limit: 0,
            bet_amount: '0.00',
            multiplier: multiplierForWinChance(50).toFixed(2),
            next_actions: [],
            isAutobetActive: false,
        });

        this.#history = new RingBuffer<RoundSummary>(13);

        this.#updateBalance = this.#createUpdater('balance', () =>
            this.api.getBalance().then(({ balance }) => balance),
        );
        this.#updateMultiplierLimits = this.#createUpdater('multiplier_limits', async () => {
            const limits = await this.api
                .getConfigCustomProps({
                    min_multiplier: 'string?',
                    max_multiplier: 'string?',
                })
                .then((props) => {
                    return {
                        min: tryParseFloat(props.min_multiplier),
                        max: tryParseFloat(props.max_multiplier),
                    } as Partial<LimboState['multiplier_limits']>;
                });
            return (previousLimits) => deepMerge(previousLimits, limits);
        });

        const usdRate = memo(() => this.session().usdRate);
        const usdBetLimits = signal(betAmountLimits);

        this.#updateBetLimits = async () => {
            const limits = await this.api.getBetLimits();
            usdBetLimits.update(
                (prevLimits) =>
                    deepMerge<BetAmountLimits>(prevLimits, limits) as LimboBetAmountLimits,
            );
        };

        effect(() => {
            const limits = usdBetLimits.read();
            const factor = usdRate();
            this.#setState((state) => ({
                ...state,
                bet_limits: {
                    bet: {
                        min: limits.bet.min * factor,
                        max: limits.bet.max * factor,
                        max_payout: limits.bet.max_payout * factor,
                    },
                },
            }));
        });

        const prepareNextRound = () => {
            this.#updateBalance().catch(logDevError);
            this.#updateBetLimits().catch(logDevError);
            this.#updateMultiplierLimits().catch(logDevError);
        };

        prepareNextRound();
        this.on('roundEnd', prepareNextRound);

        this.on('readyStateChanged', (readyState) => {
            this.#setState((state) => ({
                ...state,
                isLoading: !(state.isLoading && readyState === 'ready'),
                readyState,
                isAuthenticated: this.isAuthenticated,
            }));
        });

        this.on('error', (event) => {
            let error: unknown = event.error;

            if (error instanceof RetryError) {
                error = error.reason;
            }

            if (error instanceof GameError) {
                this.#setState((state) => {
                    return {
                        ...state,
                        next_actions:
                            error.code === 'INSUFFICIENT_FUNDS'
                                ? this.roundStartActions
                                : state.next_actions,
                        error,
                    };
                });
            }
        });
    }

    protected updateState(value: LimboState | ((prevValue: LimboState) => LimboState)): void {
        return this.#setState(value);
    }

    public get state(): SignalReader<LimboState> {
        return this.#getState;
    }

    public onStateReload(handler: () => void): void {
        this.#stateReloadHandlers.add(handler);
    }

    public override can<A extends MessageName>(action: A): boolean {
        if (super.can(action)) {
            return true;
        }
        return (
            this.roundStartActions.includes(action as GameActionName) &&
            this.#getState().next_actions.length === 0
        );
    }

    #validateBetAmount(request: RequestData<'Bet'>): { balance: number; betAmount: number } {
        if (!request.multiplier) {
            throw new GameError('Multiplier is required', 'MULTIPLIER_REQUIRED', '');
        }

        const state = this.#getState();

        const betAmount = request.bet_amount ?? 0;
        const balance = state.balance - betAmount;

        if (balance < 0) {
            throw new GameError('Insufficient funds', 'INSUFFICIENT_FUNDS', '');
        }

        const maxBetAmount = Math.min(
            state.bet_limits.bet.max,
            state.bet_limits.bet.max_payout / request.multiplier,
        );

        if (betAmount > maxBetAmount) {
            throw new GameError('Bet amount exceeds maximum limit', 'BET_AMOUNT_EXCEEDS_LIMIT', '');
        }

        return { balance, betAmount };
    }

    public async bet(request: RequestData<'Bet'>): Promise<void> {
        if (this.can('Bet')) {
            const { betAmount, balance } = this.#validateBetAmount(request);
            this.#setState((state) => ({
                ...state,
                bet_amount: currencyToFixedString(betAmount, this.session().currency),
                balance,
            }));
        }

        return this.#send(
            'Bet',
            request,
            (error) => !(error instanceof GameError) || error.code !== 'INSUFFICIENT_FUNDS',
        );
    }

    public async autobet(request: RequestData<'Autobet'>): Promise<void> {
        if (this.can('Autobet')) {
            this.removeIntent('CloseAutobet');

            const { betAmount, balance } = this.#validateBetAmount(request);
            this.#setState((state) => ({
                ...state,
                bet_amount: currencyToFixedString(betAmount, this.session().currency),
                balance,
                isAutobetActive: true,
                autobet_limit:
                    request.autobet_limit != undefined && Number.isFinite(request.autobet_limit)
                        ? request.autobet_limit
                        : 0,
                autobet_count: 0,
                on_win_reset: request.on_win_reset ?? true,
                on_win: (request.on_win ?? 0).toFixed(0),
                on_loss_reset: request.on_loss_reset ?? true,
                on_loss: (request.on_loss ?? 0).toFixed(0),
                stop_on_profit: (request.stop_on_profit ?? 0).toFixed(2),
                stop_on_loss: (request.stop_on_loss ?? 0).toFixed(2),
            }));
        }

        return this.#send(
            'Autobet',
            request,
            (error) => !(error instanceof GameError) || error.code !== 'INSUFFICIENT_FUNDS',
        ).catch(() => {
            this.#setState((state) => ({ ...state, isAutobetActive: false }));
        });
    }

    public nextbet(): Promise<void> {
        if (this.hasIntent('CloseAutobet')) {
            return this.closeAutobet();
        }
        if (this.can('Nextbet')) {
            const { bet_amount = '0.00', balance } = this.#getState();

            if (balance < Number(bet_amount)) {
                this.closeAutobet().catch(logDevError);
                return Promise.reject(
                    new GameError('Insufficient funds', 'INSUFFICIENT_FUNDS', ''),
                );
            }

            this.#setState((state) => ({
                ...state,
                balance,
            }));
        }
        return this.#send('Nextbet');
    }

    public closeAutobet(): Promise<void> {
        this.removeIntent('CloseAutobet');
        return this.#send('CloseAutobet');
    }

    public setTargetMultiplier(multiplier: number | ((multiplier: number) => number)): void {
        this.#setState((state) => {
            if (typeof multiplier === 'function') {
                let prevMultiplier = Number.NaN;
                if (state.multiplier) {
                    prevMultiplier = Number.parseFloat(state.multiplier);
                }
                if (!Number.isFinite(prevMultiplier)) {
                    prevMultiplier = state.multiplier_limits.min;
                }
                multiplier = multiplier(prevMultiplier);
            }
            const newMultiplier = multiplier.toFixed(2);
            if (newMultiplier === state.multiplier) {
                return state;
            }
            return {
                ...state,
                multiplier: newMultiplier,
            };
        });
    }

    public setBetState(amounts: Partial<Pick<LimboState, 'bet_amount' | 'multiplier'>>): void {
        this.#setState((state) => ({
            ...state,
            ...amounts,
        }));
    }

    public setAutobetState(
        autobet: Partial<AutobetState & Pick<LimboState, 'bet_amount' | 'multiplier'>>,
    ): void {
        this.#setState((state) => ({
            ...state,
            ...autobet,
        }));
    }

    public override async reload(): Promise<GameState> {
        this.#setState((state) => ({ ...state, error: null }));
        return super.reload();
    }

    public override get roundStartActions(): GameActionName[] {
        return ['Bet', 'Autobet'];
    }

    public override reset(): void {
        super.reset();

        const { isAuthenticated } = this;

        this.#setState((state) => ({
            ...state,
            error: null,
            next_actions: isAuthenticated ? this.roundStartActions : [],
            isAuthenticated,
        }));
    }

    async #send(
        action: GameActionName,
        request: RequestData<GameActionName>,
        shouldRetry?: (error: Error) => boolean,
    ): Promise<void> {
        this.#setState((state) => ({
            ...state,
            error: null,
            next_actions: [],
        }));
        await this.sendAndAwait(action, request, shouldRetry);
    }

    protected override handleUpdate(state: AnyState): void {
        if (state.type === 'Authenticate') {
            this.reset();
            return;
        }

        if (state.type === 'GameState') {
            const resetState = state.data as Writable<LimboState>;

            if (state.round_id) {
                resetState.round_id = state.round_id;
            }

            resetState.bet_amount = (
                resetState.bet_amount !== undefined ? Number.parseFloat(resetState.bet_amount) : 0
            ).toFixed(2);

            this.allowedActions = new Set(resetState.next_actions);

            this.#setState((currentState) => ({
                ...currentState,
                ...resetState,
                round_id: state.round_id ?? currentState.round_id,
                round_closed: resetState.round_closed == true,
                isAutobetActive: resetState.next_actions.includes('CloseAutobet'),
                isAuthenticated: true,
            }));

            if (resetState.round_closed) {
                this.signalRoundEnd();
            }

            void Promise.resolve().then(() => {
                for (const handler of this.#stateReloadHandlers) {
                    try {
                        handler();
                    } catch {
                        // ignore
                    }
                }
            });

            return;
        }

        if (!isGameUpdate(state)) {
            return;
        }

        const newState = structuredClone(this.#getState()) as Writable<LimboState>;
        let isNewRound = false;

        if (state.round_id) {
            isNewRound = state.round_id !== newState.round_id;
            newState.round_id = state.round_id;
        }

        newState.action = state.action;
        newState.isAuthenticated = this.isAuthenticated;
        newState.next_actions = state.data.next_actions;
        this.allowedActions = new Set(newState.next_actions);

        if (isNewRound) {
            this.#setState({
                ...newState,
                round_closed: false,
            });
            // N.B. due to removal of Open step, we have to split state update
            // into two parts to allow UI to properly clean up the previous round
            void Promise.resolve().then(() => {
                this.handleUpdate(state);
            });
            return;
        }

        merge(
            newState,
            pick(
                state.data,
                'random_multiplier',
                'bet_amount',
                'autobet_count',
                'round_closed',
                'total_payout',
            ),
        );

        if (newState.random_multiplier) {
            newState.random_multiplier = Math.max(1, Number(newState.random_multiplier)).toFixed(2);
        }

        if (
            (state.action === 'Autobet' || state.action === 'Nextbet') &&
            (newState.autobet_limit ?? 0) > 0
        ) {
            (newState.autobet_limit as number)--;
        }

        if (
            ['Bet', 'Autobet', 'Nextbet'].includes(state.action) &&
            newState.bet_amount != undefined &&
            newState.multiplier != undefined &&
            newState.random_multiplier != undefined &&
            newState.total_payout != undefined
        ) {
            const multiplier = Number(newState.multiplier);
            const randomMultiplier = Number(newState.random_multiplier);

            this.#history.add({
                time: new Date(),
                round_id: newState.round_id,
                bet_amount: newState.bet_amount,
                multiplier: multiplier.toFixed(2),
                random_multiplier: randomMultiplier.toFixed(2),
                total_payout: newState.total_payout,
                outcome: randomMultiplier >= multiplier ? 'win' : 'loss',
            });
            newState.history = Array.from(this.#history);
        }

        if (state.data.round_closed) {
            const total_payout = Number.parseFloat(state.data.total_payout ?? '0.00');

            newState.isAutobetActive = newState.next_actions.includes('CloseAutobet');
            newState.balance += total_payout;
        }

        if (newState.round_closed || newState.isAutobetActive) {
            this.signalRoundEnd();
        }

        this.#setState(newState);
    }

    #createUpdater<T extends keyof LimboState>(
        key: T,
        updater: () => Promise<LimboState[T] | ((previousValue: LimboState[T]) => LimboState[T])>,
    ): () => Promise<void> {
        let isFetching = false;
        return async () => {
            if (isFetching) {
                return;
            }

            isFetching = true;
            try {
                const value = await updater();
                this.#setState((state) => ({
                    ...state,
                    [key]: typeof value === 'function' ? value(state[key]) : value,
                }));
            } catch (error) {
                logDevError(`Failed to fetch ${key}:`, error);
            } finally {
                isFetching = false;
            }
        };
    }

    protected validateSeedVerificationData(data: unknown): boolean {
        return (
            validateObject<FinalState>(data, {
                previous_bet_outcome: 'string',
                random_multiplier: 'string',
            }) && ['win', 'loss'].includes(data.previous_bet_outcome)
        );
    }

    public async verifySeeds(params: VerifySeedParams): Promise<FinalState> {
        return (await this._validateSeeds(params)) as FinalState;
    }
}
