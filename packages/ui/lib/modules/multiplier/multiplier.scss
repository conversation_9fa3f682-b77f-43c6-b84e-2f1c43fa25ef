@use '../../styles/common/bem';
@use '../../styles/common/prop';
@use '../../styles/common/respond';
@use '../../styles/common/type-style' as *;
@use '../../styles/common/util';
@use '../../styles/config/metrics';

$-border-size: util.to-rem(1px);
$-gradient: linear-gradient(135.44deg, rgba(255, 255, 255, 0.12) 0%, rgba(255, 255, 255, 0) 100%);
$-padding-vertical: metrics.fluid-size(18, 24);
$-padding-horizontal: metrics.fluid-size(40, 46);
$-radius: util.to-rem(26px);

$-keno-radius: util.to-rem(12px);

@mixin styles {
    @include bem.module(multiplier) {
        display: inline-flex;
        flex-direction: row;
        align-items: center;
        justify-content: center;
        padding: $-padding-vertical $-padding-horizontal;
        padding-inline-end: calc(#{$-padding-horizontal} * 0.75);
        border-radius: $-radius;
        border: $-border-size solid prop.get(color-zinc-800);
        background: $-gradient;
        color: prop.get(color-white);
        backdrop-filter: blur(40px);
        min-inline-size: calc(5ch + #{$-padding-horizontal} * 2);

        @include type-style(multiplier);

        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        transition: color 0.3s ease;

        &--green {
            color: prop.get(color-green-500);
        }

        &--red {
            color: prop.get(color-red-500);
        }

        &--keno {
            @include type-style('title', $with: (font-weight: 500));
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0ch;

            flex: 1 1 0%;
            max-inline-size: 100%;
            min-inline-size: fit-content;

            padding-block: util.to-rem(16px);
            padding-inline: 0;
            margin-inline: util.to-rem(2px);

            @include type-style('body');
            font-size: util.to-rem(16px);
            line-height: 1;
            text-transform: none;
            border-radius: $-keno-radius;

            background: prop.get(color-zinc-900);
            border: util.to-rem(1px) solid prop.get(color-zinc-700);
            color: prop.get(color-white);
            white-space: nowrap;

            &::after {
                display: none !important;
            }

            @include respond.until(1200px) {
                max-inline-size: none;

                font-size: util.to-rem(13px);
                padding-block: util.to-rem(10px);

                border-radius: util.to-rem(6px);
                gap: 0;
            }
            @include respond.until(980px) {
                max-inline-size: none;

                font-size: util.to-rem(8px);
                padding-block: util.to-rem(6px);

                border-radius: util.to-rem(4px);
            }
        }

        &::after {
            content: 'x';
            position: relative;
            font-size: 80%;
            margin-inline-start: 0.2ch;
            transform: scale(1, 0.65);
        }
    }
}
