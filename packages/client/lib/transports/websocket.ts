import { logDevError } from '@monkey-tilt/utils';
import type { ActionLike, StateLike } from '../messages';
import { buildWebSocketUrl, runWithRetry, uuid, type WebSocketUrlInfo } from '../utils';
import type { ReadyState, Transport } from './';

export interface WebSocketTransportOptions {
    readonly supportedActions?: ReadonlyArray<string> | ((message: ActionLike) => boolean);
    readonly connectImmediately?: boolean;
    readonly autoReconnect?: boolean;
    readonly maxReconnectAttempts?: number;
    readonly connectTimeout?: number;
}

export function createWebSocketTransport(info: WebSocketUrlInfo): WebSocketTransport {
    return new WebSocketTransport(buildWebSocketUrl(info));
}

const PING_INTERVAL_MS = 10_000; // 10 seconds

/**
 * A transport for sending and receiving messages over a WebSocket.
 */
export class WebSocketTransport implements Transport {
    #websocket: WebSocket | null = null;
    #url: URL;
    #state: ReadyState = 'closed';

    #autoReconnect: boolean;
    #maxReconnectAttempts: number;
    #connectTimeout: number;

    #onReadyStateChangedHandlers: Set<(readyState: ReadyState) => void> = new Set();
    #onMessageHandlers: Set<(message: StateLike) => void> = new Set();
    #canSend: (message: ActionLike) => boolean;

    #isConnected = globalThis.navigator?.onLine ?? false;
    #isClosed = true;
    #pingTimer: ReturnType<typeof setTimeout> | null = null;

    /**
     * @param url - The WebSocket URL to connect to.
     * @param supportedActions - The actions that this transport supports.
     *                           If not provided, all actions are supported.
     */
    public constructor(
        url: string | URL,
        {
            supportedActions,
            connectImmediately = true,
            autoReconnect = true,
            maxReconnectAttempts = 6,
            connectTimeout = 3000,
        }: WebSocketTransportOptions = {},
    ) {
        this.#canSend =
            typeof supportedActions === 'function'
                ? supportedActions
                : Array.isArray(supportedActions)
                  ? (message) => supportedActions.includes(message.action)
                  : () => true;

        this.#url = url instanceof URL ? url : new URL(url, globalThis?.location?.origin);
        this.#autoReconnect = autoReconnect;
        this.#maxReconnectAttempts = maxReconnectAttempts;
        this.#connectTimeout = connectTimeout;

        if (globalThis.window) {
            globalThis.window.addEventListener('online', () => {
                this.#isConnected = true;
                setTimeout(() => {
                    this.connect().catch(logDevError);
                }, 300);
            });
            globalThis.window.addEventListener('offline', () => {
                this.#isConnected = false;
                this.#setReadyState('closed');
                if (this.#websocket) {
                    this.#websocket.close();
                }
            });
        }

        if (connectImmediately) {
            this.connect().catch(logDevError);
        }
    }

    public get readyState(): ReadyState {
        return this.#state;
    }

    #setReadyState(readyState: ReadyState) {
        if (this.#state === readyState) {
            return;
        }
        if (readyState === 'ready') {
            this.#schedulePing();
        } else if (readyState === 'closed') {
            this.#cancelPing();
        }
        this.#state = readyState;
        for (const handler of this.#onReadyStateChangedHandlers) {
            handler(readyState);
        }
    }

    public async switchGameSession(gameSessionId: string): Promise<void> {
        const url = new URL(this.#url);
        url.searchParams.set('game_session', gameSessionId);

        this.close();

        this.#url = url;
        return this.connect();
    }

    #cancelPing() {
        if (this.#pingTimer) {
            clearTimeout(this.#pingTimer);
            this.#pingTimer = null;
        }
    }

    #schedulePing() {
        this.#cancelPing();
        this.#pingTimer = setTimeout(() => this.#ping(), PING_INTERVAL_MS);
    }

    #ping() {
        this.#schedulePing();

        if (this.#websocket && this.#websocket.readyState === WebSocket.OPEN) {
            this.#websocket.send(JSON.stringify({ type: 'Ping', action_id: uuid() }));
        }
    }

    connect(): Promise<void> {
        if (!this.#isConnected) {
            throw new Error('No network connection');
        }
        return runWithRetry(
            {
                maxAttempts: this.#autoReconnect ? this.#maxReconnectAttempts : 1,
            },
            () =>
                new Promise<void>((resolve, reject) => {
                    this.#setReadyState('connecting');
                    this.#websocket = new WebSocket(this.#url);

                    const abortController = new AbortController();
                    const { signal } = abortController;

                    let timeout =
                        this.#connectTimeout > 0
                            ? setTimeout(() => {
                                  abortController.abort();
                                  reject(new Error('Connection attempt timed out'));
                              }, this.#connectTimeout)
                            : null;

                    const cleanup = () => {
                        if (timeout) {
                            clearTimeout(timeout);
                            timeout = null;
                        }
                        abortController.abort();
                    };

                    this.#websocket.addEventListener(
                        'open',
                        () => {
                            cleanup();
                            resolve();
                        },
                        { signal },
                    );

                    this.#websocket.addEventListener(
                        'error',
                        (event) => {
                            cleanup();
                            reject(
                                event && event instanceof Error
                                    ? event
                                    : new Error(String('data' in event ? event.data : event)),
                            );
                        },
                        { signal },
                    );

                    this.#websocket.addEventListener(
                        'close',
                        () => {
                            cleanup();
                            reject(new Error('WebSocket connection closed'));
                        },
                        { signal },
                    );
                }),
        ).then(
            () => {
                this.#websocket!.addEventListener('close', () => {
                    this.#setReadyState('closed');
                    if (!this.#isClosed && this.#autoReconnect) {
                        this.#isConnected = globalThis.navigator?.onLine ?? false;
                        this.connect().catch((error) => {
                            logDevError('Failed to reconnect', error);
                            this.#setReadyState('disconnected');
                        });
                    }
                });

                this.#websocket!.addEventListener('message', (event: MessageEvent<string>) => {
                    try {
                        // TODO: Validate received message format
                        const message = JSON.parse(event.data) as StateLike;
                        if (message && typeof message === 'object' && message.type === 'Pong') {
                            return;
                        }
                        try {
                            for (const handler of this.#onMessageHandlers) {
                                handler(message);
                            }
                        } catch {
                            // Ignore (handlers should handle their own errors)
                        }
                    } catch (error) {
                        logDevError('Failed to parse message', event.data, error);
                    }
                });

                this.#websocket!.addEventListener('error', (event) => {
                    this.#setReadyState('closed');
                    logDevError('WebSocket error', event);
                });

                this.#isClosed = false;
                this.#setReadyState('ready');
            },
            (error) => {
                logDevError('Failed to connect', error);
                this.#setReadyState('closed');
            },
        );
    }

    public get isReady() {
        return this.#state === 'ready';
    }

    public onMessage(handler: (message: StateLike) => void) {
        this.#onMessageHandlers.add(handler);
    }

    public onReadyStateChanged(handler: (readyState: ReadyState) => void) {
        this.#onReadyStateChangedHandlers.add(handler);
        handler(this.#state);
    }

    public canSend(message: ActionLike) {
        return this.isReady && this.#canSend(message);
    }

    public send(message: ActionLike) {
        if (this.#isClosed || !this.#websocket || !this.isReady) {
            return false;
        }

        this.#schedulePing();

        let data: string;

        try {
            data = JSON.stringify(message);
        } catch (error) {
            logDevError('Failed to serialize message', message, error);
            return false;
        }

        this.#websocket.send(data);
        return true;
    }

    public close() {
        this.#isClosed = true;
        if (this.#websocket) {
            this.#setReadyState('closed');
            this.#websocket.close();
        }
    }
}
