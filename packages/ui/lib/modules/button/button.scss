@use 'sass:map';
@use '../../styles/common/bem';
@use '../../styles/common/font-weight';
@use '../../styles/common/gradient-border' as *;
@use '../../styles/common/prop';
@use '../../styles/common/type-style' as *;
@use '../../styles/common/util';

$-transition-duration: 0.3s;
$-m-icon: bem.block-selector(module, icon);
$-m-button: bem.block-selector(module, button);

$-classic-risk--bg: #60a5fa1a;
$-low-risk--bg: #fcd34d0d;
$-medium-risk--bg: #f59e0b0d;
$-high-risk--bg: #ef44441a;

$-risk-colors: (
    'classic': (
        'border-color': prop.get(color-blue-400),
        'text-color': prop.get(color-blue-400),
        'background': #60a5fa1a,
        'background-hover': #60a5fa33,
        'background-active': prop.get(color-blue-400),
        'active-border': prop.get(color-blue-500),
    ),
    'low': (
        'border-color': prop.get(color-mt-400),
        'text-color': prop.get(color-mt-400),
        'background': #fcd34d0d,
        'background-hover': #fcd34d33,
        'background-active': prop.get(color-mt-400),
        'active-border': #ca9b00,
    ),
    'medium': (
        'border-color': prop.get(color-orange-500),
        'text-color': prop.get(color-orange-500),
        'background': #f59e0b0d,
        'background-hover': #f59e0b33,
        'background-active': prop.get(color-orange-500),
        'active-border': #b2740b,
    ),
    'high': (
        'border-color': prop.get(color-red-500),
        'text-color': prop.get(color-red-500),
        'background': #ef44441a,
        'background-hover': #ef444433,
        'background-active': prop.get(color-red-500),
        'active-border': #a91818,
    ),
);

@mixin styles {
    #{$-m-button} {
        cursor: pointer;
        display: inline-flex;
        justify-content: space-between;
        align-items: center;
        flex: 1 0 0;
        gap: prop.get(button-gap, util.to-rem(10px));
        min-inline-size: prop.get(button-min-size, none);
        padding: prop.get(button-padding, util.to-rem(10px) util.to-rem(12px));
        border: prop.get(
            button-border,
            prop.get(button-border-size, 1px) solid
                prop.get(button-border-color, prop.get(color-zinc-800))
        );
        border-radius: prop.get(button-border-radius, util.to-rem(10px));
        color: prop.get(button-color, prop.get(color-white));
        background: prop.get(button-fill, prop.get(color-zinc-900));
        box-shadow: prop.get(button-shadow, none);
        white-space: nowrap;
        transition:
            background $-transition-duration,
            color $-transition-duration;

        @include type-style(c1);
        @include prop.set(
            (
                icon-default-color: prop.get(color-zinc-300),
                icon-size: prop.get(size-m),
                icon-display: block,
            )
        );

        &:hover {
            @include prop.set(
                (
                    button-fill: prop.get(button-fill-hover, prop.get(color-zinc-800)),
                    icon-default-color: prop.get(color-white),
                )
            );
        }

        &:active {
            @include prop.set(
                (
                    button-fill: prop.get(button-fill-active, prop.get(color-zinc-900)),
                )
            );
        }

        &:disabled {
            @include prop.set(
                (
                    button-fill: prop.get(button-fill-disabled, prop.get(color-zinc-850)),
                    button-color: prop.get(button-color-disabled, prop.get(color-zinc-400)),
                    button-border-color: prop.get(
                            button-border-color-disabled,
                            prop.get(color-zinc-800)
                        ),
                    border-gradient: none,
                )
            );

            opacity: 0.75;

            #{$-m-icon} {
                opacity: 0.5;
            }
        }

        &#{bem.namespaced-selector(is-active)} {
            @include prop.set(button-shadow, 0 0 util.to-rem(2px) prop.get(color-mt-400));
        }

        #{$-m-icon} {
            display: prop.get(icon-display);

            &:only-child {
                transform: scale(0.9);
            }

            &::after {
                transition: background $-transition-duration;
            }
        }

        &:has(#{$-m-icon}:not(:only-child)) {
            @include prop.set(button-min-size, util.to-rem(100px));
        }

        &:has(#{$-m-icon}:only-child) {
            flex-grow: 0;
            aspect-ratio: 1;
            min-inline-size: auto;
            justify-content: center;

            @include prop.set(
                (
                    button-padding: util.to-rem(4px),
                    icon-size: prop.get(size-l),
                ),
                $important: true
            );
        }

        &--secondary,
        &--tertiary {
            justify-content: center;

            @include prop.set(
                (
                    button-min-size: util.to-rem(40px),
                    button-gradient-angle: 135deg,
                    button-gradient: linear-gradient(
                            prop.get(button-gradient-angle),
                            #373737 0%,
                            #171717 100%
                        ),
                    button-fill: prop.get(button-gradient),
                    button-fill-hover: prop.get(button-gradient),
                    button-fill-active: prop.get(button-gradient),
                    button-padding: prop.get(size-2xs),
                )
            );

            &:hover {
                @include prop.set(button-gradient-angle, 180deg);
            }

            &:active {
                @include prop.set(button-gradient-angle, 0deg);
            }
        }

        &--secondary {
            min-inline-size: min-content;
            @include font-weight.set(semi-bold);

            @include gradient-border(
                prop.get(button-border-size, 1px),
                linear-gradient(135deg, #3c3515 0%, #272729 100%)
            );
        }

        &--tertiary {
            @include prop.set(
                (
                    button-border-color: prop.get(color-mt-900),
                    button-gradient: linear-gradient(
                            prop.get(button-gradient-angle),
                            #423b16 0%,
                            #171717 100%
                        ),
                )
            );

            @include font-weight.set(bold);
        }

        &--cta,
        &--accent {
            justify-content: center;

            @include type-style(cta);

            @include prop.set(
                (
                    button-shadow: (
                        util.to-rem(4px) util.to-rem(-4px) util.to-rem(14px) 0 #f79f00 inset,
                        0 util.to-rem(2px) util.to-rem(2px) 0 #000,
                        0 0 util.to-rem(16px) 0 rgba(255, 165, 2, 0.32),
                    ),
                    button-padding: prop.get(size-m) prop.get(size-l),
                    button-color: prop.get(color-black),
                    icon-default-color: currentColor,
                    icon-display: none,
                    button-gradient-angle: 135deg,
                    button-gradient: linear-gradient(
                            prop.get(button-gradient-angle),
                            rgba(254, 187, 64, 0.6) 2.36%,
                            transparent 67.33%,
                            prop.get(color-mt-500) 100%
                        )
                        prop.get(color-mt-400),
                    button-fill: prop.get(button-gradient),
                    button-fill-hover: prop.get(button-gradient),
                    button-color-hover: prop.get(button-color),
                    button-fill-active: prop.get(button-gradient),
                )
            );

            @include gradient-border(
                prop.get(button-border-size, 1px),
                linear-gradient(135deg, #fcc54f 0%, #fdbc4c 100%)
            );

            &:hover {
                @include prop.set(
                    (
                        icon-default-color: currentColor,
                        icon-display: block,
                    )
                );
            }

            &:disabled {
                box-shadow: none;
            }

            &:has(#{$-m-icon}) {
                flex-direction: row-reverse;
                gap: prop.get(size-3xs);
            }

            &:is(&#{$-m-button}--secondary, &#{$-m-button}--tertiary) {
                letter-spacing: normal;
                @include font-weight.set(medium);
            }

            &#{$-m-button}--secondary {
                @include prop.set(button-gradient-angle, 135deg, $important: true);
            }

            &#{$-m-button}--tertiary {
                border: prop.get(
                    button-border,
                    prop.get(button-border-size, 1px) solid
                        prop.get(button-border-color, prop.get(color-zinc-700))
                );

                @include prop.set(
                    (
                        button-shadow: none,
                        button-color: prop.get(color-white),
                        button-border-color: prop.get(color-zinc-700),
                        button-gradient-angle: 180deg,
                        button-gradient: linear-gradient(
                                prop.get(button-gradient-angle),
                                #202022 0%,
                                #151517 100%
                            ),
                    )
                );

                &::before {
                    display: none;
                }

                &:hover {
                    @include prop.set(button-gradient-angle, 0deg);
                }

                &:active {
                    @include prop.set(button-gradient-angle, 180deg);
                }

                &:disabled {
                    color: prop.get(color-zinc-400);
                }
            }
        }

        &--accent {
            border-radius: util.to-rem(8px);

            @include type-style(
                c2,
                $with: (
                    font-size: (
                        prop.get(fs-1),
                        prop.get(lh-1),
                    ),
                    text-transform: none
                )
            );
            @include prop.set(button-padding, util.to-rem(8.042px) util.to-rem(12px));
        }

        &--risk {
            @each $level, $level-styles in $-risk-colors {
                &--#{$level} {
                    border: 1px solid map.get($level-styles, 'border-color');
                    color: map.get($level-styles, 'text-color');
                    background: map.get($level-styles, 'background');
                    font-weight: 500;
                    justify-content: center;

                    &:hover {
                        background: map.get($level-styles, 'background-hover');
                        opacity: 0.9;
                    }

                    &:active {
                        background: map.get($level-styles, 'background-active');
                        color: prop.get(color-black);
                        border: 1px solid map.get($level-styles, 'active-border');
                        box-shadow: 0 2px 0 0 map.get($level-styles, 'active-border');
                    }

                    &.pressed {
                        background: map.get($level-styles, 'background-active');
                        color: prop.get(color-black);
                    }

                    &:disabled {
                        opacity: 0.75;
                        border: 1px solid map.get($level-styles, 'border-color');
                    }
                }
            }
        }

        &--glass {
            position: relative;
            overflow: hidden;

            @include prop.set(
                (
                    button-border-color: rgba(255, 255, 255, 0.12),
                    button-border-size: 1px,
                    button-fill: prop.get(color-zinc-900),
                )
            );

            font-weight: 500;
            text-transform: uppercase;

            transition:
                background-color 0.4s ease,
                border-color 0.4s ease;

            &::before {
                content: '';
                position: absolute;
                inset: 0;
                background: radial-gradient(
                    ellipse at center,
                    rgba(255, 255, 255, 0.08) 0%,
                    transparent 80%
                );
                pointer-events: none;
                z-index: 0;
            }

            &:hover,
            &:active {
                background-color: rgba(21, 21, 21, 0.08);
            }
        }
    }
}
