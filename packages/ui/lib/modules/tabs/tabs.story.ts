import type { Meta, StoryObj } from '@storybook/html';
import { fn } from '@storybook/test';
import { html } from '../../component';
import { namespaced } from '../../utilities';
import { Tabs, type TabsProps } from './tabs';

const meta: Meta = {
    title: 'Modules/Tabs',
};
export default meta;

export const Default: StoryObj = (props: TabsProps) => {
    return html('div')(
        {
            className: namespaced('l-box', 'u-gap--m'),
            css: {
                background: '#09090B',
            },
        },
        Tabs({
            ...props,
            tabs: [
                { value: 'tab1', label: 'Tab 1' },
                { value: 'tab2', label: 'Tab 2' },
                { value: 'tab3', label: 'Tab 3' },
            ],
            css: {
                'max-inline-size': '400px',
            },
        }),
    ).render();
};
Default.argTypes = {
    variant: { control: 'select', options: ['default', 'subtle'] },
    disabled: { control: 'boolean' },
};
Default.args = {
    onChange: fn(),
    disabled: false,
    variant: 'default',
};
