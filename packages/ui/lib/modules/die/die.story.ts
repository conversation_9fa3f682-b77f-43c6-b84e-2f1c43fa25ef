import type { <PERSON><PERSON>, StoryObj } from '@storybook/html';
import { Die, type DieProps } from './die';

const meta: Meta<DieProps> = {
    title: 'Modules/Die',
    argTypes: {
        number: { control: 'text' },
        status: { control: 'radio', options: ['win', 'loss', 'neutral'] },
    },
};
export default meta;

export const Default: StoryObj<DieProps> = {
    render: (props) => Die(props).render(),
    args: {
        number: '24.00',
        status: 'win',
    },
};
