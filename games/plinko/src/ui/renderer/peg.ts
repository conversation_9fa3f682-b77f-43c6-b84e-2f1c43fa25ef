import type { Collidable, CollisionInfo } from '../../physics/bodies/body';
import { Peg as PegBody } from '../../physics/bodies/peg';
import { PEG_RADIUS } from '../../physics/constants';
import { Ball } from './ball';
import type { Particle } from './types';
import { KeyframeAnimation } from './utils/animation';
import { ease } from './utils/easing';
import { lerp } from './utils/lerp';

export class Peg extends PegBody {
    #onBallHit: () => void;

    public constructor(
        column: number,
        row: number,
        radius: number,
        cellSize: number,
        onBallHit: () => void,
    ) {
        super(column, row, radius, cellSize);
        this.#onBallHit = onBallHit;
    }

    public override checkForCollision(body: Collidable): CollisionInfo | null {
        const info = super.checkForCollision(body);

        if (info !== null && body instanceof Ball) {
            this.#onBallHit();
        }

        return info;
    }
}

export class PegHitFeedback extends KeyframeAnimation<'opacity' | 'scale'> implements Particle {
    #x: number;
    #y: number;

    #prevProps!: Record<'opacity' | 'scale', number>;

    public constructor(x: number, y: number, duration = 0.3) {
        super({
            duration,
            easing: ease,
            props: {
                opacity: [1, 0],
                scale: [1, 2.5],
            },
        });

        this.#x = x;
        this.#y = y;
    }

    public get isAlive(): boolean {
        return this.progress < 1;
    }

    public update(dt: number): void {
        this.#prevProps = structuredClone(this.props);
        super.update(dt);
    }

    public render(context: CanvasRenderingContext2D, alpha: number): void {
        const { opacity: prevOpacity, scale: prevScale } = this.#prevProps;
        const { opacity, scale } = this.props;

        context.fillStyle = `rgba(228, 228, 231, ${lerp(prevOpacity, opacity, alpha)})`;
        context.beginPath();
        context.arc(this.#x, this.#y, PEG_RADIUS * lerp(prevScale, scale, alpha), 0, Math.PI * 2);
        context.fill();
        context.closePath();
    }
}
