import { wait } from '@monkey-tilt/client';
import {
    defineComponent,
    forwardRef,
    html,
    Label,
    mergeClasses,
    withNamespace,
    type Component,
    type Props,
    type SignalReader,
} from '@monkey-tilt/ui';

export interface LoaderProps extends Props<HTMLDivElement> {
    readonly text?: string | ReadonlyArray<{ readonly text: string; readonly delay: number }>;
    readonly isLoading: SignalReader<boolean>;
}

export const Loader = defineComponent(
    'Loader',
    ({ state }) =>
        ({ text = 'Loading...', isLoading, ...props }: LoaderProps): Component<HTMLDivElement> => {
            const div = html('div');

            const { effect } = state();

            if (typeof text === 'string') {
                text = [{ text, delay: 0 }];
            }

            return div(
                {
                    ...props,
                    className: mergeClasses(props.className, { 'm-loader': true }),
                    ref: forwardRef(props.ref, (element: HTMLDivElement) => {
                        const isDone = !isLoading();
                        if (!isDone && text.length > 0 && text[0]!.delay > 0) {
                            const isHidden = withNamespace('m-loader--hidden');

                            element.classList.add(isHidden);

                            if (!isDone) {
                                setTimeout(() => {
                                    element.classList.remove(isHidden);
                                }, text[0]!.delay);
                            }
                        }
                    }),
                },
                div({ className: { 'm-loader__image': true } }),
                Label({
                    className: { 'm-loader__text': true },
                    variant: 'subtle',
                    ref: (element: HTMLSpanElement) => {
                        const setText = (text: string) => {
                            return () => {
                                element.textContent = text;
                            };
                        };

                        let timer = wait(text[0]!.delay, setText(text[0]!.text));

                        for (const { text: newText, delay } of text.slice(1)) {
                            timer = timer.wait(delay)(setText(newText));
                        }

                        effect(() => {
                            if (!isLoading()) {
                                timer.cancel();
                            }
                        });
                    },
                }),
            );
        },
);
