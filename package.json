{"name": "@monkey-tilt/original-games-frontend", "version": "0.0.0-dev", "description": "Monkey Tilt Original Games frontend project", "private": true, "type": "module", "license": "UNLICENSED", "author": "Monkey Tilt", "contributors": ["Alek<PERSON><PERSON> <<EMAIL>>"], "scripts": {"build": "nx run-many --target=build --all", "lint": "nx run-many --target=lint --all", "test": "nx run-many --target=test --all", "typecheck": "nx run-many --target=typecheck --all"}, "devDependencies": {"nx": "catalog:", "prettier": "catalog:", "typescript": "catalog:typescript", "typescript-eslint": "catalog:eslint"}, "packageManager": "pnpm@9.15.9+sha512.68046141893c66fad01c079231128e9afb89ef87e2691d69e4d40eee228988295fd4682181bae55b58418c3a253bde65a505ec7c5f9403ece5cc3cd37dcf2531", "pnpm": {"patchedDependencies": {"vite": "patches/vite.patch"}}}