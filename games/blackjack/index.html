<!doctype html>
<html lang="en">

<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Monkey Tilt Blackjack</title>
    <style>
        html,
        body {
            margin: 0;
            height: 100%;
        }
    </style>
</head>

<body>
    <div id="app"></div>
    <script type="module">
        import '@monkey-tilt/ui/style.scss';
        import { WebSocketTransport } from '@monkey-tilt/client';
        import { run } from './src/main.ts';
        import { MockTransport } from './src/game/mock.ts';

        let context;

        globalThis.runGame = () => {
            if (context) {
                console.log('Unmounting previous game');
                context.unmount();
            }

            run({
                container: '#app',
                gameId: 'blackjack-001',
                gatewayUrl: `${window.location.origin}/-/og-dev/v1`,
                currency: new URL(window.location.href).searchParams.get('currency') ?? 'USD',
                gameSessionId:
                    new URL(window.location.href).searchParams.get('game_session') ??
                    '933672fb-c28b-4fa0-b26d-3ad8912b87ec',
            }).then((gameContext) => {
                console.log('Game mounted', gameContext);
                context = gameContext;
            });
        };

        runGame();
    </script>
</body>

</html>