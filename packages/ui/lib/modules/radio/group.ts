import { equal } from '@monkey-tilt/utils';
import { defineComponent, type Component, type Props, type Renderable } from '../../component';
import { Flex, type FlexProps } from '../../layouts';
import type { Reactive } from '../../state';
import { Radio } from './radio';

export interface RadioGroupItem<T> {
    readonly value: T;
    readonly label: Renderable;
}

export interface RadioGroupProps<T>
    extends Omit<Props<HTMLInputElement>, 'value' | 'disabled' | 'onChange'>,
        Omit<FlexProps, 'ref' | 'onChange'> {
    /**
     * Items to display in the radio group.
     */
    readonly items: RadioGroupItem<T>[];

    /**
     * The value of the selected radio button.
     */
    readonly value?: Reactive<T>;

    /**
     * Callback to call when the selected radio button changes.
     */
    readonly onChange?: (value: T) => void;

    /**
     * Whether the radio group is disabled.
     */
    readonly disabled?: Reactive<boolean>;
}

export const RadioGroup = defineComponent(
    'Radio.Group',
    ({ state }) =>
        <T>({
            items,
            value,
            onChange,
            name: groupName,
            disabled = false,
            gap = 'l',
            threshold,
            reverse,
            wrap,
            justify,
            ...props
        }: RadioGroupProps<T>): Component<HTMLDivElement> => {
            const { onPropChange, memo, untracked } = state();

            const radios: HTMLInputElement[] = [];

            onPropChange(disabled, (disabled) => {
                for (const radio of radios) {
                    radio.disabled = disabled;
                }
            });

            const handleChange = (itemValue: T) => {
                if (onChange) {
                    onChange(itemValue);
                }
            };

            const name = groupName || `radio-group-${Math.random().toString(36).slice(2)}`;
            const isInitiallyDisabled =
                disabled instanceof Function ? untracked(disabled) : disabled === true;

            return Flex(
                { gap, threshold, reverse, wrap, justify },
                ...items.map(({ value: itemValue, label }) => {
                    const isChecked = memo(() => {
                        if (value == null) {
                            return false;
                        }
                        return equal(value instanceof Function ? value() : value, itemValue);
                    });
                    return Radio({
                        ...props,
                        inputRef: (element: HTMLInputElement) => {
                            radios.push(element);
                            element.disabled = isInitiallyDisabled;
                        },
                        name,
                        label,
                        isChecked,
                        onChange: () => handleChange(itemValue),
                    });
                }),
            );
        },
) as <T>(props: RadioGroupProps<T>) => Component<HTMLDivElement>;
