import { create, defineComponent, forwardRef, html, type Props } from '../../component';
import { mergeClasses, withNamespace } from '../../utilities/classes';
import {
    ChipProps as BaseChipProps,
    ChipValues,
    defaultColors,
    type ChipColor,
    type ChipValue,
} from './props';
import { ChipStack, type ChipStackProps } from './stack';

import chipSVG from './assets/chip.svg?raw';
import { ChipSelect, type ChipSelectProps } from './select';

export { ChipValues };
export type { ChipColor, ChipSelectProps, ChipStackProps, ChipValue };

export interface ChipProps extends BaseChipProps, Props<HTMLDivElement> {}

const isSelectedClassName = withNamespace('is-selected');
const linearGradientId = withNamespace('chip-lg');

const chip = defineComponent(
    'Chip',
    ({ state }) =>
        ({
            value,
            text = value,
            color = defaultColors[value],
            isSelected = false,
            ...props
        }: ChipProps): HTMLElement => {
            const { onPropChange } = state();

            return create(
                'div',
                {
                    props: {
                        role: 'img',
                        ariaLabel: `Chip with denomination ${text}`,
                        ...props,
                        className: mergeClasses(props.className, {
                            'm-chip': true,
                            [`m-chip--${color}`]: true,
                        }),
                        innerHTML: chipSVG.replace(
                            new RegExp(linearGradientId, 'g'),
                            `${linearGradientId}-${color}`,
                        ),
                        ref: forwardRef(props.ref, (element: HTMLDivElement) => {
                            onPropChange(isSelected, (isSelected) => {
                                element.classList.toggle(isSelectedClassName, isSelected);
                            });
                        }),
                    },
                },
                html('div')({
                    className: { 'm-chip__value': true },
                    textContent: text,
                }),
            );
        },
);

export const Chip: typeof chip & {
    readonly Stack: typeof ChipStack;
    readonly Select: typeof ChipSelect;
} = Object.assign(chip, {
    Stack: ChipStack,
    Select: ChipSelect,
});
