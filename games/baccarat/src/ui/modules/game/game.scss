@use 'sass:math';
@use '@monkey-tilt/ui/scss/common/bem';
@use '@monkey-tilt/ui/scss/common/prop';
@use '@monkey-tilt/ui/scss/common/respond';
@use '@monkey-tilt/ui/scss/common/util';
@use '@monkey-tilt/ui/scss/config/metrics';
@use '../card/card';

@mixin styles {
    @include bem.module('baccarat') {
        display: grid;
        grid-template-columns: metrics.fluid-size(308, 340) 1fr;
        grid-template-rows: 1fr util.to-rem(42px);
        gap: metrics.fluid-size(10, 12);
        padding: metrics.fluid-size(16, 0);
        background: prop.get(color-zinc-950);

        &-container {
            container: baccarat / inline-size;

            @include prop.set(measure, 100%);
        }

        &__sidebar {
            grid-column: 1;
            grid-row: 1 / span 2;
            max-block-size: 100vh;
            overflow-y: auto;

            @include bem.module('frame__section') {
                @include prop.set(frame-padding, util.to-rem(20px));
            }

            & > :last-child {
                @include prop.set(frame-padding, util.to-rem(12px));
            }
        }

        &__sidebar &__options,
        &__sidebar > &__bet {
            display: none;
        }

        &__actions {
            display: grid;
            gap: prop.get(size-xs);
            grid-template-columns: 1fr 1fr;

            @include respond.from(m) {
                > :nth-child(2n + 1):last-child {
                    grid-column: span 2;
                }
            }

            @include respond.until(m) {
                $u-hide: bem.block-selector(utility, 'hide\\@m');

                > :nth-child(2n + 1 of :not(#{$u-hide})):nth-last-child(1 of :not(#{$u-hide})) {
                    grid-column: span 2;
                }
            }
        }

        @container baccarat (inline-size < #{respond.breakpoint-value('m')}) {
            grid-template-columns: 1fr;
            grid-template-rows: 60vh auto;

            &__tabs {
                order: 2;

                &#{bem.block-selector(module, frame__section)}::before {
                    display: block;
                }
            }

            &__sidebar {
                grid-row: 2;

                & > :last-child {
                    order: 3;
                }
            }

            &__sidebar &__options,
            &__sidebar > &__bet {
                display: flex;
            }

            &__options-bar {
                display: none;
            }
        }

        &__table {
            position: relative;
            display: flex;
            flex-direction: column;
            justify-content: flex-end;
            align-items: center;
            gap: prop.get(size-m-xl);
            min-block-size: 100%;
            overflow: hidden;

            @include prop.set(
                frame-padding,
                prop.get(size-3xl) prop.get(size-m) prop.get(size-m) prop.get(size-m),
                $important: true
            );

            @include bem.module('toast-bar') {
                z-index: 100;
            }
        }

        &__table#{bem.namespaced-selector('is-loading')}
            > :not(#{bem.block-selector(module, loader)}) {
            opacity: 0;
        }

        &__deck {
            position: absolute;
            inset: 0 prop.get(size-m-xl) auto auto;
            inline-size: calc(metrics.fluid-size(70, 120) * prop.get(card-scale, 1));
            aspect-ratio: 120 / 165;
            transform: translateY(-100%);
            transition: transform prop.get(baccarat-anim-duration, 0.3s) ease-in-out;

            & > * {
                position: absolute;
                inline-size: 100%;
                aspect-ratio: 120 / 165;
                inset-block-end: 50%;
                transition: inset-block-end prop.get(baccarat-anim-duration, 0.2) ease-in-out;

                border-radius: 5% / 3.5%;
                background-size: cover;
                background-color: #202020;
                background-image: radial-gradient(
                    50% 50% at 50% 50%,
                    rgba(255, 229, 0, 0.18) 0%,
                    rgba(255, 229, 0, 0) 100%
                );

                &::before {
                    content: '';
                    position: absolute;
                    inset: 0;
                    border-radius: inherit;
                    border: util.to-rem(1px) solid prop.get(color-mt-400);
                }

                &::after {
                    content: '';
                    position: absolute;
                    inset: auto 7% 5% auto;
                    border-radius: inherit;
                    // background: url(./modules/card/assets/decks/default.svg#mt) no-repeat;
                    background: url(./modules/card/assets/decks/default/mt.svg) no-repeat;
                    background-size: cover;
                    inline-size: 25%;
                    aspect-ratio: 1;
                }

                &:nth-child(2) {
                    transition-delay: prop.get(baccarat-anim-duration, 0.15s);
                }

                &:nth-child(3) {
                    transition-delay: prop.get(baccarat-anim-duration, 0.3s);
                }
            }

            &#{bem.namespaced-selector('is-visible')} {
                transform: translateY(-50%);

                > :first-child {
                    inset-block-end: 0;
                }

                > :nth-child(2) {
                    inset-block-end: 12%;
                }

                > :nth-child(3) {
                    inset-block-end: 24%;
                }
            }
        }

        &__ribbons {
            max-inline-size: fit-content;
            justify-self: center;
            justify-content: center;

            @include bem.module(label) {
                font-size: prop.get(fs-label-s);
                line-height: prop.get(lh-label-s);
            }
        }

        &__hands {
            display: flex;
            gap: prop.get(size-m);
            margin: auto;
            min-inline-size: 100%;
            justify-content: space-around;

            > * {
                inline-size: 45%;
            }

            @include respond.until(m) {
                margin-block-end: 0;
            }
        }

        $-hand-padding: prop.get(size-s);
        $-counter-size: calc(prop.get(size-2xs-xs) * 2 + prop.get(lh-1));

        &__hand {
            position: relative;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: prop.get(size-m);
            padding: calc($-hand-padding * 2 + $-counter-size) 0;
            margin: 0 auto;
            min-block-size: calc(1.9 * card.card-block-size());

            @include prop.set(outline-width-fluid, metrics.fluid-size(2, 5));

            &:not(:has(#{bem.block-selector(module, card)})) #{bem.block-selector(module, label)} {
                visibility: hidden;
            }

            @include bem.module(label) {
                position: absolute;
                inset: calc($-hand-padding / 2) 50% auto auto;
                transform: translate(50%, 0);

                & + #{bem.block-selector(module, label)} {
                    inset: auto 50% calc($-hand-padding / 2) auto;
                }
            }

            @include card.overlap(0.4);

            @each $state, $color in (lose: red-400, win: green-400, tie: yellow-400) {
                &--is-#{$state} {
                    @include prop.set(
                        (
                            outline-width: prop.get(outline-width-fluid),
                            outline-color: prop.get(color-#{$color}),
                        )
                    );
                }
            }
        }

        &__hand#{bem.namespaced-selector('is-highlighted')} {
            @include prop.set(
                (
                    outline-width: prop.get(outline-width-fluid),
                    outline-color: prop.get(color-blue-500),
                )
            );
        }

        &__table:has(&__hand:nth-child(2)) #{bem.block-selector(module, card)} {
            @include prop.set(card-scale, 0.75);
        }

        &__hand {
            @include bem.module(label) {
                z-index: 1;

                &:not(:first-child) {
                    outline: prop.get(outline-width, 0) solid prop.get(outline-color, transparent);
                    transition: outline-width prop.get(baccarat-anim-duration, 0.3s) ease;
                }
            }

            :is(
                #{bem.block-selector(module, card__face)},
                #{bem.block-selector(module, card__back)}
            ) {
                outline: prop.get(outline-width, 0) solid prop.get(outline-color, transparent);
                transition:
                    transform prop.get(baccarat-anim-duration, 0.6s) ease,
                    outline-width prop.get(baccarat-anim-duration, 0.3s) ease;
            }
        }

        &:has(&__deck#{bem.namespaced-selector('is-visible')}) &__hand {
            @include bem.module(label) {
                &:first-child {
                    visibility: visible;
                }
            }
        }

        $-controls-size: util.to-rem(24px);

        &__bets {
            inline-size: 100%;
            min-block-size: 30%;
            display: grid;
            grid-template-columns: repeat(prop.get(num-columns, 10), 1fr);
            grid-template-rows:
                repeat(
                    prop.get(num-rows, 2),
                    calc(
                        (100% - #{$-controls-size} - prop.get(num-rows, 2) * prop.get(size-s)) /
                            prop.get(num-rows, 2)
                    )
                )
                $-controls-size;
            gap: prop.get(size-xs-s);

            @include respond.until(m) {
                max-block-size: 35%;
            }
        }

        &__bet {
            position: relative;
            display: flex;
            border-radius: metrics.fluid-size(12, 16);
            border: util.to-rem(1px) solid prop.get(color-zinc-800);
            background: prop.get(color-zinc-900);
            align-items: center;
            justify-content: center;
            padding: prop.get(size-xs);
            gap: prop.get(size-xs);
            cursor: pointer;
            z-index: 1;
            color: prop.get(color-white);

            &:hover {
                border-color: prop.get(color-mt-950);
            }

            &--player {
                grid-area: 1 / 1 / span 2 / span 3;
            }

            &--tie {
                grid-area: 1 / 4 / 1 / span 4;
            }

            &--banker {
                grid-area: 1 / 8 / span 2 / span 3;
            }

            &--side {
                grid-column: span 2;
                z-index: 2;
            }

            &-text {
                display: flex;
                flex-direction: column;
                align-items: center;
                gap: metrics.fluid-size(0, 4);
                user-select: none;

                @include bem.module(label) {
                    font-size: prop.get(fs-label);
                    line-height: prop.get(lh-label);
                }
            }

            &-stacks {
                position: absolute;
                inset: auto 0 10% 0;
                display: flex;
                flex-direction: row;
                justify-content: center;
                align-items: flex-end;
                max-inline-size: 100%;
                flex-wrap: wrap;

                > * {
                    min-block-size: prop.get(chip-size);
                    max-block-size: prop.get(chip-size);
                }
            }

            &--side &-stacks,
            &--tie &-stacks {
                inset-block: auto;
                inset-inline: 0;
            }

            @include bem.module(icon) {
                position: absolute;
                inset: util.to-rem(4px) util.to-rem(4px) auto auto;

                @include respond.until(xl) {
                    display: none;
                }
            }
        }

        &__bet-button {
            inline-size: 100%;
        }

        &__bet-controls {
            display: flex;
            flex-direction: row;
            justify-content: space-between;
            grid-area: 3 / 1 / 3 / span 10;
        }

        @container baccarat (inline-size < #{respond.breakpoint-value('m')}) {
            &__bets {
                @include prop.set(
                    (
                        num-columns: 3,
                        num-rows: 3,
                    )
                );
            }

            &__bet {
                &--player {
                    grid-area: 1 / 1 / span 3;
                }

                &--tie {
                    grid-area: 1 / 2;
                }

                &--banker {
                    grid-area: 1 / 3 / span 3;
                }

                &--side {
                    grid-column: span 1;
                }

                :where(&--tie, &--side) &-text {
                    flex-direction: row;
                    gap: 1ch;
                }
            }

            &__bet-controls {
                grid-area: 4 / 1 / 4 / span 3;
            }
        }
    }
}
