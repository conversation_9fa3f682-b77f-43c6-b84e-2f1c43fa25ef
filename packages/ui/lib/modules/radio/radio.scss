@use '../../styles/common/bem';
@use '../../styles/common/font-weight';
@use '../../styles/common/prop';
@use '../../styles/common/util';

$-border-size: util.to-rem(1px);
$-box-shadow:
    0px 0x 0.8px 0px #0000000f,
    0px 0px 6.4px 0px #00000026,
    0px 0px 0px 0.8px #0000000a;
$-pseudo-size: util.to-rem(10px);
$-radio-size: util.to-rem(16px);
$-radius: 999em;

@mixin styles {
    @include bem.module('radio') {
        display: flex;
        align-items: center;
        flex: none;
        cursor: pointer;

        &:has(:disabled) {
            cursor: default;
        }

        &__input {
            block-size: $-radio-size;
            inline-size: $-radio-size;
            border: $-border-size solid prop.get('color-zinc-400');
            border-radius: $-radius;
            position: relative;
            cursor: pointer;

            &:checked {
                background-color: prop.get('color-mt-950');
                border-color: prop.get('color-mt-400');
            }

            &:checked::after {
                content: '';
                position: absolute;
                block-size: $-pseudo-size;
                inline-size: $-pseudo-size;
                background-color: prop.get('color-mt-400');
                border-radius: $-radius;
                box-shadow: $-box-shadow;
                inset: 50%;
                transform: translate(-50%, -50%);
            }

            &:disabled {
                opacity: 0.7;
            }

            &:focus-visible {
                outline: none;
                box-shadow: 0 0 0 2px prop.get(color-blue-400);
            }
        }

        &__input:disabled + &__label {
            color: prop.get(color-zinc-400);
        }

        &__label {
            font-size: util.to-rem(12px);
            line-height: 1;
            color: prop.get('color-white');
            margin-inline-start: util.to-rem(4px);
            cursor: inherit;

            @include font-weight.set(regular);
        }

        &__addons {
            margin-inline-start: util.to-rem(4px);
        }
    }
}
