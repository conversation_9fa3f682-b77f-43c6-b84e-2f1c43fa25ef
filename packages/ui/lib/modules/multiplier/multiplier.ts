import { defineComponent, html, type Component, type Props } from '../../component';
import type { Reactive } from '../../state';
import { mergeClasses, withNamespace } from '../../utilities/classes';

export interface MultiplierProps extends Props<HTMLDivElement> {
    /**
     * The current multiplier value. Reactive prop.
     */
    readonly value: Reactive<number>;

    /**
     * The target multiplier value. Reactive prop.
     */
    readonly target?: Reactive<number | undefined>; // Optional for default state

    /**
     * Style variant
     */
    readonly variant?: 'default' | 'keno';
}

export const Multiplier = defineComponent(
    'Multiplier',
    ({ state }) =>
        ({
            value,
            target,
            variant = 'default',
            className,
            ...props
        }: MultiplierProps): Component<HTMLDivElement> => {
            const { onPropChange } = state();

            const numberFormatter = new Intl.NumberFormat('en-US', {
                minimumFractionDigits: 2,
                maximumFractionDigits: 2,
            });

            const green = withNamespace('m-multiplier--green');
            const red = withNamespace('m-multiplier--red');

            const renderKeno = (element: HTMLDivElement, formatted: string) => {
                element.innerHTML = '';
                element.appendChild(
                    html('span')({
                        className: withNamespace('m-multiplier__value'),
                        textContent: formatted,
                    }).render(),
                );
                element.appendChild(
                    html('span')({
                        className: withNamespace('m-multiplier__x'),
                        textContent: 'x',
                    }).render(),
                );
                element.classList.remove(green, red);
            };

            const renderDefault = (
                element: HTMLDivElement,
                formatted: string,
                isGreen: boolean,
                isRed: boolean,
            ) => {
                element.textContent = formatted;
                element.classList.toggle(green, isGreen);
                element.classList.toggle(red, isRed);
            };

            return html('div')({
                ...props,
                className: mergeClasses(
                    className,
                    withNamespace('m-multiplier'),
                    withNamespace(`m-multiplier--${variant}`),
                ),
                ref: (element: HTMLDivElement) => {
                    onPropChange(value, target ?? (() => undefined), (current, targetVal) => {
                        const formatted = numberFormatter.format(current);
                        const isWhite = targetVal === undefined;
                        const isGreen = !isWhite && current >= targetVal;
                        const isRed = !isWhite && current < targetVal;

                        element.ariaLabel = `Multiplier: ${formatted}x`;

                        if (variant === 'keno') {
                            renderKeno(element, formatted);
                        } else {
                            renderDefault(element, formatted, isGreen, isRed);
                        }
                    });
                },
            });
        },
);

export default Multiplier;