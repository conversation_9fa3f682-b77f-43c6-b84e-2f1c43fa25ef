{"name": "@monkey-tilt/game-baccarat", "version": "0.1.0-dev", "description": "Monkey Tilt Original Baccarat Game frontend", "private": true, "type": "module", "license": "UNLICENSED", "author": "Monkey Tilt", "contributors": ["Alek<PERSON><PERSON> <<EMAIL>>"], "files": ["dist"], "module": "./dist/baccarat.js", "exports": {".": {"import": "./dist/baccarat.js"}}, "browserslist": [">0.2%", "last 2 versions", "not dead", "not op_mini all"], "scripts": {"dev": "vite", "preview": "vite preview", "build": "vite build", "lint": "eslint ./src", "typecheck": "tsc --noEmit"}, "dependencies": {"@monkey-tilt/client": "workspace:*", "@monkey-tilt/state": "workspace:*", "@monkey-tilt/ui": "workspace:*", "@monkey-tilt/utils": "workspace:*"}, "devDependencies": {"@eslint/js": "catalog:eslint", "@types/node": "catalog:typescript", "browserslist-to-esbuild": "catalog:vite", "eslint": "catalog:eslint", "postcss-logical": "catalog:postcss", "sass-embedded": "catalog:sass", "sharp": "catalog:vite-images", "svgo": "catalog:vite-images", "typescript-eslint-language-service": "catalog:eslint", "typescript-eslint": "catalog:eslint", "typescript": "catalog:typescript", "vite-plugin-image-optimizer": "catalog:vite-images", "vite": "catalog:vite"}, "packageManager": "pnpm@9.15.9+sha512.68046141893c66fad01c079231128e9afb89ef87e2691d69e4d40eee228988295fd4682181bae55b58418c3a253bde65a505ec7c5f9403ece5cc3cd37dcf2531"}