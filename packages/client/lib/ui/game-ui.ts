import {
    DOM<PERSON>eyboardController,
    LocalStorageRepository,
    NullKeyboardController,
    type KeyboardController,
    type KeyboardControllerConfiguration,
    type KeyMapping,
    type Repository,
    type Root,
    type Signal,
    type SignalReader,
    type SignalUpdater,
} from '@monkey-tilt/ui';
import { logDevError } from '@monkey-tilt/utils';
import {
    AudioController,
    SoundSprite,
    type PlaybackOptions,
    type SoundSpriteDefinitionLike,
} from '../audio';
import type { GameClient } from '../client';
import type { HostAction, HostNotification } from '../context';
import type { GameState } from '../messages';
import { RetryError } from '../utils/retry';

export type ConfigValue =
    | string
    | number
    | boolean
    | null
    | ConfigValue[]
    | { [key: string]: ConfigValue };

/**
 * The options for the GameUI class.
 *
 * @template UIState The game UI state interface.
 * @template Game The GameClient type.
 * @template Actions The UIAction type.
 * @template Config The configuration object type.
 * @template SFX The available sound effects names.
 */
export interface GameUIOptions<
    UIState extends GameState,
    Game extends GameClient<GameState>,
    Actions extends string,
    Config extends { [key: string]: ConfigValue },
    SFX extends string,
> {
    /**
     * The base URL used to load assets from.
     */
    readonly assetsUrl: URL;

    /**
     * The GameClient instance.
     */
    readonly client: Game;

    /**
     * The state Root.
     */
    readonly root: Root;

    /**
     * The initial game state.
     */
    readonly initialState: UIState;

    /**
     * Either an already configured KeyboardController instance or
     * a key map configuration to create a new KeyboardController from.
     */
    readonly keyboardShortcuts?:
        | KeyboardController<Actions>
        | KeyboardControllerConfiguration<Actions>['keyMap'];

    /**
     * The configuration keys, with default values.
     */
    readonly config?: {
        readonly [K in keyof Config]: Config[K] | (() => Config[K]);
    };

    /**
     * The sound effects definition.
     */
    readonly soundEffects?: SoundSpriteDefinitionLike<SFX>;

    /**
     * The delay in milliseconds to wait before applying a pending update.
     *
     * Defaults to 300.
     */
    readonly pendingUpdatesDelay?: number;
}

type UIGameState<U> = U & {
    readonly isAnimatingState: boolean;
    readonly round_closed?: boolean;
    readonly pendingUpdates: ReadonlyArray<Partial<UIGameState<U>> | { delayMs?: number }>;
};

type ConfigSchema<C extends Record<string, ConfigValue | (() => ConfigValue)>> = {
    readonly [K in keyof C]: C[K] extends () => infer V ? V : C[K];
};

type ConfigSignals<C extends Record<string, ConfigValue | (() => ConfigValue)>> = {
    [K in keyof C]: Signal<ConfigSchema<C>[K]>;
};

interface GameSeedsState {
    readonly activeClientSeed: Signal<string>;
    readonly activeServerSeed: Signal<string>;
    readonly seedPairTotalBets: Signal<string>;
    readonly newClientSeed: Signal<string>;
    readonly newServerSeed: Signal<string>;
}

export abstract class GameUI<
    State extends GameState,
    Game extends GameClient<GameState>,
    Actions extends string,
    Config extends { [key: string]: ConfigValue },
    SFX extends string,
> {
    protected readonly assetsUrl: URL;
    protected readonly client: Game;
    protected readonly root: Root;
    protected readonly keyboard: KeyboardController<Actions>;
    protected readonly audio: AudioController;
    protected readonly sfx: SoundSprite;

    public readonly config: ConfigSignals<Config>;
    public readonly seeds: GameSeedsState;

    #repository: Repository<ConfigSchema<Config>>;
    #gameState: Signal<UIGameState<State>>;
    #initPromise: Promise<boolean> | null = null;
    #notificationListeners = new Set<(notification: HostNotification) => void>();
    #actionDispatcher: ((action: HostAction) => void) | null = null;

    public constructor({
        assetsUrl,
        client,
        initialState,
        root,
        config = {} as Config,
        keyboardShortcuts,
        soundEffects = {
            sources: [],
            sprites: {},
        } as unknown as SoundSpriteDefinitionLike<SFX>,
        pendingUpdatesDelay = 300,
    }: GameUIOptions<State, Game, Actions, Config, SFX>) {
        this.assetsUrl = assetsUrl;
        this.client = client;
        this.root = root;

        const { signal, effect, memo, untracked } = root.store;

        this.#gameState = signal<UIGameState<State>>({
            ...initialState,
            isAnimatingState: false,
            pendingUpdates: [],
        });

        this.keyboard =
            keyboardShortcuts != null
                ? isKeyboardController(keyboardShortcuts)
                    ? keyboardShortcuts
                    : new DOMKeyboardController({ keyMap: keyboardShortcuts })
                : new NullKeyboardController();

        this.keyboard.onAction(this.triggerAction.bind(this));

        this.audio = new AudioController();
        this.sfx = this.audio.createSoundSprite(soundEffects);

        this.#repository = new LocalStorageRepository<ConfigSchema<Config>>({
            storageKey: 'og',
            signal,
            effect,
        });

        this.config = {} as ConfigSignals<Config>;
        for (const key of Object.keys(config) as Array<keyof Config>) {
            const defaultValue = config[key] as ConfigValue | (() => ConfigValue);
            this.config[key] = this.#repository.signal(
                client.gameId,
                key,
                this.#repository.get('default', key) ??
                    ((defaultValue instanceof Function
                        ? defaultValue()
                        : defaultValue) as ConfigSchema<Config>[keyof Config]),
            );
        }

        if ('soundEffects' in this.config) {
            effect(() => {
                this.audio.muted = !this.config.soundEffects!.read();
            });
        }

        if ('hotkeys' in this.config) {
            effect(() => {
                this.keyboard.isDisabled = !this.config.hotkeys!.read();
            });
        }

        this.seeds = {
            activeClientSeed: signal(''),
            activeServerSeed: signal(''),
            seedPairTotalBets: signal('0'),
            newClientSeed: signal(''),
            newServerSeed: signal(''),
        };

        let updateTimer: ReturnType<typeof setTimeout> | null = null;
        const applyPendingUpdates = (timeout = this.isTurboMode ? 0 : pendingUpdatesDelay) => {
            const { isAnimatingState, pendingUpdates } = this.#gameState.read();

            if (!isAnimatingState || updateTimer) {
                return;
            }

            if (pendingUpdates.length === 0) {
                this.#gameState.update((gameState) => ({
                    ...gameState,
                    isAnimatingState: false,
                }));
                return;
            }

            updateTimer = setTimeout(() => {
                this.#gameState.update((gameState) => {
                    const [nextUpdate, ...pendingUpdates] = gameState.pendingUpdates;

                    if (nextUpdate) {
                        const hasMoreUpdates = pendingUpdates.length > 0;
                        let delayMs: number | undefined;

                        if ('delayMs' in nextUpdate) {
                            delayMs = nextUpdate.delayMs;
                            delete nextUpdate.delayMs;
                        }

                        void Promise.resolve().then(() => {
                            updateTimer = null;
                            if (hasMoreUpdates) {
                                applyPendingUpdates(delayMs);
                            }
                        });

                        return {
                            ...gameState,
                            ...nextUpdate,
                            pendingUpdates,
                            isAnimatingState: hasMoreUpdates,
                        };
                    }

                    return {
                        ...gameState,
                        isAnimatingState: false,
                    };
                });
            }, timeout);
        };

        const isAnimatingState = memo(() => this.#gameState.read().isAnimatingState);

        effect(() => {
            if (isAnimatingState()) {
                untracked(applyPendingUpdates);
            }
        });

        const hasRoundEnded = memo(() => {
            const { isAnimatingState, round_closed } = this.gameState();
            return !isAnimatingState && round_closed;
        });

        effect(() => {
            if (hasRoundEnded()) {
                this.handleRoundEnded();

                void Promise.resolve().then(() => {
                    this.client.signalIdle();
                });
            }
        });

        client.on('readyStateChanged', (readyState) => {
            if (readyState === 'ready') {
                this.refreshSeeds().catch(logDevError);
            }
        });

        this.onHostNotification(({ type, ...payload }) => {
            if (type === 'CURRENCY_CHANGED') {
                this.client.addIntent({ type: 'CloseAutobet' });
                this.client.addIntent({ type: 'SwitchGameSession', ...payload });
            }
        });
    }

    /**
     * Called when the round ends and the game UI state is finished updating.
     */
    protected handleRoundEnded(): void {}

    public abstract triggerAction(action: Actions, data?: unknown): void;

    public get isTurboMode(): boolean {
        return (
            'turboMode' in this.config && !!this.root.store.untracked(this.config.turboMode.read)
        );
    }

    public get keyboardShortcuts(): IterableIterator<KeyMapping<Actions>> {
        return this.keyboard.mappings;
    }

    public get gameState(): SignalReader<UIGameState<State>> {
        return this.#gameState.read;
    }

    protected updateGameState(gameState: Parameters<SignalUpdater<UIGameState<State>>>[0]): void {
        this.#gameState.update(gameState);
    }

    public dispose(): void {
        this.root.unmount();
        this.client.close();
        this.keyboard.dispose();
        this.audio.dispose();
        this.#actionDispatcher = null;
        this.#notificationListeners.clear();
    }

    public onHostNotification(callback: (notification: HostNotification) => void): () => void {
        this.#notificationListeners.add(callback);
        return () => {
            this.#notificationListeners.delete(callback);
        };
    }

    public set hostActionDispatcher(dispatch: (action: HostAction) => void) {
        this.#actionDispatcher = dispatch;
    }

    public dispatchHostAction(action: HostAction): void {
        if (this.#actionDispatcher) {
            this.#actionDispatcher(action);
        } else {
            logDevError('Host action dispatcher is not set');
        }
    }

    public handleHostNotification(notification: HostNotification): void {
        for (const listener of this.#notificationListeners) {
            listener(notification);
        }
    }

    public initSession(): Promise<boolean> {
        if (this.#initPromise) {
            return this.#initPromise;
        }
        this.#initPromise = this.client
            .reload()
            .catch((error) => {
                if (error instanceof RetryError) {
                    error = error.reason;
                }
                if (
                    'code' in error &&
                    (error as { code: unknown }).code === 'GAME_ROUND_NOT_FOUND'
                ) {
                    this.client.reset();
                } else {
                    throw error;
                }
            })
            .then(() => {
                this.client.signalSessionStart();
                return true;
            })
            .finally(() => {
                this.#initPromise = null;
            });
        return this.#initPromise;
    }

    public playSound(sprite: SFX, options?: PlaybackOptions): Promise<void> {
        return this.sfx.play(sprite, options);
    }

    public async refreshSeeds(): Promise<void> {
        const { client_seed, server_seed_hashed, next_server_seed_hashed } =
            await this.client.getSeeds();

        this.seeds.activeClientSeed.update(client_seed);
        this.seeds.activeServerSeed.update(server_seed_hashed);
        this.seeds.newClientSeed.update(Math.random().toString(36).slice(2, 10));
        this.seeds.newServerSeed.update(next_server_seed_hashed);
    }

    public async rotateSeedPair(): Promise<void> {
        const newClientSeed = this.seeds.newClientSeed.read();

        if (!newClientSeed) {
            throw new Error('New Client Seed is required');
        }

        await this.client.setClientSeed(newClientSeed);

        this.seeds.activeClientSeed.update(newClientSeed);
        this.seeds.activeServerSeed.update(this.seeds.newServerSeed.read());
        this.seeds.newClientSeed.update('');
        this.seeds.newServerSeed.update('');

        this.refreshSeeds().catch(logDevError);
    }
}

function isKeyboardController<T extends string>(value: unknown): value is KeyboardController<T> {
    return value instanceof DOMKeyboardController || value instanceof NullKeyboardController;
}
