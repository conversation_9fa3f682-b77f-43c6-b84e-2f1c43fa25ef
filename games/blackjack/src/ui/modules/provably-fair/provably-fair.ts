import {
    But<PERSON>,
    define<PERSON>ompo<PERSON>,
    Flex,
    Frame,
    Label,
    Modal,
    Reel,
    Stack,
    Tabs,
    withNamespace,
    type Component,
    type ModalControls,
    type ModalProps,
    type TabControls,
} from '@monkey-tilt/ui';
import type { CardInfo } from '../../../game/state';
import { sumCards } from '../../../util/sum';
import type { BlackjackUI } from '../../blackjack';
import { Card } from '../card/card';
import { TextInput } from '../text-input/text-input';

export interface ProvablyFairProps extends ModalProps {
    /**
     * The Blackjack UI state manager.
     */
    readonly ui: BlackjackUI;
}

export const ProvablyFair = defineComponent(
    'ProvablyFair',
    ({ state }) =>
        ({ ui, ...props }: ProvablyFairProps): Component<HTMLDialogElement> => {
            const { signal, effect } = state();

            const [activeTab, setActiveTab] = signal<'seeds' | 'verify'>('seeds');

            const [rotateSeedError, setRotateSeedError] = signal('');

            const verifyClientSeed = signal('');
            const verifyServerSeed = signal('');
            const verifyNonce = signal('');
            const [verifyError, setVerifyError] = signal('');

            const [cards, setCards] = signal<CardInfo[]>([]);

            effect(() => {
                const client_seed = verifyClientSeed.read();
                const server_seed = verifyServerSeed.read();
                const nonce = Number(verifyNonce.read());

                if (!Number.isFinite(nonce)) {
                    setVerifyError('Nonce must be a number');
                    return;
                }

                setVerifyError('');

                if (client_seed && server_seed && nonce) {
                    ui.verifySeeds({ client_seed, server_seed, nonce })
                        .then(setCards)
                        .catch((error) => {
                            setVerifyError(
                                error instanceof Error ? error.message : 'Failed to verify seeds',
                            );
                        });
                }
            });

            return Modal(
                {
                    title: 'Provably Fair',
                    controls: (controls: ModalControls) => {
                        ui.provablyFairModalControlsRef.current = controls;
                    },
                    ...props,
                },
                Tabs({
                    value: activeTab(),
                    variant: 'subtle',
                    tabs: [
                        { label: 'Seeds', value: 'seeds' },
                        { label: 'Verify', value: 'verify' },
                    ],
                    onChange(value: 'seeds' | 'verify') {
                        setActiveTab(value);
                    },
                    controls({ selectTab }: TabControls) {
                        effect(() => {
                            selectTab(activeTab());
                        });
                    },
                }),
                Stack(
                    {
                        gap: 'l',
                        ref(element: HTMLDivElement) {
                            effect(() => {
                                element.classList.toggle(
                                    withNamespace('u-hide'),
                                    activeTab() !== 'seeds',
                                );
                            });
                        },
                    },
                    TextInput({
                        label: 'Active Client Seed',
                        value: ui.seeds.activeClientSeed,
                        readOnly: true,
                        withCopy: true,
                    }),
                    TextInput({
                        label: 'Active Server Seed (hashed)',
                        value: ui.seeds.activeServerSeed,
                        readOnly: true,
                        withCopy: true,
                    }),
                    Label({ variant: 'medium' }, 'Rotate Seed Pair'),
                    TextInput({
                        label: 'New Client Seed*',
                        value: ui.seeds.newClientSeed,
                        error: rotateSeedError,
                        onChange() {
                            setRotateSeedError('');
                        },
                        button: Button(
                            {
                                variant: 'accent',
                                onClick(e: MouseEvent) {
                                    if (!(e.target instanceof HTMLButtonElement)) {
                                        return;
                                    }
                                    const button = e.target;
                                    button.disabled = true;

                                    setRotateSeedError('');

                                    ui.rotateSeedPair()
                                        .catch((error) => {
                                            setRotateSeedError(
                                                error instanceof Error
                                                    ? error.message
                                                    : 'Failed to change client seed',
                                            );
                                        })
                                        .finally(() => {
                                            button.disabled = false;
                                        });
                                },
                            },
                            'Change',
                        ),
                    }),
                    TextInput({
                        label: 'New Server Seed',
                        value: ui.seeds.newServerSeed,
                        readOnly: true,
                        withCopy: true,
                    }),
                ),
                Stack(
                    {
                        gap: 'l',
                        ref(element: HTMLDivElement) {
                            effect(() => {
                                element.classList.toggle(
                                    withNamespace('u-hide'),
                                    activeTab() === 'seeds',
                                );
                            });
                        },
                    },
                    Frame({
                        variant: 'dashed',
                        css: { 'min-block-size': '35vh' },
                        cssProps: { 'card-size': '3rem' },
                        ref: (element: HTMLDivElement) => {
                            effect(() => {
                                element.innerHTML = '';

                                const deck = cards();

                                if (deck.length < 5) {
                                    return;
                                }

                                const dealerHand = deck.slice(0, 2);
                                const playerHand = deck.slice(2, 4);
                                const cardsRow = deck.slice(4);

                                Stack(
                                    { gap: 'l', center: true },
                                    Stack(
                                        { gap: 's', center: true },
                                        Flex(
                                            { gap: 's' },
                                            Label({ variant: 'medium' }, 'Dealer'),
                                            Label({ variant: 'box' }, sumCards(dealerHand)),
                                        ),
                                        Flex(
                                            { gap: 's' },
                                            dealerHand.map((card) => Card({ card })),
                                        ),
                                    ),
                                    Stack(
                                        { gap: 's', center: true },
                                        Flex(
                                            { gap: 's' },
                                            Label({ variant: 'medium' }, 'Player'),
                                            Label({ variant: 'box' }, sumCards(playerHand)),
                                        ),
                                        Flex(
                                            { gap: 's' },
                                            playerHand.map((card) => Card({ card })),
                                        ),
                                    ),
                                    Reel(
                                        { gap: 's', css: { 'margin-top': { var: 'size-s' } } },
                                        cardsRow.map((card) => Card({ card })),
                                    ),
                                ).renderTo(element);
                            });
                        },
                    }),
                    TextInput({
                        label: 'Client Seed',
                        value: verifyClientSeed,
                        placeholder: 'Enter Client Seed',
                    }),
                    TextInput({
                        label: 'Server Seed',
                        value: verifyServerSeed,
                        placeholder: 'Enter Server Seed',
                    }),
                    TextInput({
                        label: 'Nonce',
                        value: verifyNonce,
                        error: verifyError,
                        placeholder: 'Enter Nonce',
                        inputMode: 'numeric',
                    }),
                ),
            );
        },
);
