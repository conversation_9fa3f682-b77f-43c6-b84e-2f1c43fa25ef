import { timer } from '@monkey-tilt/client';
import {
    applyClasses,
    defineComponent,
    forwardRef,
    html,
    Label,
    mergeClasses,
    type Component,
    type LabelControls,
    type Props,
    type SignalReader,
} from '@monkey-tilt/ui';
import type { Card as CardData, Hand as HandData } from '../../../game/state';
import type { BaccaratUI } from '../../baccarat';
import { sumCards } from '../../util/sum';
import { Card, type CardProps } from '../card/card';

export class CardsAddedEvent extends CustomEvent<HTMLDivElement[]> {
    constructor(cards: HTMLDivElement[]) {
        super('cardsadded', { detail: cards, cancelable: true });
    }
}

export class CardsRemovedEvent extends CustomEvent<HTMLDivElement[]> {
    constructor(cards: HTMLDivElement[]) {
        super('cardsremoved', { detail: cards, cancelable: true });
    }
}

export interface HandProps extends Props<HTMLDivElement> {
    /**
     * The Baccarat UI state manager.
     */
    readonly ui: BaccaratUI;

    /**
     * The name of the hand.
     */
    readonly name: string;

    /**
     * The signal reader for the given hand.
     */
    readonly hand: SignalReader<HandData | undefined>;

    /**
     * Called with an array of card elements before new cards are added to the hand.
     */
    readonly onBeforeCardsAdded?: (event: CardsAddedEvent) => void;

    /**
     * Called with an array of card elements when new cards are added to the hand.
     */
    readonly onCardsAdded?: (event: CardsAddedEvent) => void;

    /**
     * Called with an array of card elements before cards are removed from the hand.
     */
    readonly onBeforeCardsRemoved?: (event: CardsRemovedEvent) => void;

    /**
     * Called with an array of card elements when cards are removed from the hand.
     */
    readonly onCardsRemoved?: (event: CardsRemovedEvent) => void;
}

const revealAfter = 400;

export const Hand = defineComponent(
    'Hand',
    ({ state, render }) =>
        ({
            ui,
            name,
            hand,
            onBeforeCardsAdded,
            onCardsAdded,
            onBeforeCardsRemoved,
            onCardsRemoved,
            ...props
        }: HandProps): Component<HTMLDivElement> => {
            const { previousMemo, memo, effect } = state();

            const cards = previousMemo(() => hand()?.cards ?? []);
            const sum = memo(() => sumCards(cards().current));
            const status = memo(() => hand()?.status ?? 'unknown');

            const commonCardProps: CardProps = {
                revealAfter,
                turboMode: () => ui.isTurboMode,
                onReveal: () => {
                    if (!ui.isTurboMode) {
                        void ui.playSound('flip');
                    }
                },
            };

            const renderCard = (card: CardData) => render(Card({ ...commonCardProps, card }));

            return html('div')(
                {
                    ...props,
                    className: mergeClasses(props.className, { 'm-baccarat__hand': true }),
                    ref: forwardRef(props.ref, (element: HTMLDivElement) => {
                        const renderedCards = new Map<string, HTMLDivElement>();

                        effect(() => {
                            const { current, previous } = cards();
                            const addedCards = new Map<string, HTMLDivElement>();
                            const removedCards = new Map<string, HTMLDivElement>();

                            for (const card of current) {
                                if (!renderedCards.has(card.id)) {
                                    addedCards.set(card.id, renderCard(card));
                                }
                            }

                            for (const card of previous) {
                                if (current.find(({ id }) => id == card.id) != null) {
                                    continue;
                                }

                                const cardElement = renderedCards.get(card.id);
                                if (cardElement) {
                                    removedCards.set(card.id, cardElement);
                                }
                            }

                            if (removedCards.size > 0) {
                                void Promise.resolve().then(() => {
                                    const removedElements = Array.from(removedCards.values());
                                    const netRemoved = removedElements.slice(addedCards.size);

                                    const event = new CardsRemovedEvent(netRemoved);
                                    onBeforeCardsRemoved?.(event);

                                    if (!event.defaultPrevented) {
                                        for (const [id, card] of removedCards) {
                                            card.remove();
                                            renderedCards.delete(id);
                                        }

                                        if (onCardsRemoved) {
                                            void Promise.resolve().then(() => {
                                                onCardsRemoved(new CardsRemovedEvent(netRemoved));
                                            });
                                        }
                                    } else {
                                        for (const [id, card] of Array.from(removedCards).slice(
                                            0,
                                            addedCards.size,
                                        )) {
                                            card.remove();
                                            renderedCards.delete(id);
                                        }
                                    }
                                });
                            }

                            if (addedCards.size > 0) {
                                void Promise.resolve().then(() => {
                                    const addedElements = Array.from(addedCards.values());
                                    const event = new CardsAddedEvent(addedElements);
                                    onBeforeCardsAdded?.(event);

                                    if (!event.defaultPrevented) {
                                        for (const [id, card] of addedCards) {
                                            element.appendChild(card);
                                            renderedCards.set(id, card);
                                        }

                                        if (onCardsAdded) {
                                            void Promise.resolve().then(() => {
                                                onCardsAdded(new CardsAddedEvent(addedElements));
                                            });
                                        }
                                    }
                                });
                            }
                        });

                        const statusUpdateDelay = timer(revealAfter + 100);

                        effect(() => {
                            const handStatus = status();

                            const update = () => {
                                applyClasses(element, [
                                    {
                                        'm-baccarat__hand--is-tie': handStatus === 'tie',
                                        'm-baccarat__hand--is-win': handStatus === 'win',
                                        'm-baccarat__hand--is-lose': handStatus === 'lose',
                                    },
                                ]);
                            };

                            if (ui.isTurboMode) {
                                update();
                            } else {
                                statusUpdateDelay(update);
                            }
                        });
                    }),
                },
                Label({ text: name, variant: 'small-box' }),
                Label({
                    text: sum(),
                    variant: 'box',
                    controls({ setText }: LabelControls) {
                        const sumUpdateDelay = timer(revealAfter + 100);
                        effect(() => {
                            const newSum = sum();
                            if (ui.isTurboMode) {
                                setText(newSum);
                            } else {
                                sumUpdateDelay(() => setText(newSum));
                            }
                        });
                    },
                }),
            );
        },
);
