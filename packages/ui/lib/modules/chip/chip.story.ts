import type { Meta, StoryObj } from '@storybook/html';
import { fn } from '@storybook/test';
import { render } from '../../../.storybook/render';
import { html } from '../../component';
import { Chip, ChipValue, ChipValues, type ChipProps, type ChipStackProps } from './chip';

const meta: Meta = {
    title: 'Modules/Chip',
    globals: {
        backgrounds: { value: 'dark' },
    },
};
export default meta;

export const Default: StoryObj = (props: ChipProps) => {
    return render(({ onStoryUnmount, store: { signal } }) => {
        const [isSelected, setIsSelected] = signal((props.isSelected as boolean) ?? false);

        const interval = setInterval(() => {
            setIsSelected((prev) => !prev);
        }, 1000);

        onStoryUnmount(() => {
            clearInterval(interval);
        });

        return Chip({ ...props, isSelected });
    });
};
Default.argTypes = {
    value: {
        control: 'select',
        options: ChipValues,
    },
    color: {
        control: 'select',
        options: [
            'red',
            'black',
            'blue',
            'orange',
            'violet',
            'indigo',
            'dark-cyan',
            'cyan',
            'green',
        ],
    },
    isSelected: {
        control: 'boolean',
    },
};
Default.args = {
    value: '1',
    isSelected: false,
};

export const Stack: StoryObj = (props: ChipStackProps) => {
    return html('div')(
        {
            css: {
                display: 'flex',
                'flex-direction': 'column',
                'justify-content': 'flex-end',
                'align-items': 'center',
                'min-block-size': 'calc(100vh - 2rem)',
            },
        },
        Chip.Stack(props),
    ).render();
};
Stack.argTypes = {
    count: {
        control: 'number',
    },
    value: {
        control: 'select',
        options: ChipValues,
    },
    color: {
        control: 'select',
        options: [
            'red',
            'black',
            'blue',
            'orange',
            'violet',
            'indigo',
            'dark-cyan',
            'cyan',
            'green',
        ],
    },
    isSelected: {
        control: 'boolean',
    },
};
Stack.args = {
    count: 5,
    value: '1',
    isSelected: false,
};

export const Select: StoryObj = () => {
    return render(({ store: { signal, effect } }) => {
        const selectedChip = signal<ChipValue | null>(null);
        const onSelect = fn().mockName('onSelect');

        effect(() => {
            onSelect(selectedChip.read());
        });

        return Chip.Select({ selectedChip });
    });
};
Select.args = {};
