import {
    BucketRow,
    defineComponent,
    html,
    withNamespace,
    Stats,
    type BucketRowControls,
    type Component,
    type Props,
} from '@monkey-tilt/ui';
import type { Plinko<PERSON> } from '../../plinko';
import { PlinkoRenderer } from '../../renderer/canvas';
import type { BallData } from '../../renderer/types';
import { autoUpdate, computePosition, offset, shift } from '@floating-ui/dom';
import { formatCurrency } from '@monkey-tilt/client';
import { RowCount } from '../../../game/state';

export interface BoardProps extends Props<HTMLDivElement> {
    readonly ui: PlinkoUI;
    readonly onBoardIdle?: () => void;
    readonly onBucketHit?: (ballData: BallData) => void;
}

export const Board = defineComponent(
    'Board',
    ({ state }) =>
        ({ ui, onBoardIdle, onBucketHit, ...props }: BoardProps): Component<HTMLDivElement> => {
            const { onPropChange, memo, read, signal } = state();

            const activeBalls = new Set<string>();
            let bucketRow: BucketRowControls | undefined;

            let statsElement: HTMLElement | null = null;
            let arrowElement: HTMLElement | null = null;
            let cleanupFn: (() => void) | null = null;

            const chance = signal<string>('');
            const payout = signal<string>('');

            const focusedBucketIndex = signal(-1);
            const hits = memo(() => ui.stats.read()[ui.number_of_rows.read() as RowCount]);

            return html('div')(
                {
                    className: withNamespace('m-board'),
                    ref: (element: HTMLDivElement) => {
                        const rowsProp = `--${withNamespace('rows')}`;
                        onPropChange(ui.number_of_rows, (numberOfRows) => {
                            element.style.setProperty(rowsProp, numberOfRows.toString());
                        });
                    },
                },
                html('canvas')({
                    ...props,
                    className: withNamespace('m-board__canvas'),
                    ref: (canvas: HTMLCanvasElement) => {
                        const board = new PlinkoRenderer(canvas, {
                            numberOfRows: read(ui.number_of_rows),
                            onIdle: () => {
                                onBoardIdle?.();
                            },
                            onPegHit: () => {
                                void ui.playSound('peg');
                            },
                            onBucketHit: (ballData: BallData) => {
                                activeBalls.delete(ballData.id);

                                onBucketHit?.(ballData);

                                if (bucketRow) {
                                    const index = ballData.bucketNumber - 1;

                                    const numOfRows = ui.number_of_rows.read() as RowCount;

                                    ui.stats.update((prev) => {
                                        const currentMap = prev[numOfRows];
                                        const newMap = new Map(currentMap);
                                        const currentValue = newMap.get(index) ?? 0;
                                        newMap.set(index, currentValue + 1);

                                        return {
                                            ...prev,
                                            [numOfRows]: newMap,
                                        };
                                    });

                                    bucketRow.hitBucket(index);

                                    void ui.playSound('bucket', {
                                        playbackRate:
                                            1 +
                                            0.01 *
                                                Math.abs(
                                                    index - Math.floor(read(ui.number_of_rows) / 2),
                                                ),
                                    });
                                }
                            },
                        });

                        onPropChange(ui.config.turboMode, (isTurboMode) => {
                            board.isTurboMode = isTurboMode;
                        });

                        onPropChange(ui.number_of_rows, (numberOfRows) => {
                            board.numberOfRows = numberOfRows;
                        });

                        onPropChange(ui.droppedBall, (droppedBall) => {
                            if (droppedBall && !activeBalls.has(droppedBall.id)) {
                                board.dropBall(droppedBall);
                            }
                        });
                    },
                }),
                BucketRow({
                    className: withNamespace('m-board__buckets'),
                    multipliers: memo(() => ui.multipliers().map(([multiplier]) => multiplier)),
                    controls: (controls: BucketRowControls) => {
                        bucketRow = controls;
                    },
                    onBucketFocus: (index: number) => {
                        focusedBucketIndex.update(index);

                        payout.update(
                            formatCurrency(
                                parseFloat(ui.multipliers()[index]![0]) *
                                    parseFloat(ui.betAmount.read()),
                                ui.currency(),
                            ),
                        );

                        chance.update(
                            (parseFloat(ui.multipliers()[index]![1]) * 100)
                                .toFixed(8)
                                .replace(/\.?0+$/, '') + '%',
                        );

                        const bucketClass = withNamespace('m-board__buckets');
                        const bucketSelector = `.${bucketClass} [data-index="${index}"]`;
                        const bucketEl = document.querySelector(bucketSelector);

                        if (!bucketEl || !statsElement || !arrowElement) return;

                        if (cleanupFn) {
                            cleanupFn();
                            cleanupFn = null;
                        }

                        statsElement.classList.remove(withNamespace('u-hide'));
                        arrowElement.classList.remove(withNamespace('u-hide'));

                        cleanupFn = autoUpdate(bucketEl, statsElement, async () => {
                            const { x, y } = await computePosition(bucketEl, statsElement!, {
                                placement: 'top',
                                middleware: [offset(8), shift()],
                            });

                            const { x: xa, y: ya } = await computePosition(
                                bucketEl,
                                arrowElement!,
                                {
                                    placement: 'top',
                                    middleware: [offset(8), shift()],
                                },
                            );

                            Object.assign(statsElement!.style, {
                                position: 'absolute',
                                left: `${x}px`,
                                top: `${y}px`,
                            });

                            Object.assign(arrowElement!.style, {
                                position: 'absolute',
                                left: `${xa}px`,
                                top: `${ya + arrowElement!.offsetHeight}px`,
                            });
                        });
                    },
                    onBlur: () => {
                        if (statsElement) {
                            statsElement.classList.add(withNamespace('u-hide'));
                        }
                        if (arrowElement) {
                            arrowElement.classList.add(withNamespace('u-hide'));
                        }
                        if (cleanupFn) {
                            cleanupFn();
                            cleanupFn = null;
                        }
                    },
                }),
                Stats({
                    className: withNamespace('m-board__stats'),
                    stats: [
                        { label: 'Chance', value: chance },
                        { label: 'Payout', value: payout },
                        {
                            label: 'Times Landed',
                            value: memo(() => {
                                const index = focusedBucketIndex.read();
                                const bucketHits = hits();
                                return index >= 0 ? bucketHits.get(index) : 0;
                            }),
                        },
                    ],
                    ref: (el: HTMLElement) => {
                        statsElement = el;
                        el.classList.add(withNamespace('u-hide'));
                    },
                }),
                html('div')({
                    className: withNamespace('m-stats__arrow'),
                    ref: (el: HTMLElement) => {
                        arrowElement = el;
                        el.classList.add(withNamespace('u-hide'));
                    },
                }),
            );
        },
);
