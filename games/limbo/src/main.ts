import type { GameContext } from '@monkey-tilt/client';
import { createRoot, withNamespace } from '@monkey-tilt/ui';
import { createLimboClient, type LimboClientOptions } from './game/limbo';
import { Game, LimboUI } from './ui';

export interface LimboRunOptions extends Omit<LimboClientOptions, 'root'> {
    readonly container: HTMLElement | string;
}

const assetsUrl = new URL(
    globalThis && globalThis.document.currentScript instanceof HTMLScriptElement
        ? globalThis.document.currentScript.src
        : location.href,
);

if (globalThis && globalThis.document.currentScript instanceof HTMLScriptElement) {
    const link = document.createElement('link');
    link.rel = 'stylesheet';
    link.href = new URL('./limbo.css', assetsUrl).href;
    document.head.appendChild(link);
}

export async function run({ container, ...options }: LimboRunOptions): Promise<GameContext> {
    const element: HTMLElement | null =
        typeof container === 'string' ? document.querySelector(container) : container;

    if (!element) {
        throw new Error('Invalid container provided');
    }

    const handleResize = () => {
        element.classList.add(
            ...['s-app', 'l-cover', 'l-cover--stretch', 'm-limbo-container'].map(withNamespace),
        );
        element.style.setProperty(`--${withNamespace('min-block-size')}`, '100%');
    };

    handleResize();

    const root = createRoot(element, () => {
        element.innerHTML = '';
    });

    const ui = new LimboUI({
        client: await createLimboClient({ ...options, root }),
        assetsUrl,
        root,
    });

    root.render(Game({ ui, className: { 'l-cover__principal': true } }));

    return {
        unmount: () => {
            ui.dispose();
        },
        onAction: (callback) => {
            ui.hostActionDispatcher = callback;
        },
        notify: (notification) => {
            if (notification.type === 'CONTAINER_RESIZED') {
                handleResize();
            }
            ui.handleHostNotification(notification);
        },
    };
}
