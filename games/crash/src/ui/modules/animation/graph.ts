import * as <PERSON>IX<PERSON> from 'pixi.js';
import { 
    MAX_DATA_POINTS, 
    SAMPLING_INTERVAL, 
    ANIMATION_DURATION, 
    COLORS,
    MATH_CONFIG,
    TIMING_CONFIG,
    CANVAS_WIDTH,
    CANVAS_HEIGHT
} from './constants';
import { AnimationHelper, type AnimationContext } from './animations';
import { AssetHelper } from './assets';
import { PositionHelper, type PositionData, type ScreenPoint } from './positioning';
import { RenderingHelper, DataHelper } from './rendering';
import { defineComponent, html, type Component, type Props } from '@monkey-tilt/ui';

/**
 * High-performance animated crash game renderer with PixiJS WebGL.
 *
 * Handles the visual rendering of the crash game including:
 * - Animated line graph with real-time data points
 * - Monkey head character with propeller animations
 * - Win/lose animations and visual feedback
 * - Cloud particle effects and background parallax
 */
export class CrashGameRenderer {
    // --- Pixi.js Internals ---CRAS
    private app: PIXI.Application;
    private lineGraphics: PIXI.Graphics;
    private backgroundSprite: PIXI.Sprite | null = null;
    private cornerMultiplierContainer: PIXI.Container;
    private cornerMultiplierBackground: PIXI.Graphics;
    private cornerMultiplierText: PIXI.Text;
    private monkeyHeadContainer: PIXI.Container | null = null;
    private cloudContainer: PIXI.Container;

    // --- Helper Classes ---
    private animationHelper: AnimationHelper;
    private assetHelper: AssetHelper;
    private positionHelper: PositionHelper;
    private renderingHelper: RenderingHelper;

    // --- Graph Data & State ---
    public readonly dataPoints: Array<PositionData> = [];
    private currentDataIndex: number = 0;
    private lineEndX: number = 0;
    private lineEndY: number = 0;
    private readonly originalMonkeySvgUrl?: string;
    private readonly monkeyHeadWinSvgUrl?: string;
    private readonly monkeyHeadCrashSvgUrl?: string;
    private readonly monkeyHeadSvg2Url?: string;

    // --- Game Logic ---
    private crashMultiplier: number = 2.0;
    private playerTargetMultiplier: number = 2.0;
    private hasCrashed: boolean = false;
    private crashScreenPosition: ScreenPoint = { x: 0, y: 0 };
    private gameOutcome: 'running' | 'win' | 'lose' = 'running';
    private isRunning: boolean = false;
    private awaitingNextRound: boolean = false;

    // --- Animation & Timing ---
    private lineAnimationProgress: number = 0;
    private targetDataPointCount: number = 0;
    private dataPointInterval: ReturnType<typeof setInterval> | null = null;
    private graphStartTime: number = 0;
    private dynamicTimeout: ReturnType<typeof setTimeout> | null = null;

    // --- Layout & Sizing ---
    private width: number;
    private height: number;
    private readonly padding: number;
    private requiresRedraw: boolean = true;

    /** Calculate bottom padding as 5% of canvas height */
    private get bottomPadding(): number {
        return this.height * 0.05;
    }

    // --- Callbacks & Observers ---
    public onCrash?: () => void;
    private resizeDebounceTimeout: number | null = null;
    private resizeObserver: ResizeObserver | null = null;

    public constructor(
        container: HTMLElement,
        width = CANVAS_WIDTH,
        height = CANVAS_HEIGHT,
        backgroundSvgUrl?: string,
        monkeyHeadSvgUrl?: string,
        monkeyHeadWinSvgUrl?: string,
        monkeyHeadCrashSvgUrl?: string,
        monkeyHeadSvg2Url?: string,
    ) {
        const containerRect = container.getBoundingClientRect();
        this.width = containerRect.width > 0 ? containerRect.width : width;
        this.height = containerRect.height > 0 ? containerRect.height : height;
        this.padding = 10;

        // Store SVG URLs
        if (monkeyHeadSvgUrl) this.originalMonkeySvgUrl = monkeyHeadSvgUrl;
        if (monkeyHeadWinSvgUrl) this.monkeyHeadWinSvgUrl = monkeyHeadWinSvgUrl;
        if (monkeyHeadCrashSvgUrl) this.monkeyHeadCrashSvgUrl = monkeyHeadCrashSvgUrl;
        if (monkeyHeadSvg2Url) this.monkeyHeadSvg2Url = monkeyHeadSvg2Url;

        // Initialize PIXI components
        this.app = new PIXI.Application();
        this.lineGraphics = new PIXI.Graphics();
        this.cloudContainer = new PIXI.Container();
        this.cornerMultiplierContainer = new PIXI.Container();
        this.cornerMultiplierBackground = new PIXI.Graphics();
        this.cornerMultiplierText = new PIXI.Text({
            text: '1.00x',
            style: {
                fontFamily: 'Poppins',
                fontSize: 24,
                fontWeight: 'normal',
                fill: '#ffffff',
                align: 'center',
            },
        });
        this.cornerMultiplierContainer.addChild(this.cornerMultiplierBackground);
        this.cornerMultiplierContainer.addChild(this.cornerMultiplierText);

        // Initialize helper classes
        this.assetHelper = new AssetHelper(this.app, this.width, this.height);
        this.positionHelper = new PositionHelper(this.width, this.height, this.padding);
        this.renderingHelper = new RenderingHelper(this.lineGraphics, this.cloudContainer);

        // Initialize animation helper with context
        const animationContext: AnimationContext = {
            monkeyHeadContainer: this.monkeyHeadContainer,
            monkeyHeadSvg2Url: this.monkeyHeadSvg2Url,
            originalMonkeySvgUrl: this.originalMonkeySvgUrl,
            isRunning: this.isRunning,
            hasCrashed: this.hasCrashed,
            data: this.dataPoints,
            loadSvgAsHiResTexture: this.assetHelper.loadSvgAsHiResTexture.bind(this.assetHelper),
            switchMonkeyTexture: this.switchMonkeyTexture.bind(this)
        };
        this.animationHelper = new AnimationHelper(animationContext);
        
        void this.initializePixi(container).then(() => {
            this.setupGraphics();
            this.startAnimationLoop();
            
            if (backgroundSvgUrl) {
                this.setBackgroundSVG(backgroundSvgUrl).catch(() => {
                    // Error is handled inside setBackgroundSVG
                });
            }
            if (this.originalMonkeySvgUrl) {
                this.setMonkeyHeadSVG(this.originalMonkeySvgUrl).catch((error) => {
                    console.error('Failed to load monkey head SVG:', error);
                });
            }
        });

        this.resizeObserver = new ResizeObserver(() => {
            this.debouncedHandleResize(container);
        });
        this.resizeObserver.observe(container);
    }

    // --- Public API ---

    public startGraph(): void {
        this.reset();
        this.isRunning = true;
        this.requiresRedraw = true;
        this.updateAnimationContext();

        // Show the monkey head when the game starts
        if (this.monkeyHeadContainer) {
            this.monkeyHeadContainer.visible = true;
        }

        if (this.dataPointInterval) clearInterval(this.dataPointInterval);
        if (this.dynamicTimeout) clearTimeout(this.dynamicTimeout);
        
        this.graphStartTime = Date.now();
        this.scheduleNextDataPoint();
        
        this.animationHelper.startPropellerAnimation();
        this.startAnimationLoop();
    }

    public stopGraph(): void {
        this.isRunning = false;
        this.updateAnimationContext();
        if (this.dataPointInterval) {
            clearInterval(this.dataPointInterval);
            this.dataPointInterval = null;
        }
        if (this.dynamicTimeout) {
            clearTimeout(this.dynamicTimeout);
            this.dynamicTimeout = null;
        }
        this.animationHelper.stopPropellerAnimation();
    }

    public reset(): void {
        if (this.dataPointInterval) {
            clearInterval(this.dataPointInterval);
            this.dataPointInterval = null;
        }
        if (this.dynamicTimeout) {
            clearTimeout(this.dynamicTimeout);
            this.dynamicTimeout = null;
        }
        this.dataPoints.length = 0;
        this.currentDataIndex = 0;
        this.lineAnimationProgress = 0;
        this.targetDataPointCount = 0;
        this.hasCrashed = false;
        this.crashScreenPosition = { x: 0, y: 0 };
        this.lineEndX = this.padding;
        this.lineEndY = this.height - this.bottomPadding;
        this.gameOutcome = 'running';
        this.playerTargetMultiplier = 2.0;
        
        this.animationHelper.resetAnimations();
        
        // Properly hide monkey head container during reset
        if (this.monkeyHeadContainer) {
            this.monkeyHeadContainer.visible = false;
        }
        
        // Always reset to the original neutral monkey SVG and reset scale
        if (this.originalMonkeySvgUrl) {
            void this.setMonkeyHeadSVG(this.originalMonkeySvgUrl).then(() => {
                this.animationHelper.resetMonkeyScale();
                // Keep it hidden until the game starts
                if (this.monkeyHeadContainer) {
                    this.monkeyHeadContainer.visible = false;
                }
            });
        }
        this.requiresRedraw = true;
        this.cornerMultiplierText.text = '1.00x';
        this.cornerMultiplierText.style.fill = COLORS.WHITE;
        this.updateAnimationContext();
    }

    private scheduleNextDataPoint(): void {
        if (!this.isRunning || this.hasCrashed) {
            return;
        }
        
        const interval = this.calculateDynamicInterval();
        this.dynamicTimeout = setTimeout(() => {
            this.addDataPoint();
            if (this.hasGraphCrashed) {
                this.stopGraph();
            } else {
                this.scheduleNextDataPoint();
            }
        }, interval);
    }

    private calculateDynamicInterval(): number {
        const elapsedTime = Date.now() - this.graphStartTime;
        const elapsedSeconds = elapsedTime / 1000;
        
        // Start with a slow interval (3x normal) and accelerate to normal speed over 5 seconds
        const slowStartMultiplier = 3;
        const accelerationDuration = 5; // seconds
        
        if (elapsedSeconds >= accelerationDuration) {
            return SAMPLING_INTERVAL; // Normal speed
        }
        
        // Use easeInCubic to start slow and accelerate
        const progress = elapsedSeconds / accelerationDuration;
        const easedProgress = Math.pow(progress, 3); // easeInCubic
        
        // Interpolate from slow to normal speed
        const multiplier = slowStartMultiplier - (slowStartMultiplier - 1) * easedProgress;
        return SAMPLING_INTERVAL * multiplier;
    }

    public addDataPoint(): void {
        if (this.hasCrashed || !this.isRunning) {
            return;
        }
        
        const result = DataHelper.generateNextPoint(this.currentDataIndex, this.crashMultiplier, this.hasCrashed);
        
        if (result.shouldCrash) {
            this.hasCrashed = true;
            this.isRunning = false;
            
            if (this.crashMultiplier >= this.playerTargetMultiplier) {
                this.gameOutcome = 'win';
                if (this.monkeyHeadWinSvgUrl) {
                    void this.setMonkeyHeadSVG(this.monkeyHeadWinSvgUrl).then(() => {
                        this.animationHelper.startWinAnimation();
                    });
                } else {
                    console.log('No win SVG URL provided');
                }
            } else {
                this.gameOutcome = 'lose';
                if (this.monkeyHeadCrashSvgUrl) {
                    void this.setMonkeyHeadSVG(this.monkeyHeadCrashSvgUrl).then(() => {
                        this.animationHelper.startCrashAnimation();
                    });
                } else {
                    console.log('No crash SVG URL provided');
                }
            }
            
            this.dataPoints.push(result.point);
            
            const crashScreenPoint = this.positionHelper.toScreen(result.point, this.dataPoints);
            this.crashScreenPosition.x = crashScreenPoint.x;
            this.crashScreenPosition.y = crashScreenPoint.y;
            
            this.updateMultiplierLabel(crashScreenPoint.x, crashScreenPoint.y, result.point.y);
            this.renderingHelper.clearClouds();
            
            if (this.onCrash) {
                this.onCrash();
            }
        } else {
            this.dataPoints.push(result.point);
            if(this.dataPoints.length > 0 && this.dataPoints.length % 100 === 0) {
                console.log(`[addDataPoint] dataPoints.length: ${this.dataPoints.length}`);
            }
        }
        
        DataHelper.trimDataArray(this.dataPoints, MAX_DATA_POINTS);
        
        this.currentDataIndex++;
        this.targetDataPointCount = this.dataPoints.length;
        this.lineAnimationProgress = 0;
        this.requiresRedraw = true;
        this.updateAnimationContext();
    }

    public setCrashMultiplier(multiplier: number): void {
        this.crashMultiplier = multiplier;
        this.requiresRedraw = true;
        
        if (this.awaitingNextRound) {
            this.awaitingNextRound = false;
            this.startGraph();
        }
    }

    public setPlayerTargetMultiplier(multiplier: number): void {
        this.playerTargetMultiplier = multiplier;
    }

    public async setBackgroundSVG(svgUrl: string): Promise<void> {
        try {
            if (this.backgroundSprite) {
                this.assetHelper.disposeSprite(this.backgroundSprite);
                this.backgroundSprite = null;
            }
            
            this.backgroundSprite = await this.assetHelper.createBackgroundSprite(svgUrl);
            
            if (this.backgroundSprite) {
                this.assetHelper.positionBackgroundSprite(this.backgroundSprite);
            }
        } catch (error) {
            this.backgroundSprite = null;
            this.app.renderer.background.color = COLORS.BLACK;
            this.app.renderer.background.alpha = 1;
        }
    }

    public removeBackgroundSVG(): void {
        if (this.backgroundSprite) {
            this.assetHelper.disposeSprite(this.backgroundSprite);
            this.backgroundSprite = null;
        }
    }

    public async setMonkeyHeadSVG(svgUrl: string): Promise<void> {
        try {
            if (this.monkeyHeadContainer) {
                this.assetHelper.disposeContainer(this.monkeyHeadContainer);
                this.monkeyHeadContainer = null;
            }

            this.monkeyHeadContainer = await this.assetHelper.createMonkeyHeadSprite(svgUrl);
            
            if (this.monkeyHeadContainer) {
                this.app.stage.addChild(this.monkeyHeadContainer);
                this.positionMonkeyHead();
            }
            
            this.updateAnimationContext();
        } catch (error) {
            console.error('Failed to load SVG as image:', error);
            this.monkeyHeadContainer = null;
        }
    }

    public dispose(): void {
        this.isRunning = false;
        
        if (this.resizeDebounceTimeout !== null) {
            clearTimeout(this.resizeDebounceTimeout);
        }
        
        if (this.resizeObserver) {
            this.resizeObserver.disconnect();
        }

        if (this.dataPointInterval) {
            clearInterval(this.dataPointInterval);
            this.dataPointInterval = null;
        }
        
        this.animationHelper.resetAnimations();
        
        this.lineGraphics?.destroy({ children: true });
        this.cloudContainer?.destroy({ children: true });
        this.cornerMultiplierContainer?.destroy({ children: true });
        this.assetHelper.disposeContainer(this.monkeyHeadContainer);
        this.assetHelper.disposeSprite(this.backgroundSprite);
        this.renderingHelper.dispose();
        
        this.app.destroy(true, { children: true, texture: true });
        this.dataPoints.length = 0;
        this.onCrash = () => {};
    }

    // --- Public Getters ---
    
    public get hasGraphCrashed(): boolean {
        return this.hasCrashed;
    }

    public hasCrashedForMultiplier(multiplier: number): boolean {
        return this.hasCrashed && Math.abs(this.crashMultiplier - multiplier) < 0.001;
    }

    public get isGraphRunning(): boolean {
        return this.isRunning;
    }

    public waitForNewBet(): void {
        this.awaitingNextRound = true;
    }

    // --- Private Methods ---

    private updateAnimationContext(): void {
        // Update the animation context with current state
        this.animationHelper.updateContext({
            monkeyHeadContainer: this.monkeyHeadContainer,
            isRunning: this.isRunning,
            hasCrashed: this.hasCrashed,
            data: this.dataPoints
        });
    }

    private async switchMonkeyTexture(svgUrl: string): Promise<void> {
        await this.animationHelper.switchMonkeyTexture(svgUrl);
    }

    private startAnimationLoop(): void {
        let lastTime = 0;
        let animationFrameId: number | null = null;
        
        const animate = (currentTime: number) => {
            const deltaTime = currentTime - lastTime;
            lastTime = currentTime;
            
            let hasChanges = false;
            
            if (this.lineAnimationProgress < 1 && this.isRunning) {
                this.lineAnimationProgress += deltaTime / ANIMATION_DURATION;
                this.lineAnimationProgress = Math.min(this.lineAnimationProgress, 1);
                hasChanges = true;
            }
            
            if (hasChanges || this.requiresRedraw) {
                this.renderGraph();
                this.requiresRedraw = false;
            }
            
            if (this.isRunning || hasChanges || this.requiresRedraw) {
                animationFrameId = requestAnimationFrame(animate);
            } else {
                animationFrameId = null;
            }
        };
        
        if (animationFrameId === null) {
            animationFrameId = requestAnimationFrame(animate);
        }
    }

    private renderGraph(): void {
        if (this.dataPoints.length < 2) {
            this.cornerMultiplierText.text = '1.00x';
            this.cornerMultiplierText.style.fill = COLORS.WHITE;
            return;
        }

        const visibleData = this.dataPoints.slice(
            0,
            Math.min(this.dataPoints.length, Math.ceil(this.targetDataPointCount)),
        );
        
        if (visibleData.length === 0) {
            this.cornerMultiplierText.text = '1.00x';
            this.cornerMultiplierText.style.fill = COLORS.WHITE;
            return;
        }

        // Update background position if sprite exists
        if (this.backgroundSprite && this.dataPoints.length > 0) {
            this.assetHelper.updateBackgroundPosition(
                this.backgroundSprite,
                this.lineEndX,
                this.lineEndY,
                this.padding
            );
        }
        
        // Interpolate the last segment for smooth animation
        const interpolatedPos = this.positionHelper.calculateInterpolatedPosition(
            visibleData, 
            this.lineAnimationProgress
        );
        
        if (interpolatedPos) {
            this.lineEndX = interpolatedPos.x;
            this.lineEndY = interpolatedPos.y;
        }

        this.drawGraphLine();
    }

    private drawGraphLine(): void {
        const toScreen = (point: PositionData) => this.positionHelper.toScreen(point, this.dataPoints);
        
        this.renderingHelper.plotLine(this.dataPoints, toScreen, this.hasCrashed);

        const lastScreenPoint = this.dataPoints.length > 0 ? 
            toScreen(this.dataPoints[this.dataPoints.length - 1]!) : null;

        if (lastScreenPoint) {
            this.lineEndX = lastScreenPoint.x;
            this.lineEndY = lastScreenPoint.y;
            this.updateMultiplierLabel(
                this.lineEndX,
                this.lineEndY,
                this.dataPoints[this.dataPoints.length - 1]!.y,
            );
        } else if (this.dataPoints.length === 0) {
            this.lineEndX = this.padding;
            this.lineEndY = this.height - this.bottomPadding;
        }

        this.positionMonkeyHead();
        this.renderingHelper.drawClouds(
            this.dataPoints, 
            toScreen, 
            this.width, 
            this.height, 
            this.isRunning, 
            this.hasCrashed
        );
    }

    private positionMonkeyHead(): void {
        if (this.monkeyHeadContainer) {
            this.positionHelper.positionMonkeyHead(
                this.monkeyHeadContainer,
                this.lineEndX,
                this.lineEndY,
                this.dataPoints,
                this.gameOutcome
            );
        }
    }

    private updateMultiplierLabel(x: number, y: number, multiplier: number): void {
        this.positionHelper.positionMultiplierLabel(
            this.cornerMultiplierContainer,
            this.cornerMultiplierBackground,
            this.cornerMultiplierText,
            x,
            y,
            multiplier,
            this.gameOutcome,
            this.dataPoints.length
        );
    }

    private async initializePixi(container: HTMLElement): Promise<void> {
        await this.app.init({
            width: this.width,
            height: this.height,
            backgroundColor: COLORS.BACKGROUND,
            backgroundAlpha: 0,
            antialias: true,
            resolution: Math.min((window.devicePixelRatio || 1) * 1.5, 2),
            autoDensity: true,
        });

        container.appendChild(this.app.canvas);
        this.app.canvas.style.width = '100%';
        this.app.canvas.style.height = '100%';
        this.app.canvas.style.display = 'block';
        this.app.canvas.style.border = 'none';
    }

    private setupGraphics(): void {
        this.app.stage.addChild(this.lineGraphics);
        this.app.stage.addChild(this.cloudContainer);
        this.app.stage.addChild(this.cornerMultiplierContainer);
        this.positionHelper.setupMultiplierDisplay(
            this.cornerMultiplierContainer,
            this.cornerMultiplierBackground,
            this.cornerMultiplierText
        );
    }

    private handleResize(container: HTMLElement): void {
        const containerRect = container.getBoundingClientRect();
        if (containerRect.width > 0 && containerRect.height > 0) {
            const newWidth = Math.floor(containerRect.width);
            const newHeight = Math.floor(containerRect.height);
            
            const widthDiff = Math.abs(newWidth - this.width);
            const heightDiff = Math.abs(newHeight - this.height);
            
            if (widthDiff > MATH_CONFIG.RESIZE_THRESHOLD || heightDiff > MATH_CONFIG.RESIZE_THRESHOLD) {
                this.width = newWidth;
                this.height = newHeight;
                
                this.app.renderer.resize(this.width, this.height);
                this.assetHelper.updateDimensions(this.width, this.height);
                this.positionHelper.updateDimensions(this.width, this.height);
                
                if (this.backgroundSprite) {
                    this.assetHelper.positionBackgroundSprite(this.backgroundSprite);
                }
                
                this.positionHelper.setupMultiplierDisplay(
                    this.cornerMultiplierContainer,
                    this.cornerMultiplierBackground,
                    this.cornerMultiplierText
                );
                
                this.requiresRedraw = true;
            }
        }
    }

    private debouncedHandleResize(container: HTMLElement): void {
        if (this.resizeDebounceTimeout !== null) {
            clearTimeout(this.resizeDebounceTimeout);
        }
        
        this.resizeDebounceTimeout = window.setTimeout(() => {
            this.handleResize(container);
            this.resizeDebounceTimeout = null;
        }, TIMING_CONFIG.RESIZE_DEBOUNCE);
    }
}

// --- Component Export ---

export interface GraphProps extends Props<HTMLDivElement> {
    readonly value: () => number;
    readonly ui: { 
        gameState: () => any;
        signalGraphCrashed?: () => void;
        multiplier: { read: () => string };
    };
    readonly onGraphReady?: (graph: CrashGameRenderer) => void;
    readonly backgroundSvg?: string;
    readonly monkeyHeadSvg?: string;
    readonly monkeyHeadWinSvg?: string;
    readonly monkeyHeadCrashSvg?: string;
    readonly monkeyHeadSvg2?: string;
}

export const Graph = defineComponent(
    'Graph',
    ({ state, onUnmount }) =>
        ({
            ui,
            value,
            onGraphReady,
            backgroundSvg,
            monkeyHeadSvg,
            monkeyHeadWinSvg,
            monkeyHeadCrashSvg,
            monkeyHeadSvg2,
            ...props
        }: GraphProps): Component<HTMLDivElement> => {
            const { effect } = state();
            let graph: CrashGameRenderer | null = null;

            return html('div')({
                ...props,
                ref: (element: HTMLDivElement) => {
                    if (graph) {
                        graph.dispose();
                    }
                    graph = new CrashGameRenderer(
                        element,
                        CANVAS_WIDTH,
                        CANVAS_HEIGHT,
                        backgroundSvg,
                        monkeyHeadSvg,
                        monkeyHeadWinSvg,
                        monkeyHeadCrashSvg,
                        monkeyHeadSvg2,
                    );
                    
                    onUnmount(() => {
                        if (graph) {
                            graph.dispose();
                        }
                    });
                    
                    let lastProcessedMultiplier: string | number | undefined;
                    let processingBet = false;
                    
                    effect(() => {
                        const gameState: unknown = ui.gameState();
                        
                        if (typeof gameState === 'object' && gameState !== null) {
                            const { random_multiplier, action } = gameState as {
                                random_multiplier?: string | number;
                                action?: string;
                            };
                            
                            if (random_multiplier && action && ['Bet', 'Autobet', 'Nextbet'].includes(action) && 
                                random_multiplier !== lastProcessedMultiplier && !processingBet) {
                                processingBet = true;
                                lastProcessedMultiplier = random_multiplier;
                                const crashMultiplier = Number(random_multiplier);
                                
                                const playerTargetMultiplier = Number(ui.multiplier.read());
                                
                                if (graph) {
                                    graph.setCrashMultiplier(crashMultiplier);
                                    graph.setPlayerTargetMultiplier(playerTargetMultiplier);
                                }
                                
                                setTimeout(() => { processingBet = false; }, TIMING_CONFIG.PROCESSING_TIMEOUT);
                            }
                        }
                    });
                    
                    if (onGraphReady && graph) {
                        onGraphReady(graph);
                    }
                    
                    if (graph) {
                        graph.onCrash = () => {
                            if (ui.signalGraphCrashed) {
                                ui.signalGraphCrashed();
                            }
                        };
                    }
                    
                    return () => {
                        if (graph) {
                            graph.stopGraph();
                        }
                    };
                },
            });
        },
);