import {
    arrow,
    autoPlacement,
    autoUpdate,
    computePosition,
    hide,
    offset,
    shift,
} from '@floating-ui/dom';
import {
    defineComponent,
    forwardRef,
    html,
    render,
    type Props,
    type RefObject,
    type Renderable,
} from '../../component';
import { mergeClasses, withNamespace } from '../../utilities/classes';
import { onClickOutside } from '../../utilities/clickOutside';

export interface TooltipControls {
    setContent(this: void, content: Renderable): void;
    ref(this: void): HTMLElement | null;
}

const Side = ['top', 'right', 'bottom', 'left'] as const;
type Side = (typeof Side)[number];

export interface TooltipProps extends Props<HTMLDivElement> {
    readonly placement?: Side | `${Side}-${'start' | 'end'}`;
    readonly controls?: (controls: TooltipControls) => void;
    readonly target?: RefObject<HTMLElement> | HTMLElement | string;
    readonly showOn?: 'hover' | 'click';
}

export const Tooltip = defineComponent(
    'Tooltip',
    ({ root }) =>
        ({ controls, target, placement = 'right', showOn = 'hover', ...props }: TooltipProps) => {
            const setup = (element: HTMLElement) => {
                const referenceElement =
                    typeof target === 'string'
                        ? document.querySelector(target)
                        : target instanceof HTMLElement
                          ? target
                          : (target?.current ?? element.parentElement);

                if (!referenceElement) {
                    console.error('Tooltip target not found');
                    return;
                }

                const hideClassName = withNamespace('u-hide');

                const tooltipX = `--${withNamespace('tooltip-x')}`;
                const tooltipY = `--${withNamespace('tooltip-y')}`;

                const tooltipArrowX = `--${withNamespace('tooltip-arrow-x')}`;
                const tooltipArrowY = `--${withNamespace('tooltip-arrow-y')}`;
                const tooltipArrowAngle = `--${withNamespace('tooltip-arrow-angle')}`;

                const dpr = window.devicePixelRatio || 1;
                const toPx = (value: number) => `${Math.round(value * dpr) / dpr}px`;

                let cleanupFn: (() => void) | null = null;

                const hideTooltip = () => {
                    if (cleanupFn === null) {
                        return;
                    }

                    cleanupFn();
                    cleanupFn = null;
                    element.classList.add(hideClassName);
                };

                const showTooltip = () => {
                    if (cleanupFn !== null || element.textContent === '') {
                        return;
                    }

                    element.classList.remove(hideClassName);
                    element.style.setProperty('visibility', 'visible');

                    const stopAutoUpdate = autoUpdate(referenceElement, element, () => {
                        void computePosition(referenceElement, element, {
                            placement,
                            middleware: [
                                offset(10),
                                shift(),
                                autoPlacement(),
                                hide(),
                                arrow({ element: arrowElement, padding: 8 }),
                            ],
                        }).then(
                            ({
                                x,
                                y,
                                middlewareData: {
                                    offset = { placement, x: 0, y: 0 },
                                    hide,
                                    arrow = {},
                                },
                            }) => {
                                element.style.setProperty(tooltipX, toPx(x));
                                element.style.setProperty(tooltipY, toPx(y));

                                let angle = '0';
                                const inset = {
                                    left: 'auto',
                                    right: 'auto',
                                    top: 'auto',
                                    bottom: 'auto',
                                };

                                switch (offset.placement.split('-')[0] as Side) {
                                    case 'left': {
                                        angle = '180deg';
                                        inset.top = toPx(0);
                                        inset.right = toPx(offset.x);
                                        break;
                                    }
                                    case 'right': {
                                        inset.top = toPx(0);
                                        inset.left = toPx(-offset.x);
                                        break;
                                    }
                                    case 'top': {
                                        angle = '-90deg';
                                        inset.left = toPx(0);
                                        inset.bottom = toPx(offset.y);
                                        break;
                                    }
                                    case 'bottom': {
                                        angle = '90deg';
                                        inset.left = toPx(0);
                                        inset.top = toPx(-offset.y);
                                        break;
                                    }
                                }

                                for (const side of Side) {
                                    arrowElement.style.setProperty(side, inset[side]);
                                }

                                arrowElement.style.setProperty(tooltipArrowAngle, angle);

                                element.style.setProperty(
                                    tooltipArrowX,
                                    arrow.x != null ? toPx(arrow.x) : '',
                                );
                                element.style.setProperty(
                                    tooltipArrowY,
                                    arrow.y != null ? toPx(arrow.y) : '',
                                );
                                element.style.setProperty(
                                    'visibility',
                                    arrow.centerOffset != 0 || hide?.referenceHidden
                                        ? 'hidden'
                                        : '',
                                );
                            },
                        );
                    });

                    let cancelClickOutside: (() => void) | null = null;

                    void Promise.resolve().then(() => {
                        if (cleanupFn) {
                            cancelClickOutside = onClickOutside(element, hideTooltip);
                        }
                    });

                    cleanupFn = () => {
                        stopAutoUpdate();
                        if (cancelClickOutside) {
                            cancelClickOutside();
                        }
                    };
                };

                if (showOn === 'hover') {
                    referenceElement.addEventListener('mouseenter', showTooltip);
                    referenceElement.addEventListener('mouseleave', hideTooltip);
                }

                referenceElement.addEventListener('click', showTooltip);
            };

            const arrowElement = html('div')({
                className: withNamespace('m-tooltip__arrow'),
            }).render();

            root.append(
                html('div')({
                    role: 'tooltip',
                    ...props,
                    className: mergeClasses(props.className, { 'm-tooltip': true, 'u-hide': true }),
                    children: [...(props.children ?? []), arrowElement],
                    ref: forwardRef(props.ref, (element: HTMLElement) => {
                        if (controls) {
                            controls({
                                setContent(content) {
                                    element.innerHTML = '';
                                    const node = render(content, root);
                                    if (node) {
                                        element.appendChild(node);
                                    }
                                    element.appendChild(arrowElement);
                                },
                                ref() {
                                    return element;
                                },
                            });
                        }
                        void Promise.resolve().then(() => setup(element));
                    }),
                }),
            );

            return null;
        },
);
