import type { GameActionName } from '.';

/**
 * The "registry" of all supported message types.
 * To be extended by the game-specific messages.
 *
 * Each key of this object represents a message action (e.g., "Bet").
 *
 * Value of each key is an object with the following properties:
 *  - `type`: The type of the message, if this is not a GameEvent/GameUpdate message.
 *  - `request`: The type of the request payload, or `void` if there is no payload.
 *  - `response`: The type of the response payload, or `void` if there is no payload.
 */
export interface MessageTypes {
    /**
     * The "auth" message.
     *
     * This message is used to authenticate the client.
     */
    readonly auth: {
        readonly type: 'Authenticate';
        readonly request: {};
        readonly response: {
            /**
             * Whether the authentication was successful.
             */
            readonly authenticated: boolean;
        };
    };

    readonly GameState: {
        readonly type: 'GameState';
        readonly request: void;
        readonly response: GameState;
    };

    /**
     * This is the first message required to start ANY game.
     * It will start a round and create an internal GameState in the backend
     */
    readonly Bet: {
        readonly request: BetPayload;
        readonly response: GameState;
    };
}

/**
 * The game-specific Bet action payload.
 */
export interface BetPayload {}

/**
 * The game state as returned by the "GameState" action.
 *
 * This type should be extended by the game-specific state.
 */
export interface GameState {
    readonly next_actions: ReadonlyArray<GameActionName>;
}
