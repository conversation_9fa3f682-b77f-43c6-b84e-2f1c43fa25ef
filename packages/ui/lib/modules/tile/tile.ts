import { defineComponent, html, type Component, type Props } from '../../component';
import { mergeClasses, withNamespace } from '../../utilities/classes';
import type { Reactive } from '../../state';

export interface TileProps extends Omit<Props<HTMLButtonElement>, 'disabled'> {
    readonly number: Reactive<number>;
    readonly isSelected?: Reactive<boolean>;
    readonly isHit?: Reactive<boolean>;
    readonly disabled?: Reactive<boolean>;
    readonly isMiss?: Reactive<boolean>;
}

export const Tile = defineComponent(
    'Tile',
    ({ state }) =>
        ({
            number,
            isSelected,
            isHit,
            isMiss,
            disabled,
            ...props
        }: TileProps): Component<HTMLButtonElement> => {
            const { onPropChange } = state();

            return html('button')({
                ...props,
                className: mergeClasses(withNamespace('m-tile')),
                ref: (element) => {
                    const numberElement = html('span')({
                        className: withNamespace('m-tile__number'),
                    }).render();
                    element.appendChild(numberElement);

                    onPropChange(number, (value) => {
                        numberElement.textContent = value.toString();
                    });

                    if (isSelected) {
                        onPropChange(isSelected, (selected) => {
                            element.classList.toggle(withNamespace('m-tile--selected'), selected);
                        });
                    }
                    if (isHit) {
                        onPropChange(isHit, (hit) => {
                            element.classList.toggle(withNamespace('m-tile--hit'), hit);
                        });
                    }

                    if (isMiss) {
                        onPropChange(isMiss, (isMissed) => {
                            element.classList.toggle(withNamespace('m-tile--miss'), isMissed);
                        });
                    }

                    if (disabled) {
                        onPropChange(disabled, (isDisabled) => {
                            element.disabled = isDisabled;
                            element.classList.toggle(withNamespace('m-tile--disabled'), isDisabled);
                        });
                    }
                },
                children: [],
            });
        },
);

export default Tile;
