import {
    defineComponent,
    forwardRef,
    Frame,
    html,
    Label,
    namespaced,
    Toast,
    withNamespace,
    type Component,
    type Props,
    type RefObject,
    type ToastBarControls,
} from '@monkey-tilt/ui';
import type { BlackjackUI } from '../../blackjack';
import { Balance } from '../balance/balance';
import { Loader } from '../loader/loader';
import { Hand, type HandProps } from './hand';

export interface TableProps extends Props<HTMLDivElement> {
    /**
     * The Blackjack UI state manager.
     */
    readonly ui: BlackjackUI;
}

export const Table = defineComponent(
    'Table',
    ({ state, render }) =>
        ({ ui, ...props }: TableProps): Component<HTMLDivElement> => {
            const { previousMemo, memo, effect, signal } = state();

            const div = html('div');

            const playerTable: RefObject<HTMLDivElement> = { current: null };
            const deck: RefObject<HTMLDivElement> = { current: null };

            const [isLoading, setIsLoading] = signal(ui.gameState().isLoading);
            effect(() => {
                let timer: ReturnType<typeof setTimeout> | undefined;
                const newIsLoading = ui.gameState().isLoading;

                if (newIsLoading) {
                    timer = setTimeout(() => {
                        setIsLoading(true);
                    }, 1500);
                } else {
                    setIsLoading(false);
                }

                return () => {
                    if (timer) {
                        clearTimeout(timer);
                    }
                };
            });

            const dealerHand = memo(() => ui.gameState().dealer_hand);
            const playerHands = previousMemo(() => ui.gameState().player_hands);
            const activeHand = memo(() => {
                const { hand_index, hand_owner, player_hands, round_closed } = ui.gameState();
                return {
                    shouldHighlight: !round_closed && player_hands.length > 1,
                    index: hand_index,
                    owner: hand_owner,
                };
            });

            function animateHand(
                element: RefObject<HTMLDivElement>,
                { isDealer = false }: { isDealer?: boolean } = {},
            ): Partial<HandProps> {
                const before = { x: 0, y: 0 };
                let firstRender = true;
                return {
                    onBeforeCardsAdded: () => {
                        if (element.current) {
                            const { x, y } = element.current.getBoundingClientRect();
                            before.x = x;
                            before.y = y;
                            if (firstRender) {
                                element.current.style.visibility = 'hidden';
                            }
                        }
                    },
                    onCardsAdded: ({ detail: cards }) => {
                        const { isTurboMode } = ui;
                        const duration = (ms: number) => (isTurboMode ? 0.1 : ms);

                        if (deck.current) {
                            const deckRect = deck.current.getBoundingClientRect();
                            for (let i = 0; i < cards.length; i++) {
                                const card = cards[i]!;
                                const rect = card.getBoundingClientRect();

                                const fromX = deckRect.x - rect.x;
                                const fromY = deckRect.y - rect.y - deckRect.height;

                                card.animate(
                                    [
                                        { transform: 'translate(0, 0)' },
                                        { transform: `translate(${fromX}px, ${fromY}px)` },
                                    ],
                                    {
                                        duration: duration(1),
                                        fill: 'forwards',
                                    },
                                );

                                card.animate(
                                    [
                                        { transform: `translate(${fromX}px, ${fromY}px)` },
                                        { transform: 'translate(0, 0)' },
                                    ],
                                    {
                                        delay: duration(i * 100),
                                        duration: duration(300),
                                        easing: 'ease-in-out',
                                        fill: 'forwards',
                                    },
                                );
                            }

                            if (firstRender && element.current) {
                                setTimeout(() => {
                                    element.current!.style.visibility = 'visible';
                                }, 100);
                            }
                        }

                        void ui.playSound('deal');

                        if (firstRender || !element.current) {
                            firstRender = false;
                            return;
                        }

                        const { x, y } = element.current.getBoundingClientRect();

                        element.current.animate(
                            [
                                { transform: `translate(${before.x - x}px, ${before.y - y}px)` },
                                { transform: 'translate(0, 0)' },
                            ],
                            {
                                duration: duration(150),
                                easing: 'ease-out',
                                fill: 'forwards',
                            },
                        );
                    },
                    onBeforeCardsRemoved: (event) => {
                        event.preventDefault();

                        const { isTurboMode } = ui;
                        const duration = (ms: number) => (isTurboMode ? 0.1 : ms);

                        for (let i = 0; i < event.detail.length; i++) {
                            const animation = event.detail[i]!.animate(
                                [
                                    { opacity: 1, transform: 'translate(0, 0)' },
                                    { opacity: 0, transform: 'translate(-50%, 30%)' },
                                ],
                                {
                                    delay: duration(i * 50),
                                    duration: duration(150),
                                    easing: 'ease-out',
                                    fill: 'forwards',
                                },
                            );
                            if (i === event.detail.length - 1) {
                                animation.onfinish = () => {
                                    event.detail.forEach((card) => card.remove());
                                    if (
                                        !element.current!.querySelector(
                                            `.${withNamespace('m-card')}`,
                                        )
                                    ) {
                                        firstRender = true;
                                        if (!isDealer) {
                                            element.current!.remove();
                                        }
                                    }
                                };
                            }
                        }
                    },
                };
            }

            const renderedHands = new Map<string, HTMLDivElement>();

            effect(() => {
                const { current, previous } = playerHands();

                if (!playerTable.current) {
                    return;
                }

                const animDuration = ui.isTurboMode ? 0 : 300;

                const removed = previous.filter(
                    ({ id }) => renderedHands.has(id) && !current.some((hand) => id == hand.id),
                );

                for (const { id } of removed) {
                    const element = renderedHands.get(id)!;
                    renderedHands.delete(id);

                    setTimeout(() => element.remove(), animDuration);
                }

                for (let index = 0; index < current.length; index++) {
                    const { id, splitFrom } = current[index]!;

                    if (renderedHands.has(id)) {
                        continue;
                    }

                    const element: RefObject<HTMLDivElement> = { current: null };

                    element.current = render(
                        Hand({
                            ui,
                            hand: memo(() => playerHands().current.find((hand) => hand.id == id)),
                            isHighlighted: memo(() => {
                                const { shouldHighlight, owner, index } = activeHand();
                                return (
                                    shouldHighlight &&
                                    owner == 'Player' &&
                                    index ==
                                        playerHands().current.findIndex((hand) => hand.id == id)
                                );
                            }),
                            ...animateHand(element),
                        }),
                    );

                    renderedHands.set(id, element.current);

                    const splitFromElement = splitFrom ? renderedHands.get(splitFrom) : undefined;

                    setTimeout(() => {
                        if (!element.current || !playerTable.current) {
                            return;
                        }
                        if (splitFromElement) {
                            splitFromElement.insertAdjacentElement('afterend', element.current);
                        } else {
                            playerTable.current.appendChild(element.current);
                        }
                    }, animDuration);
                }
            });

            const dealerHandElement: RefObject<HTMLDivElement> = { current: null };

            return Frame(
                {
                    ...props,
                    ref: forwardRef(props.ref, (element: HTMLDivElement) => {
                        effect(() => {
                            element.classList.toggle(withNamespace('is-loading'), isLoading());
                        });
                    }),
                    className: { 'm-blackjack__table': true },
                },
                Loader({
                    isLoading,
                    text: [
                        { text: 'Loading, please wait...', delay: 1000 },
                        { text: 'This is taking longer than expected...', delay: 5000 },
                        { text: 'Please check your internet connection!', delay: 8000 },
                    ],
                    ref: (element: HTMLDivElement) => {
                        effect(() => {
                            element.classList.toggle(withNamespace('u-hide'), !isLoading());
                        });
                    },
                }),
                Balance({ ui }),
                div(
                    { className: namespaced('m-blackjack__dealer') },
                    Hand({
                        ui,
                        hand: dealerHand,
                        ref: dealerHandElement,
                        ...animateHand(dealerHandElement, { isDealer: true }),
                    }),
                ),
                div(
                    { className: namespaced('l-stack', 'u-gap--xs', 'm-blackjack__ribbons') },
                    Label({ text: 'Blackjack pays 3 to 2', variant: 'ribbon-l' }),
                    Label({ text: 'Insurance pays 2 to 1', variant: 'ribbon-r' }),
                ),
                div({ className: namespaced('m-blackjack__player'), ref: playerTable }),
                div(
                    {
                        className: namespaced('m-blackjack__deck'),
                        ref: forwardRef(deck, (element: HTMLDivElement) => {
                            const isTableEmpty = memo(
                                () => ui.gameState().dealer_hand.cards.length == 0,
                            );
                            effect(() => {
                                element.classList.toggle(
                                    withNamespace('is-visible'),
                                    !isTableEmpty(),
                                );
                            });
                        }),
                    },
                    div(),
                    div(),
                    div(),
                ),
                Toast.Bar({
                    position: 'absolute',
                    controls: (toastControls: ToastBarControls) => {
                        ui.toastBarControlsRef.current = toastControls;
                    },
                }),
            );
        },
);
