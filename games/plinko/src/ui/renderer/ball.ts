import { logDevError } from '@monkey-tilt/utils';
import { Ball as BallBody } from '../../physics/bodies/ball';
import { BALL_RADIUS } from '../../physics/constants';
import { type DynamicRenderable, Risk } from './types';
import { loadImage } from './utils/image';
import { lerp } from './utils/lerp';

import highRiskBallUrl from './assets/high.svg';
import lowRiskBallUrl from './assets/low.svg';
import mediumRiskBallUrl from './assets/medium.svg';

// To account for the fact that a drop shadow makes the ball actually smaller than
// the image size, we scale the image up a bit.
// This helps to ensure that the ball appears to have the correct radius.
const IMAGE_SCALE = 1.2;

const IMAGE_SIZE = BALL_RADIUS * 2 * IMAGE_SCALE;
const IMAGE_OFFSET = BALL_RADIUS * IMAGE_SCALE;

/**
 * A Plinko ball implementation.
 */
export class Ball extends BallBody implements DynamicRenderable {
    #previousPosition;
    #image: CanvasImageSource | null = null;

    constructor(risk: Risk, x: number, y: number) {
        super(x, y);

        this.#previousPosition = { x, y };

        loadImage(
            risk === Risk.High
                ? highRiskBallUrl
                : risk === Risk.Low
                  ? lowRiskBallUrl
                  : mediumRiskBallUrl,
        ).then((image) => {
            this.#image = image;
        });
    }

    update(dt: number): void {
        this.#previousPosition = { x: this.x, y: this.y };
        super.update(dt);
    }

    render(context: CanvasRenderingContext2D, alpha: number): void {
        if (this.#image === null) {
            return;
        }

        const { x: prevX, y: prevY } = this.#previousPosition;
        const { x, y } = this;

        context.drawImage(
            this.#image,
            lerp(prevX, x, alpha) - IMAGE_OFFSET,
            lerp(prevY, y, alpha) - IMAGE_OFFSET,
            IMAGE_SIZE,
            IMAGE_SIZE,
        );
    }
}

// preload images (the loadImage() will cache them)
Promise.all(
    [lowRiskBallUrl, mediumRiskBallUrl, highRiskBallUrl].map((url) => loadImage(url)),
).catch((error) => logDevError('Failed to load ball images', error));
