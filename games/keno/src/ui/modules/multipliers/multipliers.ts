import {
    defineComponent,
    Flex,
    forwardRef,
    Multiplier,
    Props,
    Reactive,
    type Component,
} from '@monkey-tilt/ui';

export interface MultipliersProps extends Props<HTMLDivElement> {
    /**
     * The items to display in the ticker.
     */
    readonly items: Reactive<ReadonlyArray<[string, string]>>;
}

export const Multipliers = defineComponent(
    'Multipliers',
    ({ state, render }) =>
        ({ items, ...props }: MultipliersProps): Component<HTMLDivElement> => {
            const { onPropChange } = state();

            return Flex({
                ...props,
                className: { 'm-keno__multipliers': true },
                gap: 's',
                ref: forwardRef(props.ref, (element: HTMLDivElement) => {
                    const renderItem = ([multiplier, _chance]: [string, string]) => {
                        return render(
                            Multiplier({
                                value: multiplier,
                                variant: 'keno',
                            }),
                        );
                    };

                    // ToDo: Improve, edit existing elements when possible

                    onPropChange(items, (items) => {
                        element.innerHTML = '';
                        for (const item of items) {
                            const renderedItem = renderItem(item);
                            element.appendChild(renderedItem);
                        }
                    });
                }),
            });
        },
);
