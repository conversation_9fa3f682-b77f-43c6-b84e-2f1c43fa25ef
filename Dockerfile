# syntax=docker/dockerfile:1.7-labs
FROM debian@sha256:27586f4609433f2f49a9157405b473c62c3cb28a581c413393975b4e8496d0ab AS build

ARG GITHUB_TOKEN

RUN set -eux; \
    apt-get update; \
    apt-get install tree git curl unzip ca-certificates -y --no-install-recommends; \
    rm -rf /var/lib/apt/lists/*

WORKDIR /app

COPY .node-version package.json ./devops/build.sh ./

RUN ./build.sh prepare

COPY pnpm-*.yaml .npmrc ./
COPY --parents patches sdk/ packages/*/package.json games/*/package.json ./

RUN echo "//npm.pkg.github.com/:_authToken=$GITHUB_TOKEN" >> .npmrc

RUN ./build.sh install

COPY .eslint.* .prettier* nx.json tsconfig* ./
COPY --parents packages/* games/* ./

RUN ./build.sh build

FROM nginx@sha256:e9293c9bedb0db866e7d2b69e58131db4c2478e6cd216cdd99b134830703983a AS standalone

ARG ALLOWED_ORIGINS
ENV ALLOWED_ORIGINS=${ALLOWED_ORIGINS}

WORKDIR /app

COPY --from=build /app/sdk/dist .
COPY --from=build /app/standalone/*.html ./standalone/
COPY ./devops/nginx-static.conf /etc/nginx/conf.d/default.conf.template
COPY ./devops/generate-nginx-config.sh /app/generate-nginx-config.sh

RUN chmod +x /app/generate-nginx-config.sh

RUN sh /app/generate-nginx-config.sh "$ALLOWED_ORIGINS" /etc/nginx/conf.d/default.conf


FROM nginx@sha256:e9293c9bedb0db866e7d2b69e58131db4c2478e6cd216cdd99b134830703983a AS sdk

ARG ALLOWED_ORIGINS
ENV ALLOWED_ORIGINS=${ALLOWED_ORIGINS}

WORKDIR /app

COPY --from=build /app/sdk/dist .
COPY ./devops/nginx-static.conf /etc/nginx/conf.d/default.conf.template
COPY ./devops/generate-nginx-config.sh /app/generate-nginx-config.sh

RUN chmod +x /app/generate-nginx-config.sh

RUN sh /app/generate-nginx-config.sh "$ALLOWED_ORIGINS" /etc/nginx/conf.d/default.conf

