import type { Meta, StoryObj } from '@storybook/html';
import { Toast, type ToastBarProps, type ToastProps } from './toast';
import { fn } from '@storybook/test';
import { html } from '../../component';
import { Button } from '../button/button';

const meta: Meta = {
    title: 'Modules/Toast',
};
export default meta;

export const Default: StoryObj = (props: ToastProps) => {
    return Toast({
        ...props,
        dismiss:
            (props.dismiss as string) === 'delay'
                ? { after: 5000 }
                : props.dismiss == 'click'
                  ? 'click'
                  : undefined,
    }).render();
};
Default.argTypes = {
    variant: { control: 'select', options: ['default', 'error', 'win', 'lose', 'tie'] },
    content: { control: 'text' },
    label: { control: 'text' },
    dismiss: { control: 'select', options: ['click', 'delay', 'never'] },
    isDismissable: { control: 'boolean' },
};
Default.args = {
    content: 'Insufficient balance.',
    variant: 'error',
    dismiss: 'click',
    label: '',
    isDismissable: true,
    onDismiss: fn(),
};

export const Snackbar: StoryObj = (props: ToastBarProps) => {
    let show: (toast: ToastProps) => void;

    return html('div')(
        { className: { 'l-box': true, 'u-gap--xl': true } },
        Button({
            label: 'Show Toast',
            onClick: () => {
                const variant = (['default', 'error'] as const)[Math.floor(Math.random() * 2)]!;
                show({
                    content: `This is ${variant} toast.`,
                    variant,
                    dismiss: { after: 5000 },
                });
            },
        }),
        Toast.Bar({
            ...props,
            controls({ showToast }) {
                show = showToast;
            },
        }),
    ).render();
};
Snackbar.storyName = 'Toast Bar';
Snackbar.argTypes = {
    align: { control: 'select', options: ['start', 'center', 'end'] },
    animate: { control: 'select', options: ['default', 'fade'] },
};
Snackbar.args = {
    align: 'end',
    animate: 'default',
};
