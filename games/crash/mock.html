<!doctype html>
<html lang="en">

<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Monkey Tilt Crash (Mock Mode)</title>
    <style>
        html,
        body {
            margin: 0;
            height: 100%;
            background: #0a0a0a;
        }
        
        .mock-banner {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            background: #ff6b35;
            color: white;
            text-align: center;
            padding: 8px;
            font-family: monospace;
            font-weight: bold;
            z-index: 9999;
            font-size: 14px;
        }
        
        #app {
            margin-top: 40px;
            height: calc(100vh - 40px);
        }
    </style>
</head>

<body>
    <div class="mock-banner">
        🎭 MOCK MODE - Using simulated data for development
    </div>
    <div id="app"></div>
    <script type="module">
        import '@monkey-tilt/ui/style.scss';
        import { run } from './src/main.ts';

        let context;

        globalThis.runGame = () => {
            if (context) {
                console.log('Unmounting previous game');
                context.unmount();
            }

            run({
                container: '#app',
                gameId: 'crash-007',
                gatewayUrl: 'http://localhost:3000/api', // Mock gateway (not used)
                currency: 'USD',
                gameSessionId: 'mock-session-' + Date.now(),
                useMockData: true, // Force mock mode
            }).then((gameContext) => {
                console.log('🎭 Crash game mounted in mock mode', gameContext);
                context = gameContext;
            }).catch((error) => {
                console.error('Failed to mount crash game:', error);
            });
        };

        runGame();
    </script>
</body>

</html> 