@use '../../styles/common/bem';
@use '../../styles/common/prop';
@use '../../styles/common/type-style' as *;
@use '../../styles/common/util';

$-ns: &#{bem.block-selector(module, 'tabs')};
$-gap: util.to-rem(4px);

@mixin styles {
    #{$-ns} {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(0, 1fr));
        position: relative;
        gap: $-gap;
        padding: $-gap;
        background: linear-gradient(135deg, #373737 0%, #171717 100%);
        color: prop.get(color-white);
        border: 1px solid prop.get(color-zinc-800);
        border-radius: 999em;

        &::before {
            display: block;
            content: '';
            position: absolute;
            inset: $-gap auto $-gap
                calc(prop.get(active-tab, 0) * (prop.get(tab-pill-size) + $-gap) + $-gap);
            background: prop.get(color-mt-950);
            border-radius: 999em;
            z-index: 0;
            inline-size: prop.get(tab-pill-size);
            transition: left 0.2s ease;

            @include prop.set(
                tab-pill-size,
                calc((100% - $-gap * (prop.get(tab-count, 0) + 1)) / prop.get(tab-count, 0))
            );
        }

        &#{bem.namespaced-selector('is-disabled')} {
            opacity: 0.8;
            pointer-events: none;

            &::before {
                background: prop.get(color-zinc-700);
            }
        }

        &__tab {
            position: relative;
            border: none;
            background: none;
            padding: prop.get(size-xs-s) prop.get(size-m);
            border-radius: 999em;
            z-index: 1;
            white-space: nowrap;
            text-align: center;
            transition: color 0.4s ease;

            @include type-style('c2');

            &[aria-selected='true'] {
                color: prop.get(color-mt-400);
            }
            &:disabled {
                color: prop.get(color-zinc-300);
            }
        }

        &__label {
            position: relative;
            z-index: 1;
        }

        &--subtle {
            background: none;
            border: none;
            gap: 0;
            padding: prop.get(size-xs) 0;
            margin-block-end: prop.get(size-xs);

            &::before {
                background: prop.get(color-white);
                inset-block-start: auto;
                block-size: 0.25em;
                border-radius: 999em 999em 0 0;
            }

            #{$-ns}__tab {
                color: prop.get(color-zinc-400);
                padding-block: prop.get(size-s) prop.get(size-m);

                &[aria-selected='true'] {
                    color: prop.get(color-white);
                }

                &:focus-visible {
                    outline: none;
                    color: prop.get(color-mt-400);
                }
            }
        }
    }
}
