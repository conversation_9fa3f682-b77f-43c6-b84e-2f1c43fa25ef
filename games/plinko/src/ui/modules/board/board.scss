@use 'sass:math';
@use '@monkey-tilt/ui/scss/common/bem';
@use '@monkey-tilt/ui/scss/common/prop';
@use '@monkey-tilt/ui/scss/common/respond';
@use '@monkey-tilt/ui/scss/common/util';
@use '@monkey-tilt/ui/scss/config/metrics';

@mixin styles {
    @include bem.module('board') {
        position: relative;
        max-block-size: 85vh;
        max-inline-size: 100cqmin;
        display: grid;
        margin: 0 auto;
        grid-template-columns: repeat(calc(prop.get(columns) * 2), 1fr);
        grid-template-rows: repeat(calc(prop.get(rows) + 1), 1fr);
        aspect-ratio: prop.get(columns) / calc(prop.get(rows) + 1);

        @include prop.set(columns, calc(prop.get(rows) + 2));

        &__canvas {
            overflow: auto;
            grid-column: 1 / -1;
            grid-row: 1 / -2;
        }

        &__buckets {
            position: relative;
            transform: translateY(30%);
            grid-column: 2 / -2;
            align-self: start;
        }

        &__stats {
            flex-wrap: nowrap;

            @include respond.until('900px') {
                flex-direction: column;
                align-items: stretch;
            }
        }
    }
}
