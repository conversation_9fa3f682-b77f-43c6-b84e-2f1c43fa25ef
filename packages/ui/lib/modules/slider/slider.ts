import { create, defineComponent, html, type Props } from '../../component';
import type { Reactive } from '../../state';
import { mergeClasses, namespaced, withNamespace } from '../../utilities/classes';

export interface SliderProps extends Omit<Props<HTMLDivElement>, 'onChange'> {
     /**
     * The current value of the slider.
     */
    readonly value: Reactive<number>;
     /**
     * The minimum allowed value of the slider. Defaults to 0.
     */
    readonly min?: Reactive<number>;
     /**
     * The maximum allowed value of the slider. Defaults to 100.
     */
    readonly max?: Reactive<number>;
    /**
     * The step interval for the slider adjustments. Defaults to 0.1.
     */
    readonly step?: number;
    /**
     * Callback function that is called when the slider value changes.
     *
     * @param value The new value of the slider.
     */
    readonly onChange?: (value: number) => void;
     /**
     * Toggle the slider between "over" and "under". Defaults to false.
     */
    readonly rollOver?: Reactive<boolean>;
     /**
     * The number of decimal places to display in the tooltip. Defaults to 2.
     */
    readonly fractionalDigits?: number;
    /**
     * The number of labels to display on the slider. Defaults to 5.
     */
    readonly totalLabels?: number;
     /**
     * The number to start the labels from. Defaults to `min`.
     */
    readonly labelFrom?: number;
    /**
     * The number to end the labels at. Defaults to `max`.
     */
    readonly labelTo?: number;
      /**
     * Visual variant of the slider. Defaults to 'default'.
     */
    readonly variant?: 'default' | 'plinko';
     /**
     * Whether the slider is disabled. Defaults to false.
     */
    readonly disabled?: Reactive<boolean>;
}

export const Slider = defineComponent(
    'Slider',
    ({ state }) =>
        ({
            value,
            min = 0,
            max = 100,
            step = 0.1,
            onChange,
            rollOver = false,
            fractionalDigits = 2,
            totalLabels = 5,
            labelFrom,
            labelTo,
            variant = 'default',
            className,
            disabled = false,
            ...props
        }: SliderProps): HTMLDivElement => {
            const { onPropChange, read, signal, effect, untracked } = state();

            labelFrom ??= read(min);
            labelTo ??= read(max);

            let sliderRef: HTMLDivElement | null = null;
            let tooltipRef: HTMLDivElement | null = null;
            let inputRef: HTMLInputElement | null = null;

            const position = signal(read(value));
            const sliderPercentageVar = `--${withNamespace('slider-percentage')}`;
            const plinkoPercentageVar = '--m-slider__plinko-percentage';
            const tooltipPercentVar = '--slider-tooltip-percent';

            const updateTrackPosition = (val: number) => {
                if (!sliderRef) return;

                const minVal = read(min);
                const maxVal = read(max);
                const clamped = Math.max(minVal, Math.min(maxVal, val));
                const percent = ((clamped - minVal) / (maxVal - minVal)) * 100;

                if (variant === 'plinko') {
                    sliderRef.style.setProperty(plinkoPercentageVar, `${percent}%`);
                    sliderRef.style.setProperty('--slider-tooltip-percent', `${percent}`);
                    if (tooltipRef) {
                        tooltipRef.dataset.value = String(Math.round(clamped));
                    }
                } else {
                    sliderRef.style.setProperty(sliderPercentageVar, `${percent}`);
                    if (tooltipRef) {
                        tooltipRef.dataset.value = clamped.toFixed(fractionalDigits);
                    }
                }

                if (tooltipRef) {
                    tooltipRef.dataset.value =
                        variant === 'plinko'
                            ? String(Math.round(clamped))
                            : clamped.toFixed(fractionalDigits);

                    tooltipRef.style.setProperty(tooltipPercentVar, `${percent}`);
                }
            };

            effect(() => {
                const newPosition = Math.max(read(min), Math.min(read(max), position.read()));
                updateTrackPosition(newPosition);

                if (inputRef) {
                    inputRef.value = String(newPosition);
                }

                untracked(() => {
                    if (onChange && read(value) !== newPosition) {
                        onChange(newPosition);
                    }
                });
            });

            onPropChange(value, position.update);

            return create(
                'div',
                {
                    props: {
                        ...props,
                        className: mergeClasses(
                            className,
                            namespaced('m-slider'),
                            variant && withNamespace(`m-slider--${variant}`),
                        ),
                        ref: (element: HTMLDivElement) => {
                            sliderRef = element;
                            updateTrackPosition(position.read());

                            const rollOverClass = withNamespace('m-slider--roll-over');
                            onPropChange(rollOver, (rollOver) => {
                                element.classList.toggle(rollOverClass, rollOver);
                            });
                        },
                    },
                },

                html('div')({ className: namespaced('m-slider__background') }),
                html('div')({ className: namespaced('m-slider__track') }),
                html('div')({ className: namespaced('m-slider__progress') }),

                html('div')({
                    className: namespaced('m-slider__tooltip'),
                    ref: (element: HTMLDivElement) => {
                        tooltipRef = element;
                    },
                }),

                html('input')({
                    type: 'range',
                    min: String(read(min)),
                    max: String(read(max)),
                    step: String(step),
                    value: String(read(value)),
                    className: namespaced('m-slider__input'),
                    ref: (element: HTMLInputElement) => {
                        inputRef = element;

                        onPropChange(min, max, (min, max) => {
                            element.min = String(min);
                            element.max = String(max);
                        });

                        onPropChange(disabled, (disabled) => {
                            element.disabled = disabled;
                        });
                    },
                    onInput: (event: Event) => {
                        if (event.target instanceof HTMLInputElement) {
                            position.update(Number(event.target.value));
                        }
                    },
                    onMouseup: (event: Event) => {
                        if (event.target instanceof HTMLInputElement) {
                            event.target.blur();
                        }
                    },
                }),

                totalLabels > 1 &&
                    html('div')({
                        className: namespaced('m-slider__labels'),
                        children: Array.from({ length: totalLabels }).map((_, index) =>
                            html('div')({
                                className: namespaced('m-slider__label'),
                                'data-label': String(
                                    Math.round(
                                        labelFrom +
                                            ((labelTo - labelFrom) / (totalLabels - 1)) * index,
                                    ),
                                ),
                            }),
                        ),
                    }),
            );
        },
);