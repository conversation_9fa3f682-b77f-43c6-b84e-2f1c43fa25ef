import games from './games.json';

import * as client from '@monkey-tilt/client';
import * as state from '@monkey-tilt/state';
import * as ui from '@monkey-tilt/ui';
import * as utils from '@monkey-tilt/utils';

import '@monkey-tilt/ui/style.scss';

export type UnmountFn = () => void;
type RunFn = (options: Record<string, unknown>) => GameContext | Promise<GameContext>;

export interface CurrencyChanged {
    readonly type: 'CURRENCY_CHANGED';
    readonly gameSessionId: string;
    readonly currency: string;
}

export interface SetCurrencyChangeAllowed {
    readonly type: 'SET_CURRENCY_CHANGE_ALLOWED';
    readonly isAllowed: boolean;
}

export type Notification = CurrencyChanged;
export type Action = SetCurrencyChangeAllowed;

export interface GameContext extends UnmountFn {
    readonly unmount: UnmountFn;
    readonly onAction: (callback: (action: Action) => void) => void;
    readonly notify: (notification: Notification) => void;
}

declare global {
    interface Window {
        MT_client: typeof client;
        MT_state: typeof state;
        MT_ui: typeof ui;
        MT_utils: typeof utils;
        MT_inspect: (...args: unknown[]) => void;
        [key: string]: unknown;
    }
}

window.MT_client = client;
window.MT_state = state;
window.MT_ui = ui;
window.MT_utils = utils;
window.MT_inspect = window.MT_inspect || (() => {});

const baseUrl = new URL(
    globalThis && globalThis.document.currentScript instanceof HTMLScriptElement
        ? globalThis.document.currentScript.src
        : location.href,
);

const link = document.createElement('link');
link.rel = 'stylesheet';
link.href = new URL('./style.css', baseUrl).href;
document.head.appendChild(link);

const loadedGames = new Map<string, Promise<RunFn>>();
const mountedGames = new Map<HTMLElement | string, GameContext>();

export function run(options: {
    readonly gameId: keyof typeof games;
    readonly container: HTMLElement | string;
    readonly [key: string]: unknown;
}): Promise<GameContext> {
    const { gameId } = options;

    if (!loadedGames.has(gameId)) {
        window.MT_inspect('Loading game:', gameId);
        loadedGames.set(
            gameId,
            new Promise((resolve, reject) => {
                const { src, namespace } = games[gameId];
                const script = document.createElement('script');
                script.src = new URL(src, baseUrl).href;
                script.onload = () => {
                    const game = window[namespace] as { run?: RunFn } | undefined;
                    if (game && typeof game.run == 'function') {
                        window.MT_inspect(`Game "${gameId}" loaded successfully.`);
                        resolve(game.run);
                    } else {
                        window.MT_inspect(
                            `Game "${gameId}" does not export ${namespace}.run() function`,
                        );
                        reject(
                            new Error(
                                `Game "${gameId}" does not export ${namespace}.run() function`,
                            ),
                        );
                    }
                };
                script.onerror = (event) => {
                    const error = event instanceof ErrorEvent ? event.error : String(event);
                    window.MT_inspect(`Error loading game "${gameId}":`, error);
                    reject(error);
                };
                document.body.appendChild(script);
            }),
        );
    }

    const { container } = options;

    return loadedGames
        .get(gameId)!
        .then((run) => {
            if (mountedGames.has(container)) {
                window.MT_inspect(`Unmounting existing game ${gameId}`, { container });
                mountedGames.get(container)!.unmount();
            }
            window.MT_inspect(`Mounting game ${gameId}`, { options, container });
            return run(options);
        })
        .then(({ unmount: unmountGame, onAction, notify }) => {
            const instance = {
                unmount: unmountGame,
                onAction,
                notify,
            };
            window.MT_inspect(`Game ${gameId} mounted successfully`, {
                options,
                instance,
            });
            let isMounted = true;

            const unmount = () => {
                if (isMounted) {
                    window.MT_inspect(`Unmounting game ${gameId} [explicit]`, {
                        options,
                        container,
                        instance,
                    });
                    isMounted = false;
                    mountedGames.delete(container);
                    unmountGame();
                }
            };

            const context: GameContext = Object.assign(unmount, {
                unmount,
                onAction,
                notify,
            });

            mountedGames.set(container, context);

            return context;
        });
}
