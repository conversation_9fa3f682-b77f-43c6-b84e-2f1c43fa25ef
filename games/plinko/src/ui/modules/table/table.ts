import {
    Badge,
    defineComponent,
    forwardRef,
    Frame,
    getBucketColor,
    html,
    Toast,
    withNamespace,
    Stack,
    type BadgeTickerItem,
    type Component,
    type Props,
    type ToastBarControls,
} from '@monkey-tilt/ui';
import { type PlinkoUI } from '../../plinko';
import { Balance } from '../balance/balance';
import { Loader } from '../loader/loader';
import { Board } from '../board/board';

export interface TableProps extends Props<HTMLDivElement> {
    /**
     * The Plinko UI state manager.
     */
    readonly ui: PlinkoUI;
}

export const Table = defineComponent(
    'Table',
    ({ state }) =>
        ({ ui, ...props }: TableProps): Component<HTMLDivElement> => {
            const { signal, effect, memo } = state();

            const [isLoading, setIsLoading] = signal(ui.gameState().isLoading);
            effect(() => {
                let timer: ReturnType<typeof setTimeout> | undefined;
                const newIsLoading = ui.gameState().isLoading;

                if (newIsLoading) {
                    timer = setTimeout(() => {
                        setIsLoading(true);
                    }, 1500);
                } else {
                    setIsLoading(false);
                }

                return () => {
                    if (timer) {
                        clearTimeout(timer);
                    }
                };
            });

            const badges = memo(() =>
                ui.history
                    .read()
                    .map<BadgeTickerItem>(({ id, risk, number_of_rows, bucketNumber }) => {
                        return {
                            id,
                            text: `${Number(ui.getMultiplier(number_of_rows, risk, bucketNumber - 1))}x`,
                            color: getBucketColor(bucketNumber - 1, number_of_rows + 1),
                            pill: true,
                        };
                    }),
            );

            return Frame(
                {
                    ...props,
                    ref: forwardRef(props.ref, (element: HTMLDivElement) => {
                        effect(() => {
                            element.classList.toggle(withNamespace('is-loading'), isLoading());
                        });
                    }),
                    className: { 'm-plinko__table': true },
                },

                Loader({
                    isLoading,
                    text: [
                        { text: 'Loading, please wait...', delay: 1000 },
                        { text: 'This is taking longer than expected...', delay: 5000 },
                        { text: 'Please check your internet connection!', delay: 8000 },
                    ],
                    ref: (element: HTMLDivElement) => {
                        effect(() => {
                            element.classList.toggle(withNamespace('u-hide'), !isLoading());
                        });
                    },
                }),
                Balance({ ui }),
                Stack(
                    { gap: 'l' },
                    Board({
                        ui,
                        onBoardIdle: () => {
                            ui.isBoardIdle.update(true);
                        },
                        onBucketHit: (ballData) => {
                            ui.history.update((prev) => [...prev, ballData].slice(-5));
                            const payout = Number.parseFloat(ballData.payout);
                            if (payout > 0) {
                                ui.incrementBalance(payout);
                            }
                        },
                    }),
                    html('div')(
                        {
                            className: { 'm-plinko__ticker': true },
                        },
                        Badge.Ticker({
                            delayMs: memo(() => (ui.config.turboMode.read() ? 20 : 200)),
                            items: badges,
                            limit: 5,
                            orientation: 'vertical',
                        }),
                    ),
                ),

                Toast.Bar({
                    position: 'absolute',
                    controls: (toastControls: ToastBarControls) => {
                        ui.toastBarControlsRef.current = toastControls;
                    },
                }),
                Toast.Bar({
                    position: 'absolute',
                    align: 'center',
                    css: {
                        bottom: '20%',
                    },
                    controls: (toastControls: ToastBarControls) => {
                        ui.notificationsControlsRef.current = toastControls;
                    },
                }),
            );
        },
);
