import type { BetAmountLimits, <PERSON>Action<PERSON>ame, GameError, ReadyState } from '@monkey-tilt/client';

interface SuitMap {
    Hearts: 0;
    Diamonds: 1;
    Clubs: 2;
    Spades: 3;
}
export type Suit = SuitMap[keyof SuitMap];

export interface FinalState {
    readonly banker_hand: Hand;
    readonly player_hand: Hand;
}

export interface CardInfo {
    readonly value: 1 | 2 | 3 | 4 | 5 | 6 | 7 | 8 | 9 | 10;
    readonly suit: Suit;
    readonly index: 0 | 1 | 2 | 3 | 4 | 5 | 6 | 7 | 8 | 9 | 10 | 11 | 12;
}

export interface Card extends CardInfo {
    /**
     * Unique identifier for the card, FE-only
     */
    readonly id: string;
}

export interface HandInfo {
    readonly cards: Card[];
    readonly hand_value: number;
}

export interface Hand extends HandInfo {
    /**
     * Status of the hand, FE-only
     */
    readonly status: 'lose' | 'win' | 'tie' | 'dealing';

    /**
     * Unique identifier for the hand, FE-only
     */
    readonly id: string;
}

export interface BaccaratBetAmountLimits extends BetAmountLimits<'dragon_7' | 'panda'> {
    readonly bet: {
        readonly min: number;
        readonly max: number;
        readonly max_payout: number;
    };
    readonly dragon_7: {
        readonly min: number;
        readonly max: number;
        readonly max_payout: number;
    };
    readonly panda: {
        readonly min: number;
        readonly max: number;
        readonly max_payout: number;
    };
}

export const BaccaratBetAmounts = [
    'player_bet_amount',
    'banker_bet_amount',
    'tie_bet_amount',
    'dragon_7_bet_amount',
    'panda_bet_amount',
] as const;
export type BaccaratBetAmount = (typeof BaccaratBetAmounts)[number];

export const BaccaratSidebets = ['dragon7_sidebet', 'panda_sidebet'] as const;
export type BaccaratSidebet = (typeof BaccaratSidebets)[number];

export const BaccaratMainBets = ['player', 'banker', 'tie'] as const;
export type BaccaratMainBet = (typeof BaccaratMainBets)[number];

export const BaccaratBets = [...BaccaratMainBets, ...BaccaratSidebets] as const;
export type BaccaratBet = (typeof BaccaratBets)[number];

export interface SidebetStatus {
    readonly payout: string;
    readonly status: 'sidebet_won' | 'sidebet_lost';
}

export interface AutobetState {
    readonly autobet_limit: number;
    readonly autobet_count: number;
    readonly on_win_reset: boolean;
    readonly on_win: string;
    readonly on_loss_reset: boolean;
    readonly on_loss: string;
    readonly stop_on_profit: string;
    readonly stop_on_loss: string;
    readonly autobet_cumulative_payout: string;
}

export interface BaccaratState
    extends Partial<Record<BaccaratBetAmount, string | undefined>>,
        Partial<Record<BaccaratSidebet, SidebetStatus | undefined>>,
        Partial<AutobetState> {
    readonly action?: GameActionName;

    readonly balance: number;
    readonly bet_limits: BaccaratBetAmountLimits;

    readonly bet_odds: Record<BaccaratBet, number>;

    readonly error: GameError | null;
    readonly isLoading: boolean;
    readonly readyState: ReadyState;
    readonly isAuthenticated: boolean;

    readonly round_id: string;

    readonly banker_hand: Hand;
    readonly player_hand: Hand;

    readonly next_actions: ReadonlyArray<GameActionName>;

    readonly isAutobetActive: boolean;

    readonly round_closed?: boolean;
    readonly total_payout?: string;
}

export const ActionsExcludedFromSync = ['Autobet', 'CloseAutobet', 'Nextbet'];
