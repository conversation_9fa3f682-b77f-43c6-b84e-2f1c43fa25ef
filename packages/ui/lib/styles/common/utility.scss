@use 'sass:meta';
@use './bem';
@use './respond';

///
/// Generates a utility modifier classes including their responsive variations.
///
/// @param {string} $block - The block name.
/// @param {list} $modifiers - List of modifier names.
/// @param {mixin} $mixin - The mixin to apply to the modifier.
///
@mixin modifiers($block, $modifiers, $mixin) {
    @each $modifier in $modifiers {
        @include bem.modifier($modifier, $block) {
            @include meta.apply($mixin, $modifier);
        }
    }

    @each $breakpoint in respond.breakpoint-names() {
        @each $modifier in $modifiers {
            @include respond.until($breakpoint) {
                @include bem.modifier('#{$modifier}\\@#{$breakpoint}', $block) {
                    @include meta.apply($mixin, $modifier);
                }
            }
        }
    }
}
