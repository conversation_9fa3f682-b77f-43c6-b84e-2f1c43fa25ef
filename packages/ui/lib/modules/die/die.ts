import { create, defineComponent, html, type Props } from '../../component';
import type { Reactive } from '../../state';
import { mergeClasses, withNamespace } from '../../utilities/classes';

export interface DieProps extends Props<HTMLDivElement> {
    /**
     * The number to display on the die.
     */
    readonly number: Reactive<string>;
    /**
     * The status of the die, 'win', 'loss' or 'neutral'.
     */
    readonly status?: Reactive<'win' | 'loss' | 'neutral' | undefined>;
}

export const Die = defineComponent(
    'Die',
    ({ state }) =>
        ({ number, status, className, ...props }: DieProps): HTMLElement => {
            const { onPropChange } = state();

            return create(
                'div',
                {
                    props: {
                        role: 'img',
                        ...props,
                        className: mergeClasses(className, { 'm-die': true }),
                        ref: (element) => {
                            onPropChange(number, (number) => {
                                element.ariaLabel = `Die showing ${number}`;
                            });

                            const winClassName = withNamespace('m-die--win');
                            const lossClassName = withNamespace('m-die--loss');

                            onPropChange(status, (status = 'neutral') => {
                                element.classList.toggle(winClassName, status === 'win');
                                element.classList.toggle(lossClassName, status === 'loss');
                            });
                        },
                    },
                },
                html('div')({
                    className: withNamespace('m-die__shape'),
                }),
                html('span')({
                    className: withNamespace('m-die__number'),
                    ref: (element) => {
                        onPropChange(number, (number) => {
                            element.textContent = number;
                        });
                    },
                }),
            );
        },
);
