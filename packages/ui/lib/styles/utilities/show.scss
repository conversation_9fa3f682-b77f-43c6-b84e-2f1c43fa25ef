@use '../common/bem';
@use '../common/respond';

@mixin styles {
    $display-values: inline, flex, inline-flex, grid;

    @include bem.utility('show') {
        display: block !important;

        @each $display in $display-values {
            @include bem.modifier($display) {
                display: $display !important;
            }
        }

        @each $breakpoint in respond.breakpoint-names() {
            &\@#{$breakpoint} {
                display: none !important;
            }

            @each $display in $display-values {
                @include bem.modifier('#{$display}\\@#{$breakpoint}') {
                    display: none !important;
                }
            }
        }

        @each $breakpoint in respond.breakpoint-names() {
            @include respond.until($breakpoint) {
                &\@#{$breakpoint} {
                    display: block !important;
                }
            }
            @each $display in $display-values {
                @include respond.until($breakpoint) {
                    @include bem.modifier('#{$display}\\@#{$breakpoint}') {
                        display: $display !important;
                    }
                }
            }
        }
    }
}
