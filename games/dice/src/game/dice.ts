import {
    APIClient,
    createWebSocketTransport,
    currencyToFixedString,
    GameClient,
    GameError,
    isGameUpdate,
    merge,
    pick,
    RetryError,
    validateObject,
    type AnyState,
    type BetAmountLimits,
    type GameActionName,
    type GameState,
    type MessageName,
    type RequestData,
    type VerifySeedParams,
    type Writable,
} from '@monkey-tilt/client';
import type { Root, SignalReader, SignalUpdater } from '@monkey-tilt/ui';
import { deepMerge, logDevError, RingBuffer } from '@monkey-tilt/utils';
import {
    calculateParams,
    DEFAULT_RTP,
    DICE_ROLL_PRECISION,
    INITIAL_MULTIPLIER,
    MULTIPLIER_PRECISION,
} from '../util/chance';
import {
    type AutobetState,
    type DiceBetAmountLimits,
    type DiceState,
    type FinalState,
    type RoundSummary,
} from './state';

export interface DiceClientOptions {
    readonly root: Root;
    readonly gameId: string;
    readonly gatewayUrl: string | URL;
    readonly websocketUrl?: string | URL;
    readonly gameSessionId: string;
    readonly currency: string;
}

interface DiceGameOptions extends DiceClientOptions {
    readonly apiClient: APIClient;
    readonly betAmountLimits: DiceBetAmountLimits;
    readonly rtp: number;
}

export async function createDiceClient(options: DiceClientOptions): Promise<DiceClient> {
    const apiClient = new APIClient(options.gatewayUrl);
    apiClient.gameId = options.gameId;
    apiClient.gameSessionId = options.gameSessionId;

    return new DiceGame({
        ...options,
        apiClient,
        betAmountLimits: deepMerge<BetAmountLimits>(
            {
                bet: {
                    min: 0,
                    max: 5000,
                    max_payout: 100_000,
                },
            },
            await apiClient.getBetLimits(),
        ) as DiceBetAmountLimits,
        rtp: (await apiClient.getRTP()) ?? DEFAULT_RTP,
    });
}

export interface DiceClient extends GameClient<DiceState> {
    readonly state: SignalReader<DiceState>;
    onStateReload(handler: () => void): void;
    bet(request: RequestData<'Bet'>): Promise<void>;
    autobet(request: RequestData<'Autobet'>): Promise<void>;
    nextbet(): Promise<void>;
    closeAutobet(): Promise<void>;
    setBetState(
        amounts: Partial<Pick<DiceState, 'bet_amount' | 'multiplier' | 'roll_over' | 'dice_roll'>>,
    ): void;
    setAutobetState(
        autobet: Partial<
            AutobetState & Pick<DiceState, 'bet_amount' | 'multiplier' | 'roll_over' | 'dice_roll'>
        >,
    ): void;
    reload(): Promise<GameState>;
    verifySeeds(params: VerifySeedParams): Promise<FinalState>;
}

class DiceGame extends GameClient<DiceState> implements DiceClient {
    #getState: SignalReader<DiceState>;
    #setState: SignalUpdater<DiceState>;
    #history: RingBuffer<RoundSummary>;

    #stateReloadHandlers = new Set<() => void>();

    #updateBalance: () => Promise<void>;
    #updateBetLimits: () => Promise<void>;
    #updateRTP: () => Promise<void>;

    public constructor({
        root,
        gameId,
        gatewayUrl,
        websocketUrl,
        gameSessionId,
        currency,
        apiClient,
        betAmountLimits,
        rtp,
    }: DiceGameOptions) {
        super({
            root,
            gameId,
            gameSessionId,
            currency,
            gatewayUrl,
            apiClient,
            transport: createWebSocketTransport({
                gameSessionId,
                gatewayUrl,
                websocketUrl,
            }),
            syncableActions: () => false,
            actionTimeout: 2000,
            autoRetryAttempts: 5,
        });

        const { signal, effect, memo } = this.root.store;

        const initialRollOver = true;
        const initialParams = calculateParams(
            { multiplier: INITIAL_MULTIPLIER },
            { rtp, roll_over: initialRollOver },
        );

        [this.#getState, this.#setState] = signal<DiceState>({
            rtp,
            history: [],
            balance: 0,
            bet_limits: betAmountLimits,
            error: null,
            isLoading: true,
            readyState: 'closed',
            isAuthenticated: false,
            round_id: '',
            on_loss_reset: true,
            on_win_reset: true,
            autobet_limit: 0,
            bet_amount: '0.00',
            roll_over: initialRollOver,
            multiplier: initialParams.multiplier.toFixed(MULTIPLIER_PRECISION),
            dice_roll: initialParams.dice_roll.toFixed(DICE_ROLL_PRECISION),
            next_actions: [],
            isAutobetActive: false,
        });

        this.#history = new RingBuffer<RoundSummary>(13);

        this.#updateBalance = this.#createUpdater('balance', () =>
            this.api.getBalance().then(({ balance }) => balance),
        );
        this.#updateRTP = this.#createUpdater('rtp', async () => {
            const rtp = await this.api.getRTP();
            return (previousRTP) => rtp ?? previousRTP;
        });

        const usdRate = memo(() => this.session().usdRate);
        const usdBetLimits = signal(betAmountLimits);

        this.#updateBetLimits = async () => {
            const limits = await this.api.getBetLimits();
            usdBetLimits.update(
                (prevLimits) =>
                    deepMerge<BetAmountLimits>(prevLimits, limits) as DiceBetAmountLimits,
            );
        };

        effect(() => {
            const limits = usdBetLimits.read();
            const factor = usdRate();
            this.#setState((state) => ({
                ...state,
                bet_limits: {
                    bet: {
                        min: limits.bet.min * factor,
                        max: limits.bet.max * factor,
                        max_payout: limits.bet.max_payout * factor,
                    },
                },
            }));
        });

        const prepareNextRound = () => {
            this.#updateBalance().catch(logDevError);
            this.#updateBetLimits().catch(logDevError);
            this.#updateRTP().catch(logDevError);
        };

        prepareNextRound();
        this.on('roundEnd', prepareNextRound);

        this.on('readyStateChanged', (readyState) => {
            this.#setState((state) => ({
                ...state,
                isLoading: !(state.isLoading && readyState === 'ready'),
                readyState,
                isAuthenticated: this.isAuthenticated,
            }));
        });

        this.on('error', (event) => {
            let error: unknown = event.error;

            if (error instanceof RetryError) {
                error = error.reason;
            }

            if (error instanceof GameError) {
                this.#setState((state) => {
                    return {
                        ...state,
                        next_actions:
                            error.code === 'INSUFFICIENT_FUNDS'
                                ? this.roundStartActions
                                : state.next_actions,
                        error,
                    };
                });
            }
        });
    }

    public override close(): void {
        this.root.unmount();
        super.close();
    }

    protected updateState(value: DiceState | ((prevValue: DiceState) => DiceState)): void {
        return this.#setState(value);
    }

    public get state(): SignalReader<DiceState> {
        return this.#getState;
    }

    public onStateReload(handler: () => void): void {
        this.#stateReloadHandlers.add(handler);
    }

    public override can<A extends MessageName>(action: A): boolean {
        if (super.can(action)) {
            return true;
        }
        return (
            this.roundStartActions.includes(action as GameActionName) &&
            this.#getState().next_actions.length === 0
        );
    }

    #validateBetAmount(request: RequestData<'Bet'>): { balance: number; betAmount: number } {
        if (!request.multiplier) {
            throw new GameError('Multiplier is required', 'MULTIPLIER_REQUIRED', '');
        }

        const state = this.#getState();

        const betAmount = request.bet_amount ?? 0;
        const balance = state.balance - betAmount;

        if (balance < 0) {
            throw new GameError('Insufficient funds', 'INSUFFICIENT_FUNDS', '');
        }

        const maxBetAmount = Math.min(
            state.bet_limits.bet.max,
            state.bet_limits.bet.max_payout / request.multiplier,
        );

        if (betAmount > maxBetAmount) {
            throw new GameError('Bet amount exceeds maximum limit', 'BET_AMOUNT_EXCEEDS_LIMIT', '');
        }

        return { balance, betAmount };
    }

    public async bet(request: RequestData<'Bet'>): Promise<void> {
        if (this.can('Bet')) {
            const { betAmount, balance } = this.#validateBetAmount(request);
            this.#setState((state) => ({
                ...state,
                bet_amount: currencyToFixedString(betAmount, this.session().currency),
                balance,
            }));
        }

        return this.#send(
            'Bet',
            request,
            (error) => !(error instanceof GameError) || error.code !== 'INSUFFICIENT_FUNDS',
        );
    }

    public async autobet(request: RequestData<'Autobet'>): Promise<void> {
        if (this.can('Autobet')) {
            this.removeIntent('CloseAutobet');

            const { betAmount, balance } = this.#validateBetAmount(request);
            this.#setState((state) => ({
                ...state,
                bet_amount: currencyToFixedString(betAmount, this.session().currency),
                balance,
                isAutobetActive: true,
                autobet_limit:
                    request.autobet_limit != undefined && Number.isFinite(request.autobet_limit)
                        ? request.autobet_limit
                        : 0,
                autobet_count: 0,
                on_win_reset: request.on_win_reset ?? true,
                on_win: (request.on_win ?? 0).toFixed(0),
                on_loss_reset: request.on_loss_reset ?? true,
                on_loss: (request.on_loss ?? 0).toFixed(0),
                stop_on_profit: (request.stop_on_profit ?? 0).toFixed(2),
                stop_on_loss: (request.stop_on_loss ?? 0).toFixed(2),
            }));
        }

        return this.#send(
            'Autobet',
            request,
            (error) => !(error instanceof GameError) || error.code !== 'INSUFFICIENT_FUNDS',
        ).catch(() => {
            this.#setState((state) => ({ ...state, isAutobetActive: false }));
        });
    }

    public nextbet(): Promise<void> {
        if (this.hasIntent('CloseAutobet')) {
            return this.closeAutobet();
        }
        if (this.can('Nextbet')) {
            const { bet_amount = '0.00', balance } = this.#getState();

            if (balance < Number(bet_amount)) {
                this.closeAutobet().catch(logDevError);
                return Promise.reject(
                    new GameError('Insufficient funds', 'INSUFFICIENT_FUNDS', ''),
                );
            }

            this.#setState((state) => ({
                ...state,
                balance,
            }));
        }
        return this.#send('Nextbet');
    }

    public closeAutobet(): Promise<void> {
        this.removeIntent('CloseAutobet');
        return this.#send('CloseAutobet');
    }

    public setBetState(
        amounts: Partial<Pick<DiceState, 'bet_amount' | 'multiplier' | 'roll_over' | 'dice_roll'>>,
    ): void {
        this.#setState((state) => ({
            ...state,
            ...amounts,
        }));
    }

    public setAutobetState(
        autobet: Partial<
            AutobetState & Pick<DiceState, 'bet_amount' | 'multiplier' | 'roll_over' | 'dice_roll'>
        >,
    ): void {
        this.#setState((state) => ({
            ...state,
            ...autobet,
        }));
    }

    public override async reload(): Promise<GameState> {
        this.#setState((state) => ({ ...state, error: null }));
        return super.reload();
    }

    public override get roundStartActions(): GameActionName[] {
        return ['Bet', 'Autobet'];
    }

    public override reset(): void {
        super.reset();

        const { isAuthenticated } = this;

        this.#setState((state) => ({
            ...state,
            error: null,
            next_actions: isAuthenticated ? this.roundStartActions : [],
            isAuthenticated,
        }));
    }

    async #send(
        action: GameActionName,
        request: RequestData<GameActionName>,
        shouldRetry?: (error: Error) => boolean,
    ): Promise<void> {
        this.#setState((state) => ({
            ...state,
            error: null,
            next_actions: [],
        }));
        await this.sendAndAwait(action, request, shouldRetry);
    }

    protected override handleUpdate(state: AnyState): void {
        if (state.type === 'Authenticate') {
            this.reset();
            return;
        }

        if (state.type === 'GameState') {
            const resetState = state.data as Writable<DiceState>;

            if (state.round_id) {
                resetState.round_id = state.round_id;
            }

            resetState.bet_amount = (
                resetState.bet_amount !== undefined ? Number.parseFloat(resetState.bet_amount) : 0
            ).toFixed(2);

            this.allowedActions = new Set(resetState.next_actions);

            this.#setState((currentState) => ({
                ...currentState,
                ...resetState,
                round_id: state.round_id ?? currentState.round_id,
                round_closed: resetState.round_closed == true,
                isAutobetActive: resetState.next_actions.includes('CloseAutobet'),
                isAuthenticated: true,
            }));

            if (resetState.round_closed) {
                this.signalRoundEnd();
            }

            void Promise.resolve().then(() => {
                for (const handler of this.#stateReloadHandlers) {
                    try {
                        handler();
                    } catch {
                        // ignore
                    }
                }
            });

            return;
        }

        if (!isGameUpdate(state)) {
            return;
        }

        const newState = structuredClone(this.#getState()) as Writable<DiceState>;
        let isNewRound = false;

        if (state.round_id) {
            isNewRound = state.round_id !== newState.round_id;
            newState.round_id = state.round_id;
        }

        newState.action = state.action;
        newState.isAuthenticated = this.isAuthenticated;
        newState.next_actions = state.data.next_actions;
        this.allowedActions = new Set(newState.next_actions);

        if (isNewRound) {
            this.#setState({
                ...newState,
                round_closed: false,
            });
            // N.B. due to removal of Open step, we have to split state update
            // into two parts to allow UI to properly clean up the previous round
            void Promise.resolve().then(() => {
                this.handleUpdate(state);
            });
            return;
        }

        merge(
            newState,
            pick(
                state.data,
                'random_dice_roll',
                'bet_amount',
                'autobet_count',
                'round_closed',
                'total_payout',
            ),
        );

        if (newState.random_dice_roll) {
            newState.random_dice_roll = Math.min(
                100,
                Math.max(0, Number(newState.random_dice_roll)),
            ).toFixed(DICE_ROLL_PRECISION);
        }

        if (
            (state.action === 'Autobet' || state.action === 'Nextbet') &&
            (newState.autobet_limit ?? 0) > 0
        ) {
            (newState.autobet_limit as number)--;
        }

        if (
            ['Bet', 'Autobet', 'Nextbet'].includes(state.action) &&
            newState.multiplier != undefined &&
            newState.bet_amount != undefined &&
            newState.dice_roll != undefined &&
            newState.random_dice_roll != undefined &&
            newState.roll_over != undefined &&
            newState.total_payout != undefined
        ) {
            const multiplier = Number(newState.multiplier);
            const diceRoll = Number(newState.dice_roll);
            const randomDiceRoll = Number(newState.random_dice_roll);

            this.#history.add({
                time: new Date(),
                round_id: newState.round_id,
                bet_amount: newState.bet_amount,
                multiplier: multiplier.toFixed(MULTIPLIER_PRECISION),
                dice_roll: diceRoll.toFixed(DICE_ROLL_PRECISION),
                roll_over: newState.roll_over,
                random_dice_roll: randomDiceRoll.toFixed(DICE_ROLL_PRECISION),
                total_payout: newState.total_payout,
                outcome:
                    newState.previous_bet_outcome ??
                    ((newState.roll_over && randomDiceRoll > diceRoll) ||
                    (!newState.roll_over && randomDiceRoll < diceRoll)
                        ? 'win'
                        : 'loss'),
            });
            newState.history = Array.from(this.#history);
        }

        if (state.data.round_closed) {
            const total_payout = Number.parseFloat(state.data.total_payout ?? '0.00');

            newState.isAutobetActive = newState.next_actions.includes('CloseAutobet');
            newState.balance += total_payout;
        }

        if (newState.round_closed || newState.isAutobetActive) {
            this.signalRoundEnd();
        }

        this.#setState(newState);
    }

    #createUpdater<T extends keyof DiceState>(
        key: T,
        updater: () => Promise<DiceState[T] | ((previousValue: DiceState[T]) => DiceState[T])>,
    ): () => Promise<void> {
        let isFetching = false;
        return async () => {
            if (isFetching) {
                return;
            }

            isFetching = true;
            try {
                const value = await updater();
                this.#setState((state) => ({
                    ...state,
                    [key]: typeof value === 'function' ? value(state[key]) : value,
                }));
            } catch (error) {
                logDevError(`Failed to fetch ${key}:`, error);
            } finally {
                isFetching = false;
            }
        };
    }

    protected validateSeedVerificationData(data: unknown): boolean {
        return (
            validateObject<FinalState>(data, {
                previous_bet_outcome: 'string',
                random_dice_roll: 'string',
            }) && ['win', 'loss'].includes(data.previous_bet_outcome)
        );
    }

    public async verifySeeds(params: VerifySeedParams): Promise<FinalState> {
        return (await this._validateSeeds(params)) as FinalState;
    }
}
