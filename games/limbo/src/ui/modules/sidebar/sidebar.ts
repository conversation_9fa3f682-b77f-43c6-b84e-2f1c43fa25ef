import { formatCurrency, getCurrencySymbol, BetAmount, Options } from '@monkey-tilt/client';
import {
    Button,
    defineComponent,
    Frame,
    html,
    Icon,
    Label,
    mergeClasses,
    namespaced,
    Stack,
    Tabs,
    withNamespace,
    type Component,
    type Props,
    type TabControls,
} from '@monkey-tilt/ui';
import { possibleWinAmount } from '../../../util/chance';
import { type LimboUI } from '../../limbo';
import { Autobet } from './autobet';

export interface SidebarProps extends Props<HTMLDivElement> {
    /**
     * The Limbo UI state manager.
     */
    readonly ui: LimboUI;
}

export const Sidebar = defineComponent(
    'Sidebar',
    ({ state }) =>
        ({ ui, ...props }: SidebarProps): Component<HTMLDivElement> => {
            const { effect, memo } = state();

            const div = html('div');

            const actionState = memo(() => {
                const {
                    next_actions = [],
                    round_closed = true,
                    readyState = 'closed',
                    isAuthenticated = false,
                    isLoading,
                    isAnimatingState,
                } = ui.gameState();
                return {
                    next_actions,
                    round_closed,
                    canPlay:
                        !isLoading &&
                        !isAnimatingState &&
                        readyState === 'ready' &&
                        isAuthenticated,
                };
            });

            const isAutobetActive = memo(() => ui.gameState().isAutobetActive);

            const limits = memo(() => ui.gameState().bet_limits.bet);

            const betButton = Button({
                cta: true,
                label: memo(() => (isAutobetActive() ? 'Stop' : 'Bet')),
                icon: 'plus',
                className: { 'm-limbo__bet-button': true },
                ref: (element: HTMLButtonElement) => {
                    effect(() => {
                        element.disabled =
                            !ui.isBetAllowed() ||
                            (ui.isBettingLocked() &&
                                (!isAutobetActive() || ui.shouldCloseAutobet()));
                    });
                },
                onClick() {
                    ui.triggerAction('BetOrStop');
                },
            });

            const winAmount = memo(() => {
                const multiplier = Number(ui.multiplier.read());
                const bet_amount = Number(ui.betAmount.read());
                const currency = ui.currency();

                return formatCurrency(possibleWinAmount({ bet_amount, multiplier }), currency, {
                    currencyDisplay: 'none',
                });
            });

            const hideOnMobile = withNamespace('u-hide@m');
            const showOnMobile = withNamespace('u-show@m');

            return Frame(
                { ...props, className: { 'm-limbo__sidebar': true } },

                Frame.Section(
                    {
                        variant: 'subtle',
                        className: { 'm-limbo__tabs': true },
                        ref: (element: HTMLDivElement) => {
                            effect(() => {
                                element.classList.toggle(hideOnMobile, ui.isBettingLocked());
                            });
                        },
                    },
                    Tabs({
                        ariaLabel: 'Bet type',
                        value: ui.activeTab.read(),
                        tabs: [
                            { label: 'Manual', value: 'manual' },
                            { label: 'Auto', value: 'auto' },
                        ],
                        onChange(value: 'manual' | 'auto') {
                            ui.activeTab.update(value);
                        },
                        controls({ selectTab, setEnabled }: TabControls) {
                            effect(() => {
                                selectTab(ui.activeTab.read());
                            });
                            effect(() => {
                                setEnabled(!ui.isBettingLocked());
                            });
                        },
                    }),
                ),

                Frame.Section(
                    {
                        variant: 'subtle',
                        className: mergeClasses(showOnMobile, { 'l-flex': true }),
                    },
                    betButton,
                ),

                Frame.Section(
                    {
                        variant: 'subtle',
                        className: { 'u-gap--l': true, 'm-limbo__controls': true },
                    },

                    Stack(
                        { scroll: true, css: { padding: '4px' } },
                        Stack(
                            { gap: 'xs' },
                            BetAmount({
                                label: 'Bet Amount',
                                value: ui.betAmount,
                                disabled: ui.isBettingLocked,
                                limits,
                                step: 0.01,
                                presets: [0.1, 1.0, 10.0, 100.0],
                                currency: ui.currency,
                                conversionFactor: ui.usdRate,
                                onMultiply: (factor) => {
                                    ui.triggerAction('MultiplyBetAmount', factor);
                                },
                            }),
                            Label({
                                variant: 'error',
                                className: { 'u-hide': true },
                                ref: (element: HTMLSpanElement) => {
                                    const hide = withNamespace('u-hide');
                                    effect(() => {
                                        element.classList.toggle(hide, ui.isBetAllowed());
                                    });
                                },
                                text: memo(() => [
                                    'The maximum bet amount is ',
                                    formatCurrency(ui.maxBetAmount(), ui.currency()) + '.',
                                    html('br')(),
                                    'Reduce target multiplier to increase maximum bet.',
                                ]),
                            }),
                        ),

                        Label.Group({
                            nowrap: true,
                            variant: 'box',
                            text: Label({
                                icon: 'chips',
                                text: 'Win Amount',
                            }),
                            value: html('span')(
                                null,
                                Icon({ currency: memo(() => getCurrencySymbol(ui.currency())) }),
                                ' ',
                                Label({ text: winAmount }),
                            ),
                        }),

                        Autobet({
                            ui,
                            actionState,
                            ref(element: HTMLDivElement) {
                                const hide = withNamespace('u-hide');
                                effect(() => {
                                    element.classList.toggle(hide, ui.activeTab.read() !== 'auto');
                                });
                            },
                        }),
                    ),
                    div({ className: mergeClasses(hideOnMobile, { 'l-flex': true }) }, betButton),
                ),

                Frame.Section(
                    {
                        className: {
                            'l-stack__split': true,
                            'l-stack': true,
                            'u-gap--s': true,
                        },
                    },
                    Options({ ui, namespace: 'limbo' }),
                    html('button')(
                        {
                            onClick(e) {
                                e.preventDefault();
                                ui.provablyFairModalControlsRef.current?.open();
                            },
                            className: { 'u-text--center': true },
                            css: { 'border-width': '0' },
                        },
                        Label(
                            { variant: 'subtle', icon: 'double-check' },
                            html('span')({ className: namespaced('u-hide@m') }, 'This Game is'),
                            'Provably Fair',
                        ),
                    ),
                ),
            );
        },
);
