@use 'sass:map';
@use '../common/prop';

// set of named type styles
$type-styles: (
    body: (
        font-family: Poppins,
        font-weight: semi-bold,
        font-size: (
            prop.get(fs-0),
            prop.get(lh-0),
        ),
    ),
    body2: (
        type-style: body,
        font-weight: normal,
        font-size: (
            prop.get(fs-1),
            prop.get(lh-1),
        ),
    ),
    title: (
        font-family: Rubik,
        font-optical-sizing: auto,
        font-weight: normal,
        font-size: (
            prop.get(fs-2),
            prop.get(lh-2),
        ),
    ),
    alert: (
        type-style: title,
        font-weight: medium,
        font-size: (
            prop.get(fs-1),
            prop.get(lh-1),
        ),
    ),
    alert-xl: (
        type-style: body,
        font-weight: semi-bold,
        text-transform: uppercase,
        font-size: (
            prop.get(fs-10),
            prop.get(lh-10),
        ),
    ),
    heading: (
        type-style: title,
        font-weight: semi-bold,
        font-size: (
            prop.get(fs-5),
            prop.get(lh-5),
        ),
    ),
    c1: (
        type-style: body,
        font-weight: normal,
        font-size: (
            prop.get(fs-1),
            prop.get(lh-1),
        ),
    ),
    c2: (
        type-style: title,
        font-weight: medium,
    ),
    c3: (
        type-style: c2,
        font-size: (
            prop.get(fs-2),
            prop.get(lh-2),
        ),
    ),
    c4: (
        type-style: title,
        text-transform: uppercase,
        font-size: (
            prop.get(fs-2),
            prop.get(lh-2),
        ),
    ),
    caption: (
        type-style: title,
        font-weight: medium,
        font-size: (
            prop.get(fs-caption),
            prop.get(lh-caption),
        ),
    ),
    cta: (
        type-style: title,
        font-weight: bold,
        letter-spacing: 5%,
        text-transform: uppercase,
        font-size: (
            prop.get(fs-3),
            prop.get(lh-3),
        ),
    ),
    caps: (
        type-style: body,
        font-weight: normal,
        text-transform: uppercase,
        font-size: (
            prop.get(fs-caps),
            prop.get(lh-caps),
        ),
    ),
    multiplier: (
        font-family: Staatliches,
        font-weight: normal,
        text-transform: uppercase,
        font-size: (
            prop.get(fs-multiplier),
            prop.get(lh-multiplier),
        ),
    ),
);

// names of all defined type styles
$type-style-names: map.keys($type-styles);
